<?php

namespace app\admin\controller;

use app\model\ShortcutContents;
use support\Log;
use support\Request;
use think\db\exception\DbException;

class ShortcutContentController extends base
{
    public function list(Request $request)
    {
        try {
            $status = $request->get('status', null);
            $query = ShortcutContents::order(['sort' => 'desc', 'id' => 'desc']);
            if (!empty($request->get('content'))) {
                $query->where('content', 'like', '%' . $request->get('content') . '%');
            }
            if ($status !== null && $status >= 0) {
                $query->where('status', $status);
            }
            $list = $query->paginate($request->get('limit', 10));
        } catch (DbException $e) {
            Log::error($e->getMessage());
            return $this->error(500, "系统查询错误");
        }
        return $this->success($list, null);
    }

    public function add(Request $request)
    {
        $post = $request->post();
        if (empty($post)) return $this->error(500, "参数错误");
        try {
            ShortcutContents::create($post);
        } catch (DbException $e) {
            Log::error($e->getMessage());
            return $this->error(500, $e->getMessage());
        }

        return $this->success(true);
    }

    public function edit(Request $request)
    {
        $post = $request->post();
        if (empty($post['id'])) return $this->error(500, '参数错误');
        try {
            ShortcutContents::update($post);
        } catch (DbException $e) {
            Log::error($e->getMessage());
            return $this->error(500, '更新失败');
        }
        return $this->success(true);
    }

    public function del(Request $request)
    {
        $post = $request->post();
        if (empty($post['id'])) return $this->error(500, '参数错误');
        return $this->success(ShortcutContents::destroy($post['id']));
    }

}