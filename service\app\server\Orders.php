<?php

namespace app\server;

use app\common\Error;
use app\model\Admins;
use app\model\Admins as AdminsModel;
use app\model\Blacks;
use app\model\FilterMobiles;
use app\model\Finances as FinancesModel;
use app\model\LiveRoomWorks;
use app\model\OrderAllocateLogs;
use app\model\Orders as OrdersModel;
use app\model\Products;
use Carbon\Carbon;
use http\Exception\InvalidArgumentException;
use support\Log;
use support\Redis;

class Orders {
    protected static $redisPool = [];
    public static function isDaishiyong(OrdersModel $order): bool
    {
        // 根据 OrdersModel::AllOssStatusSql[1] 进行判断
        // ((os=1 and order_status=3) or (os=2 and order_status=4) or (os=3 and order_status=1))
        return (in_array($order->os, [1, 7]) && $order->order_status == 3)
            || ($order->os == 2 && $order->order_status == 4)
            || (in_array($order->os, [3, 5]) && in_array($order->order_status, [1, 2, 5]))
            || (in_array($order->os, [6, 8]) && in_array($order->order_status, [200, 205, 210, 300, 310]));
    }

    /**
     * @params []OrdersModel $order
     * @return array
     */
    public static function reminderOrders(OrdersModel ...$orders)
    {
        $admin_ids = [];
        $sns = [];

        foreach ($orders as $order) {
            $admin_ids[] = $order->admin_id;
            $sns[] = $order->sn;
        }

        $admins = AdminsModel::whereIn('id', $admin_ids)->select()->column('mobile', 'id');

        if (empty($admins)) {
            return array_fill_keys($sns, Error::undefined('admin check error'));
        }

        $ttl    = 86400;
        $result = [];

        $admins = array_column($admins, null, 'id');
        foreach($orders as $order) {
            $admin_mobile = $admins[$order->admin_id] ?? '';

            if (empty($order->mobile) || empty($admin_mobile)) {
                $result[$order->sn] = Error::undefined('client mobile or admin mobile invalid');
                continue;
            }
            if (FilterMobiles::isFilterMobile($order->mobile)) {
                $result[$order->sn] = Error::undefined('刷单账单');
                continue;
            }
            $admin_mobile = $admin_mobile['mobile'] ?? '';

            if (\app\server\Blacks::CExists($order->mobile)) {
                $result[$order->sn] = Error::ERR_SMS_BLACKS;
                Log::info(__METHOD__ . " blacks", [$order->mobile, $order->sn]);
                continue;
            }

            $key = sprintf("CRM:%s:%s_%s", 'reminderOrders', $order->sn, $order->mobile);
            $ok  = Redis::set($key, time(), 'EX', $ttl, 'NX');

            if (!$ok) {
                $result[$order->sn] = Error::undefined('reminder cooldown');
                continue;
            }

            $res = SMS::juhe_sms_send($order->mobile, SMS::JUHE_TMP_REMINDER_ORDER, ['title' => $order->product_name, 'mobile' => $admin_mobile]);
            $err_code = $res['error_code'] ?? 0;

            if ($err_code != 0) {
                Log::error(__METHOD__ . " send error", [$res, $order->mobile, $order->sn]);
                $result[$order->sn] = Error::ERR_SMS_SEND_FAIL;
                continue;
            }

            $result[$order->sn] = [];
        }

        return count($orders) > 1 ? $result : reset($result);
    }

    public static function syncFromThird(OrdersModel $order)
    {
        $redisKey = 'sync_order:' . $order->sn;
        if (Redis::exists($redisKey)) {
            return 1;
        }
        Redis::setEx($redisKey, mt_rand(300, 600), 1);
        $got = null;

        // 记录同步前的订单状态，用于检测状态变化
        $originalOrderStatus = $order->order_status;

        switch ($order->os) {
            case 1:
            case 7:
                $mt = new Meituan($order->os);
                $it = $mt->get(1, null, null, $order->sn);

                if ($it) {
                    $got = $it[0];
                }

                break;
            case 3:
            case 5:
                $dy = new Douyin($order->os);
                $it = $dy->get(1, null, null, $order->sn);
                if ($it) {
                    $got = $it[0];

                    // 查询未预约状态
                    if ($order->appointment_status == 0) {
                        $appointment = $dy->_orderMakeAppointmentStatus($order->sn);
                        if ($appointment !== null) {
                            $got['appointment_status'] = 1;
                        }
                    }
                }

                break;
            case 6:
            case 8:
                $tc = new Tongcheng($order->os);
                $it = $tc->get(1, null, null, $order->sn);

                if ($it) {
                    $got = $it[0];
                }
                break;
        }

        if (is_null($got)) {
            Log::info(__METHOD__ . ": get from os is null", [$order]);
            return;
        }

        $back = $order->save($got);

        if ($back) {
            self::finance(0, $order->id, $order->asset_price, $order->shop_id);

            // 检测是否从非退款状态变为退款状态
            $newOrderStatus = $order->order_status;
            if (self::isRefundStatusChange($order->os, $originalOrderStatus, $newOrderStatus)) {
                // 触发退款订单分配给私域客服
                self::allocateRefundOrderToPrivateCustomer($order);
            }
        }

        return 0;
    }

    public static function finance($type = 1, $order_id = 0, $price = 0, $shopId = 0)
    {
        //总的关于这个订单的金额
        $total = FinancesModel::where('order_id', $order_id)->sum('total');
        //如果总金额大于提交上来的核销金额,那就是退费的
        //如果提交上来的金额小于总金额,那就是核销的

        if ($total > $price) {
            $type = 2;
            $fee = -($total - $price);
        } elseif ($total < $price) {
            $type = 1;
            $fee = $price - $total;
        } else {
            return;
        }

        FinancesModel::create([
            'order_id' => $order_id,
            'type' => $type,
            'total' => $fee,
            'status' => 1,
            'shop_id' => $shopId,
        ]);

        return;
    }

    /**
     * 同步第三方返回
     * @param OrdersModel $order
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function syncFromThirdV2(OrdersModel $order)
    {
        // 记录同步前的订单状态，用于检测状态变化
        $originalOrderStatus = $order->order_status;

        $got = null;
        switch ($order->os) {
            case 1:
            case 7:
                $mt = new Meituan($order->os);
                $it = $mt->get(1, null, null, $order->sn);

                if ($it) {
                    $got = $it[0];
                }

                break;
            case 3:
            case 5:
                $dy = new Douyin($order->os);
                $it = $dy->get(1, null, null, $order->sn);
                if ($it) {
                    $got = $it[0];
                }

                break;
        }

        if (is_null($got)) {
            Log::info(__METHOD__ . ": get from os is null", [$order]);
            return $order;
        }

        $back = $order->save($got);

        if ($back) {
            self::finance(0, $order->id, $order->asset_price, $order->shop_id);

            // 检测是否从非退款状态变为退款状态
            $newOrderStatus = $order->order_status;
            if (self::isRefundStatusChange($order->os, $originalOrderStatus, $newOrderStatus)) {
                // 触发退款订单分配给私域客服
                self::allocateRefundOrderToPrivateCustomer($order);
            }
        }

        return $order;
    }

    /**
     * @param int $orderCreateTime
     * @param     $productId
     * @return array|mixed|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getLiveRoomWork(int $orderCreateTime, $productId) {
        $orderCreateDate = date('Y-m-d H:i:s', $orderCreateTime/1000);
        $roomWork = LiveRoomWorks::join('live_room', 'live_room_works.live_room_id=live_room.id')
            ->where('live_room_works.start', '<', $orderCreateDate)
            ->where('live_room_works.end', '>', $orderCreateDate)
            ->whereRaw(sprintf('find_in_set(%s, live_room.`product_ids`)', $productId))
            ->field(['live_room_works.id'])
            ->find();

        return $roomWork;
    }

    /**
     * 分配客服
     * @param $order
     * @return array
     * @throws \think\db\exception\BindParamException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function allocatCustomer($order) {
        $mobile = $order->mobile;
        $productId = $order->product_id;
        $adminId = 0;
        $shopId = 0;
        $product = Products::where('third_product_id', $productId)->find();
        if (empty($product)) {
            return compact('adminId', 'shopId');
        }
        $shopId = $product->shop_id;

        $allocateMethod = '在线客服分配'; // 记录分配方式
        $extraData = [
            'mobile' => $mobile,
            'product_id' => $productId,
            'product_name' => $product->name ?? '',
        ];

        if (FilterMobiles::isFilterMobile($mobile)) {
            $adminId = 60;
            $shopId = 1;
            $allocateMethod = '过滤手机号分配';
        } else {
            $oldMobile = \app\model\Orders::where("mobile", $mobile)
                ->where('admin_id', '!=', 0)
                ->where("create_time", '>=', time() - 24 * 3600 * 3)->find();

            if (!empty($oldMobile) && $shopId == $oldMobile->shop_id) {
                $adminId = $oldMobile->admin_id;
                $allocateMethod = '历史客服分配';
                $extraData['old_order_sn'] = $oldMobile->sn;
            }

            if (!$adminId) {
                try {
                    $adminId = self::poolUser(1, $productId);
                    $allocateMethod = '在线客服分配';
                } catch (\Exception $exception) {
                    Log::info(sprintf('create order fail:%s, order_id:%s, os:%s, product_id:%s', $exception->getMessage(), $order->sn, $order->os, $productId));
                    $allocateMethod = '分配失败';
                }
            }
        }

        // 记录分配日志
        if ($adminId && $order->sn) {
            self::recordAllocateLog($order->sn, $adminId, $shopId, $allocateMethod, $extraData);
        }

        return compact('adminId', 'shopId');
    }

    /**
     * 记录订单分配日志
     * @param string $orderSn 订单号
     * @param int $adminId 客服ID
     * @param int $shopId 门店ID
     * @param string $allocateMethod 分配方式
     * @param array $extraData 额外数据
     * @return bool
     */
    public static function recordAllocateLog($orderSn, $adminId, $shopId, $allocateMethod = '', $extraData = [])
    {
        try {
            // 获取管理员的线路权限信息
            $admin = Admins::where('id', $adminId)->field(['route_type', 'product_ids', 'route_group_ids'])->find();
            $routePermission = '';

            if ($admin) {
                $permissions = [];

                // 添加产品权限
                if (!empty($admin->product_ids)) {
                    $permissions[] = '产品权限:' . $admin->product_ids;
                }

                // 添加线路组权限
                if (!empty($admin->route_group_ids)) {
                    $groupIds = explode(',', $admin->route_group_ids);
                    $groupNames = \app\model\OtaRouteGroups::whereIn('id', $groupIds)
                        ->column('group_name');
                    if (!empty($groupNames)) {
                        $permissions[] = '线路组权限:' . implode(',', $groupNames);
                    }
                }

                $routePermission = !empty($permissions) ? implode(';', $permissions) : '未设置权限';
            }

            // 添加分配方式到额外数据
            $extraData['allocate_method'] = $allocateMethod;
            $extraData['allocate_timestamp'] = time();

            return OrderAllocateLogs::recordAllocateLog(
                $orderSn,
                $adminId,
                $shopId,
                $routePermission,
                $extraData
            );
        } catch (\Exception $e) {
            Log::error('记录订单分配日志失败', [
                'order_sn' => $orderSn,
                'admin_id' => $adminId,
                'shop_id' => $shopId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 分配用户
     * @param int    $status
     * @param string $categoryDesc
     * @return int|string
     * @throws \Exception
     */
    public static function poolUser($status, $thirdProductId) {
        $status .= $thirdProductId;
//        if (empty(self::$redisPool[$status])) {
            self::$redisPool[$status] = Redis::hGetAll('CRM:Pool:' . $status);
            $users = self::users($thirdProductId);
            shuffle($users);
            $_users = [];
            if (empty(self::$redisPool[$status])) {
                foreach ($users as $user) {
                    $_users[$user->username] = 0;
                    Redis::hSet('CRM:Pool:' . $status, $user->username, 0);
                }
                self::$redisPool[$status] = $_users;
            } else {
                asort(self::$redisPool[$status]);
                $key_users = array_keys(self::$redisPool[$status]);
                $username = $key_users[0];
                $max = self::$redisPool[$status][$username];
                $_users = [];
                foreach ($users as $user) {
                    $_users[] = $user->username;
                    if (!in_array($user->username, $key_users)) {
                        self::$redisPool[$status][$username] = $max;
                        Redis::hSet('CRM:Pool:' . $status, $user->username, $max);
                    }
                }
                foreach (self::$redisPool[$status] as $username => $val) {
                    if (!in_array($username, $_users)) {
                        unset(self::$redisPool[$status][$username]);
                        Redis::hDel('CRM:Pool:' . $status, $username);
                    }
                }
            }
//        }

        $username = null;
        try {
            $pool = self::$redisPool[$status];
            // 根据今日分配数量再排序
            if (empty($pool)) $pool = [];
            asort($pool);
            $keys = array_keys($pool);
            if (empty($keys)) {
                throw new \Exception('没有可以分配的用户');
            }
            $username = $keys[0];
            self::$redisPool[$status][$username] += 1;
            Redis::hIncrBy('CRM:Pool:' . $status, $username, 1);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
        return $username;
    }

    /**
     * @param $thirdProductId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected static function users($thirdProductId) {
        $product = Products::query()->where('third_product_id', $thirdProductId)->find();
        if (empty($product)) {
            return [];
        }

        // 获取符合条件的管理员：优先线路组分配，其次单个线路分配
        $users = collect();

        // 1. 通过线路组匹配管理员
        $groupAdmins = Admins::where('status', 1)
            ->where('is_order', 1)
            ->where('route_group_ids', '<>', '')
            ->whereNotNull('route_group_ids')
            ->select();

        foreach ($groupAdmins as $admin) {
            if (empty($admin->route_group_ids)) {
                continue;
            }

            $groupIds = array_filter(explode(',', $admin->route_group_ids));
            if (!empty($groupIds)) {
                // 使用OtaRouteGroups模型检查产品是否在任何线路组中
                foreach ($groupIds as $groupId) {
                    if (\app\model\OtaRouteGroups::isProductInGroup($groupId, $product->id)) {
                        $users->push($admin);
                        break; // 只需要添加一次这个管理员
                    }
                }
            }
        }

        // 2. 如果没有通过线路组匹配到管理员，使用传统的单个线路匹配
        if ($users->isEmpty()) {
            $productAdmins = Admins::where('status', 1)
                ->where('is_order', 1)
                ->whereFindInSet('product_ids', $product->id)
                ->select();

            foreach ($productAdmins as $admin) {
                $users->push($admin);
            }
        }

        $us = [];
        $whiteUids = [53, 94, 95];
        foreach ($users as $u) {
            $ru = Redis::get('CRM:USER:ONLINE:' . $u->id);
            if (empty($ru)) continue;

            if ($u->shop_id == 1 && !in_array($u->id, $whiteUids) && !self::checkWorkTimeCondition($u->start_work_time)) {
                continue;
            }

            $_u = new \stdClass();
            $_u->username = $u->id;
            $us[] = $_u;
        }

        return $us;
    }

    /**
     * 判断工作时间是否在上午，且当前时间早于当日13:30
     * @param int $workStartTimestamp 精确到秒的时间戳
     * @return bool
     */
    private static function checkWorkTimeCondition(int  $workStartTimestamp): bool {
        $isLegal = false;
        try {
            date_default_timezone_set('Asia/Shanghai');
            if ($workStartTimestamp < 0) {
                throw new InvalidArgumentException("时间戳不能为负数");
            }
//            $now = Carbon::now();
//            $startToday = Carbon::now()->setTime(9, 10, 0);
//            $deadlineToday = Carbon::now()->setTime(22, 0);
//            if ($now > $startToday && $now < $deadlineToday) {
//                $isLegal = true;
//            }

            $workStart = (new \DateTime())->setTimestamp($workStartTimestamp);
            $isMorning = (int)$workStart->format('H') < 12; // 是否是早班
            $currentTime = new \DateTime('now');
            $startToday = (clone $currentTime)->setTime(9, 10, 0);
            $deadlineToday = (clone $currentTime)->setTime(14, 0, 0);

            if ($isMorning && $currentTime > $startToday && ($currentTime < $deadlineToday)) {
                $isLegal = true;
            }

            // 下午上线的用户，2开始分单,10点结束
            $startAfternoon = (new $currentTime)->setTime(14, 0);
            $deadlineAfternoon = (clone $currentTime)->setTime(22, 0, 0);
            if (!$isMorning && $currentTime > $startAfternoon && ($currentTime < $deadlineAfternoon)) {
                $isLegal = true;
            }
        } catch (\Exception $e) {
            throw new InvalidArgumentException("时间处理错误: " . $e->getMessage());
        }

        return $isLegal;
    }

    /**
     * 发送订单短信
     * @param $admin_id
     * @param $order
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function sendOrderSms($admin_id, $order)
    {
        $user = Admins::cache(true)->where('id', $admin_id)->find();
        if ((!config('app.debug', true) || config('app.debug', true) === 'false') && (time() * 1000 - $order->create_at) / 1000 < 2 * 24 * 3600) {
            $has = Blacks::where('mobile', $order->mobile)->find();
            if (empty($has) && !empty($order->mobile)) {
                if (env('SMS_JUMP_URL')) {
                    SMS::juhe_sms_send($order->mobile, 265867, ['title' => $order->product_name, 'mobile' => $user->mobile, 'url' => env('SMS_JUMP_URL')]);
                } else {
                    SMS::juhe_sms_send($order->mobile, 261607, ['title' => $order->product_name, 'mobile' => $user->mobile]);
                }
            } else {
                sleep(10);
            }
        } else {
            Log::info('订单未发送短息：' . json_encode([$order->mobile, 261607, ['title' => $order->product_name, 'mobile' => $user->mobile]]));
        }
    }

    /**
     * 检测是否从非退款状态变为退款状态
     * @param int $os 订单来源
     * @param int $originalStatus 原始状态
     * @param int $newStatus 新状态
     * @return bool
     */
    public static function isRefundStatusChange(int $os, int $originalStatus, int $newStatus): bool
    {
        // 检查功能是否启用
        $config = config('refund_convert');
        if (!$config['enabled']) {
            return false;
        }

        // 使用配置中的退款状态映射
        $refundStatusMap = $config['refund_status_map'] ?? [];

        if (!isset($refundStatusMap[$os])) {
            return false; // 不支持的订单来源
        }

        $refundStatus = $refundStatusMap[$os];
        return $originalStatus != $refundStatus && $newStatus == $refundStatus;
    }

    /**
     * 退款订单分配给私域客服
     * @param OrdersModel $order 直播订单
     */
    public static function allocateRefundOrderToPrivateCustomer(OrdersModel $order)
    {
        $config = config('refund_convert');

        // 检查功能是否启用
        if (!$config['enabled'] || $order->shop_id != 1 || !$order->admin_id) {
            return;
        }

        try {
            if ($config['enable_logging']) {
                Log::info('直播订单退款，开始分配私域客服', [
                    'order_sn' => $order->sn,
                    'order_id' => $order->id,
                    'os' => $order->os,
                    'order_status' => $order->order_status,
                    'mobile' => $order->mobile
                ]);
            }

            // 过滤检查
            if ($config['filters']['require_mobile'] && empty($order->mobile)) {
                if ($config['enable_logging']) {
                    Log::warning('退款订单无手机号，跳过私域分配', ['order_sn' => $order->sn]);
                }
                return;
            }

            if ($config['filters']['filter_fake_mobiles'] && FilterMobiles::isFilterMobile($order->mobile)) {
                if ($config['enable_logging']) {
                    Log::info('退款订单为过滤手机号，跳过私域分配', ['order_sn' => $order->sn, 'mobile' => $order->mobile]);
                }
                return;
            }

            if ($config['filters']['filter_blacks'] && \app\server\Blacks::CExists($order->mobile)) {
                if ($config['enable_logging']) {
                    Log::info('退款订单手机号在黑名单，跳过私域分配', ['order_sn' => $order->sn, 'mobile' => $order->mobile]);
                }
                return;
            }

            // 重复检查
            if ($config['check_duplicate']) {
                $checkDays = $config['duplicate_check_days'] ?? 30;
                $existingXsOrder = \app\model\OrdersXs::where('mobile', $order->mobile)
                    ->where('create_at', '>', (time() - $checkDays * 24 * 3600) . '000')
                    ->find();

                if ($existingXsOrder) {
                    if ($config['enable_logging']) {
                        Log::info("该客户{$checkDays}天内已有私域订单，跳过创建", [
                            'order_sn' => $order->sn,
                            'existing_xs_order_sn' => $existingXsOrder->sn
                        ]);
                    }
                    return;
                }
            }

            // 获取产品信息
            $product = Products::where('third_product_id', $order->product_id)->find();
            if (empty($product)) {
                if ($config['enable_logging']) {
                    Log::warning('未找到对应产品信息，跳过私域分配', [
                        'order_sn' => $order->sn,
                        'product_id' => $order->product_id
                    ]);
                }
                return;
            }

            // 分配私域客服
            $adminId = 0;
            $allocateMethod = '退款转私域分配';

            try {
                $order->product_id = $config['private_product_id'];
                // 使用私域客服分配逻辑
                $adminId = \app\server\OrdersXs::poolUser(1, $order->product_id);
                if ($config['enable_logging']) {
                    Log::info('成功分配私域客服', ['order_sn' => $order->sn, 'admin_id' => $adminId]);
                }
            } catch (\Exception $e) {
                if ($config['enable_logging']) {
                    Log::error('私域客服分配失败', [
                        'order_sn' => $order->sn,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // 创建私域订单
            $xsOrder = new \app\model\OrdersXs();
            $xsOrder->sn = 'XS' . $order->sn;
            $xsOrder->product_id = $order->product_id;
            $xsOrder->product_name = $order->product_name;
            $xsOrder->os = $config['private_os_code'] ?? 21;
            $xsOrder->mobile = $order->mobile;
            $xsOrder->create_at = time() . '000';
            $xsOrder->remark = '直播退款转私域：' . $order->sn;
            $xsOrder->level = $config['default_level'] ?? 2;
            $xsOrder->fans_status = 1;
            $xsOrder->shop_id = $product->shop_id ?? $order->shop_id;
            $xsOrder->admin_id = $adminId;
            $xsOrder->status = $config['default_status'] ?? 0;
            $xsOrder->source_order_sn = $order->sn; // 记录来源订单号
            $xsOrder->total_price = $order->total_price ?? 0;
            $xsOrder->actual_price = $order->actual_price ?? 0;

            if ($xsOrder->save()) {
                // 记录分配日志
                \app\server\OrdersXs::recordAllocateLog(
                    $xsOrder->sn,
                    $adminId,
                    $xsOrder->shop_id,
                    $allocateMethod,
                    [
                        'source_order_sn' => $order->sn,
                        'refund_reason' => '直播订单退款转私域',
                        'original_os' => $order->os,
                        'original_order_status' => $order->order_status
                    ]
                );

                if ($config['enable_logging']) {
                    Log::info('退款订单成功转为私域订单', [
                        'original_order_sn' => $order->sn,
                        'xs_order_sn' => $xsOrder->sn,
                        'admin_id' => $adminId,
                        'mobile' => $order->mobile
                    ]);
                }

                // 发送通知
                if ($config['notify_admin']) {
                    try {
                        $admin = Admins::where('id', $adminId)->find();
                        if ($admin && !empty($admin->mobile)) {
                            if ($config['enable_logging']) {
                                Log::info('退款转私域通知', [
                                    'admin_mobile' => $admin->mobile,
                                    'customer_mobile' => $order->mobile,
                                    'product_name' => $order->product_name
                                ]);
                            }
                            // 这里可以添加实际的短信通知逻辑
                            // SMS::send($admin->mobile, 'REFUND_CONVERT_TEMPLATE', [...]);
                        }
                    } catch (\Exception $e) {
                        if ($config['enable_logging']) {
                            Log::error('发送退款转私域通知失败', ['error' => $e->getMessage()]);
                        }
                    }
                }

            } else {
                if ($config['enable_logging']) {
                    Log::error('创建私域订单失败', [
                        'order_sn' => $order->sn,
                        'admin_id' => $adminId
                    ]);
                }
            }

        } catch (\Exception $e) {
            if ($config['enable_logging']) {
                Log::error('退款订单分配私域客服异常', [
                    'order_sn' => $order->sn,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
    }
}
