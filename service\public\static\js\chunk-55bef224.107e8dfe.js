(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-55bef224"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function l(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function n(t,e,a){var n=s(),o=t-n,r=20,c=0;e="undefined"===typeof e?500:e;var u=function(){c+=r;var t=Math.easeInOutQuad(c,n,o,e);l(t),c<e?i(u):a&&"function"===typeof a&&a()};u()}},3413:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"订单号"},model:{value:t.listQuery.sn,callback:function(e){t.$set(t.listQuery,"sn",e)},expression:"listQuery.sn"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"手机号"},model:{value:t.listQuery.mobile,callback:function(e){t.$set(t.listQuery,"mobile",e)},expression:"listQuery.mobile"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"100px"},attrs:{placeholder:"主播"},model:{value:t.listQuery.zhubo,callback:function(e){t.$set(t.listQuery,"zhubo",e)},expression:"listQuery.zhubo"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"100px"},attrs:{placeholder:"客服"},model:{value:t.listQuery.admin,callback:function(e){t.$set(t.listQuery,"admin",e)},expression:"listQuery.admin"}}),a("el-cascader",{staticClass:"filter-item",attrs:{placeholder:"平台状态",options:t.oss},on:{change:t.handleChange},model:{value:t.listQuery.os_status,callback:function(e){t.$set(t.listQuery,"os_status",e)},expression:"listQuery.os_status"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"跟进状态"},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},[a("el-option",{key:"",attrs:{label:"请选择",value:""}}),t._l(t.status_arr,(function(t,e){return a("el-option",{key:e,attrs:{label:t,value:e}})}))],2),a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"时间"},model:{value:t.listQuery.timetype,callback:function(e){t.$set(t.listQuery,"timetype",e)},expression:"listQuery.timetype"}},[a("el-option",{key:"",attrs:{label:"请选择",value:""}}),t._l(t.timetype_arr,(function(t,e){return a("el-option",{key:e,attrs:{label:t,value:e}})}))],2),a("el-date-picker",{staticClass:"filter-item",attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","default-time":["00:00:00","23:59:59"],"end-placeholder":"结束日期"},model:{value:t.listQuery.times,callback:function(e){t.$set(t.listQuery,"times",e)},expression:"listQuery.times"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}}},[t._v(" 导出 ")]),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(e){t.dialog2Visible=!0}}},[t._v(" 核单 ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"center",fixed:"",label:"电话",width:"120",prop:"mobile"}}),a("el-table-column",{attrs:{align:"center",fixed:"",label:"平台",width:"80",prop:"os_name"}}),a("el-table-column",{attrs:{align:"center",fixed:"",label:"直播",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.is_zhibo?a("el-tag",[t._v("是")]):a("el-tag",{attrs:{type:"info"}},[t._v("否")])]}}])}),a("el-table-column",{attrs:{align:"center",fixed:"",label:"客服",width:"80",prop:"admin.username"}}),a("el-table-column",{attrs:{align:"center",label:"订单号",width:"180",prop:"sn"}}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"下单时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.create_at,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{width:"160px",align:"center",label:"派单时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.give_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"状态",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{padding:"1px 5px","border-radius":"3px"},style:{color:t.order_status[e.row.order_status],border:"1px solid "+t.order_status[e.row.order_status]},attrs:{type:"primary"}},[t._v(" "+t._s(e.row.order_status_name)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"跟进状态",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{padding:"1px 5px","border-radius":"3px"},style:{color:t.follow_status[e.row.status],border:"1px solid "+t.follow_status[e.row.status]},attrs:{type:"primary"}},[t._v(" "+t._s(e.row.status_name)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",width:"500px",label:"标题",prop:"product_name"}}),a("el-table-column",{attrs:{width:"500px",align:"center",label:"跟进备注",prop:"remark"}}),a("el-table-column",{attrs:{align:"center",label:"联系人",width:"120",prop:"contact"}}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"出行时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.travel_date,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"最后跟进时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.last_follow,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"核单",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.is_check?a("i",{staticClass:"el-icon-check"}):t._e(),2==e.row.is_check?a("i",{staticClass:"el-icon-close"}):t._e()]}}])}),a("el-table-column",{attrs:{align:"center",width:"138px",label:"分类",prop:"category_desc"}}),a("el-table-column",{attrs:{align:"center",label:"总金额",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.total_price/100))])]}}])}),a("el-table-column",{attrs:{align:"center",width:"80px",label:"人数",prop:"quantity"}}),a("el-table-column",{attrs:{align:"center",label:"主播",width:"80",prop:"anchor.username"}}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"修改时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.update_time,"{y}-{m}-{d} {h}:{i}")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}),a("el-dialog",{attrs:{title:"订单跟进",visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{attrs:{"label-width":"130px",model:t.item}},[a("el-form-item",{attrs:{label:"产品名称"}},[t._v(" "+t._s(t.item.product_name)+" ")]),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"产品状态"}},[t._v(" "+t._s(t.item.order_status_name)+" ")]),a("el-form-item",{attrs:{label:"数量"}},[t._v(" "+t._s(t.item.quantity)+" ")]),a("el-form-item",{attrs:{label:"联系人"}},[t._v(" "+t._s(t.item.contact)+" ")]),a("el-form-item",{attrs:{label:"手机"}},[t._v(" "+t._s(t.item.mobile)+" ")]),a("el-form-item",{attrs:{label:"下单时间"}},[t._v(" "+t._s(t._f("parseTime")(t.item.create_at,"{y}-{m}-{d} {h}:{i}"))+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"人员"}},[a("el-row",[a("el-col",{attrs:{span:3}},[t._v("大人")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"adult",placeholder:"大人"},model:{value:t.item.personnel.adult,callback:function(e){t.$set(t.item.personnel,"adult",e)},expression:"item.personnel.adult"}})],1),a("el-col",{attrs:{span:3}},[t._v("老人")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"old",placeholder:"老人"},model:{value:t.item.personnel.old,callback:function(e){t.$set(t.item.personnel,"old",e)},expression:"item.personnel.old"}})],1),a("el-col",{attrs:{span:3}},[t._v("小孩")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"child",placeholder:"小孩"},model:{value:t.item.personnel.child,callback:function(e){t.$set(t.item.personnel,"child",e)},expression:"item.personnel.child"}})],1)],1)],1),1!==t.item.status?a("el-form-item",{attrs:{label:"核销码"}},[a("el-input",{attrs:{name:"check_sn",placeholder:"请输入平台核销码"},model:{value:t.item.check_sn,callback:function(e){t.$set(t.item,"check_sn",e)},expression:"item.check_sn"}})],1):t._e(),2!==t.item.status?a("el-form-item",{attrs:{label:"加微信"}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.item.is_wechat,callback:function(e){t.$set(t.item,"is_wechat",e)},expression:"item.is_wechat"}},[t._v("已加微信")])],1):t._e(),a("el-form-item",{attrs:{label:"出游日期"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期时间"},model:{value:t.item.travel_date,callback:function(e){t.$set(t.item,"travel_date",e)},expression:"item.travel_date"}})],1),1!==t.item.status?a("el-form-item",{attrs:{label:"返回日期"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期时间"},model:{value:t.item.travel_end,callback:function(e){t.$set(t.item,"travel_end",e)},expression:"item.travel_end"}})],1):t._e(),2!==t.item.status?a("el-form-item",{attrs:{label:"下次跟进时间"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间"},model:{value:t.next_follow,callback:function(e){t.next_follow=e},expression:"next_follow"}})],1):t._e()],1)],1),a("el-form-item",{attrs:{label:"跟进状态"}},[t._l(t.status_arr,(function(e,i){return[i>0?a("el-radio",{attrs:{label:i,border:""},model:{value:t.item.status,callback:function(e){t.$set(t.item,"status",e)},expression:"item.status"}},[t._v(t._s(e))]):t._e()]}))],2),a("el-form-item",{attrs:{label:"跟进说明"}},[a("el-input",{attrs:{type:"textarea"},model:{value:t.item.desc,callback:function(e){t.$set(t.item,"desc",e)},expression:"item.desc"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSave(t.item)}}},[t._v("保 存")])],1),a("el-tabs",{attrs:{type:"border-card"},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[a("el-tab-pane",{attrs:{name:"follow",label:"跟进记录"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.item.follow}},[a("el-table-column",{attrs:{label:"日期",width:"138"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{label:"跟进人",width:"110",prop:"admin.username"}}),a("el-table-column",{attrs:{label:"状态",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.status_arr[e.row.status]))])]}}])}),a("el-table-column",{attrs:{prop:"desc",label:"跟进说明"}})],1)],1),a("el-tab-pane",{attrs:{name:"finance",label:"财务记录"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.item.finance}},[a("el-table-column",{attrs:{label:"日期"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{label:"类型",width:"110"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.type_arr[e.row.type]))])]}}])}),a("el-table-column",{attrs:{label:"状态",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.total/100))])]}}])})],1)],1)],1)],1),a("el-dialog",{attrs:{title:"纯核销",visible:t.dialog2Visible},on:{"update:visible":function(e){t.dialog2Visible=e}}},[a("el-form",{attrs:{"label-width":"160px",model:t.form}},[a("el-form-item",{attrs:{label:"平台"}},[a("el-radio",{attrs:{label:"1"},model:{value:t.form.os,callback:function(e){t.$set(t.form,"os",e)},expression:"form.os"}},[t._v("美团")])],1),a("el-form-item",{attrs:{label:"核销码"}},[a("el-input",{attrs:{placeholder:"请输入平台核销码"},model:{value:t.form.check_sn,callback:function(e){t.$set(t.form,"check_sn",e)},expression:"form.check_sn"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onPass(t.form)}}},[t._v("保 存")])],1)],1),a("el-dialog",{attrs:{title:"申请转出订单",visible:t.applyVisible},on:{"update:visible":function(e){t.applyVisible=e}}},[a("el-form",{ref:"ruleForm",attrs:{"label-width":"160px",model:t.item3,rules:t.rules}},[a("el-form-item",{attrs:{label:"标题:"}},[a("el-input",{attrs:{disabled:""},model:{value:t.item3.product_name,callback:function(e){t.$set(t.item3,"product_name",e)},expression:"item3.product_name"}})],1),a("el-form-item",{attrs:{label:"订单号:"}},[a("el-input",{attrs:{disabled:""},model:{value:t.item3.sn,callback:function(e){t.$set(t.item3,"sn",e)},expression:"item3.sn"}})],1),a("el-form-item",{staticStyle:{width:"600px"},attrs:{label:"流转对象:",prop:"flowObj"}},[a("el-select",{attrs:{placeholder:"请选择"},on:{change:t.onChange2},model:{value:t.item3.flowObj,callback:function(e){t.$set(t.item3,"flowObj",e)},expression:"item3.flowObj"}},[a("el-form-item",{staticStyle:{display:"inline-flex","text-align":"left",width:"770px"}},t._l(t.adminList,(function(t){return a("el-option",{key:t.value,staticStyle:{width:"250px",display:"inline-flex","word-break":"break-all"},attrs:{label:t.username,value:t.id}})})),1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t.item3.backs&&0==t.item3.backs.status?a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onCancel(t.item3.flowObj)}}},[t._v("取 消")]):a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onCirculationSave(t.item3.flowObj)}}},[t._v("确 认")])],1)],1)],1)},l=[],s=a("d00a"),n=a("d09a"),o=(a("e224"),a("4cc3"),a("374d"),a("5227"),a("67f2")),r=a("f8b7"),c={name:"Orderlist",components:{Pagination:o["a"]},data:function(){return{active:"follow",types:{0:"",1:"",2:"",3:"primary",4:"success",5:"warning",6:"danger",7:"info"},types2:{1:"primary",2:"success",3:"warning"},status_arr:["待跟进","跟进中","已核销","核销失败","放弃跟单","加入公海"],type_arr:["-","收益","支出"],timetype_arr:{},order_status:["#9e9f9c","#04bcd9","#fc9904","#1193f4","#48b14b","#eb1662","#9d1cb5"],follow_status:["#9e9f9c","#04bcd9","#fc9904","#1193f4","#48b14b","#eb1662"],options:[],value:null,next_follow:null,list:[],total:0,listLoading:!0,listQuery:{page:1,limit:10,times:[],status:null,admin:null,zhubo:null,os_status:[]},item:{next_follow:"",personnel:{}},follow:[],dialogVisible:!1,dialog2Visible:!1,applyVisible:!1,oss:[],item3:{sn:null,backs:null,flowObj:"",os:null},adminList:[],form:{},rules:{flowObj:[{required:!0,message:"请选择流转对象",trigger:"change"}]}}},created:function(){this.listQuery.zhubo=this.$route.query.zhubo||null,this.$route.query.start&&this.$route.query.end&&(this.listQuery.times=[this.$route.query.start,this.$route.query.end]),this.setQuery("status"),this.setQuery("os_status"),this.setQuery("times"),this.getList(),this.getShortcutContent(),this.getAdminList()},methods:{setQuery:function(t){this.$route.query.hasOwnProperty(t)?this.listQuery[t]=this.$route.query[t]:this.listQuery[t]=""},getList:function(t){var e=this;if(this.listQuery.excel=null,1!=t)this.listQuery.os_status=[4,2],this.$axios.get("/admin/order/index",{params:this.listQuery}).then((function(t){e.list=t.data.data,e.total=t.data.total,e.timetype_arr=t.ext.timetype,e.oss=t.ext.oss,e.listLoading=!1}));else{this.listQuery.excel=1;var a=this.listQuery.times[0]instanceof Date,i=Object(n["a"])(Object(n["a"])({},this.listQuery),{},{times:[a?this.listQuery.times[0].toISOString():"",a?this.listQuery.times[1].toISOString():""]});window.open("/admin/order/index?"+this.objectToQuery(i))}},objectToQuery:function(t){return Object.keys(t).map((function(e){var a=t[e];return void 0==a||null==a?"":encodeURIComponent(e)+"="+encodeURIComponent(a)})).join("&")},onInfo:function(t){var e=this;this.value=null,this.next_follow=null,this.$set(t,"next_follow",null),this.item=t,this.active="follow",this.$axios.get("/admin/order/info",{params:{id:t.id}}).then((function(t){e.item=t.data,e.dialogVisible=!0})).catch((function(t){}))},resetForm:function(t){this.$refs[t].resetFields()},getAdminList:function(){var t=this;this.$axios.get("/admin/admin/index",{params:{limit:100,status:1,is_order:1}}).then((function(e){t.adminList=e.data.data,t.listLoading=!1})).catch((function(t){}))},onCirculation:function(t){this.applyVisible=!0,this.item3=Object(n["a"])(Object(n["a"])({},t),{},{os:Number(t.os)}),console.log(this.item3),this.item3.backs&&this.item3.backs.admin_id?this.item3.flowObj=this.item3.backs.admin_id:this.resetForm("ruleForm")},onCirculationSave:function(t){var e=this;this.$refs.ruleForm.validate((function(a){if(!a)return!1;Object(r["a"])({sn:e.item3.sn,os:e.item3.os,to_admin_id:t}).then((function(t){e.applyVisible=!1,e.getList()}))}))},onCancel:function(){var t=this;this.$refs.ruleForm.validate((function(e){if(!e)return!1;t.$axios.post("/admin/order/backcancel",{id:t.item3.id}).then((function(e){t.applyVisible=!1,t.getList()})).catch((function(t){console.log(t)}))}))},onBack:function(){var t=this;this.$axios.post("/admin/order/back",this.item).then((function(e){t.dialogVisible=!1,t.item={},t.getList()})).catch((function(t){}))},onSave:function(t){var e=this;console.log(this.next_follow),this.$axios.post("/admin/order/save",{id:t.id,check_sn:t.check_sn,is_wechat:t.is_wechat,travel_end:t.travel_end,travel_date:t.travel_date,desc:t.desc,status:t.status,next_follow:this.next_follow,personnel:this.item.personnel}).then((function(t){e.dialogVisible=!1,e.item={next_follow:"",personnel:{}}})).catch((function(t){}))},onPass:function(t){var e=this;this.$axios.post("/admin/order/pass",{check_sn:t.check_sn}).then((function(t){e.dialog2Visible=!1,e.form={}})).catch((function(t){}))},onChange:function(t){this.$set(this.item,"desc",t+(void 0!=this.item.desc?this.item.desc:""))},onChange2:function(t){this.$set(this.item,"to_admin_id",t+(void 0!=this.item.admin_id?this.item.admin_id:""))},handleChange:function(t){console.log(t)},getShortcutContent:function(){var t=this;this.listLoading=!0,this.$axios.get("/admin/shortcutContent/list",{params:{page:1,limit:50,status:1}}).then((function(e){var a,i=Object(s["a"])(e.data.data);try{for(i.s();!(a=i.n()).done;){var l=a.value;t.options.push({value:l.id,label:l.content})}}catch(n){i.e(n)}finally{i.f()}})).catch((function(){}))}}},u=c,d=(a("ff68"),a("8a34")),m=Object(d["a"])(u,i,l,!1,null,"6b2c35e3",null);e["default"]=m.exports},"67f2":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[a("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},l=[],s=(a("374d"),a("09f4")),n={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(s["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(s["a"])(0,800)}}},o=n,r=(a("7d30"),a("8a34")),c=Object(r["a"])(o,i,l,!1,null,"28fdfbeb",null);e["a"]=c.exports},7140:function(t,e,a){},"7d30":function(t,e,a){"use strict";a("7140")},a5ed:function(t,e,a){},f8b7:function(t,e,a){"use strict";a.d(e,"a",(function(){return l})),a.d(e,"c",(function(){return s})),a.d(e,"b",(function(){return n})),a.d(e,"d",(function(){return o}));var i=a("b775");function l(t){return Object(i["a"])({url:"/admin/order/back",method:"post",data:t})}function s(t){return Object(i["a"])({url:"admin/order/backBatch",method:"post",data:t})}function n(t){return Object(i["a"])({url:"/admin/order-xs/back",method:"post",data:t})}function o(t){return Object(i["a"])({url:"admin/order-xs/backBatch",method:"post",data:t})}},ff68:function(t,e,a){"use strict";a("a5ed")}}]);