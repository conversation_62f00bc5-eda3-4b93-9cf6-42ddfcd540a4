<template>
  <div class="app-container">
    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="行程标题"
        style="width: 200px; margin-right: 10px"
        class="filter-item"
        clearable
      />
      <el-input
        v-model="listQuery.destination"
        placeholder="目的地"
        style="width: 150px; margin-right: 10px"
        class="filter-item"
        clearable
      />
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        style="width: 120px; margin-right: 10px"
        class="filter-item"
        clearable
      >
        <el-option label="全部" value="" />
        <el-option label="上架" value="online" />
        <el-option label="下架" value="offline" />
        <el-option label="草稿" value="draft" />
      </el-select>
      <el-select
        v-model="listQuery.productType"
        placeholder="产品类型"
        style="width: 120px; margin-right: 10px"
        class="filter-item"
        clearable
      >
        <el-option label="全部" value="" />
        <el-option label="跟团游" value="group" />
        <el-option label="自由行" value="free" />
        <el-option label="定制游" value="custom" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-circle-plus"
        @click="handleCreate"
      >
        新增行程
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" label="操作" width="240" fixed="left">
        <template slot-scope="scope">
          <div class="button-group">
            <el-button
              type="primary"
              size="mini"
              class="action-button"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="scope.row.status === 'offline' || scope.row.status === 'draft'"
              type="success"
              size="mini"
              class="action-button"
              @click="handleOnline(scope.row)"
            >
              上架
            </el-button>
            <el-button
              v-if="scope.row.status === 'online'"
              type="warning"
              size="mini"
              class="action-button"
              @click="handleOffline(scope.row)"
            >
              下架
            </el-button>
            <el-button
              type="info"
              size="mini"
              class="action-button"
              @click="handleInventory(scope.row)"
            >
              库存
            </el-button>
            <el-button
              type="danger"
              size="mini"
              class="action-button"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="id" label="ID" width="80" align="center" />

      <el-table-column prop="title" label="行程标题" min-width="200">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center;">
            <img
              v-if="scope.row.coverImage"
              :src="scope.row.coverImage"
              style="width: 40px; height: 40px; margin-right: 10px; border-radius: 4px;"
            />
            <div>
              <div>{{ scope.row.title }}</div>
              <div style="color: #999; font-size: 12px;">
                {{ scope.row.days }}天{{ scope.row.nights }}晚
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="destination" label="目的地" width="150" align="center">
        <template slot-scope="scope">
          <span v-if="Array.isArray(scope.row.destination)">
            {{ scope.row.destination.join(', ') }}
          </span>
          <span v-else>{{ scope.row.destination }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="departureCity" label="出发城市" width="150" align="center">
        <template slot-scope="scope">
          <span v-if="Array.isArray(scope.row.departureCity)">
            {{ scope.row.departureCity.join(', ') }}
          </span>
          <span v-else>{{ scope.row.departureCity }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="productType" label="产品类型" width="100" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="getProductTypeTag(scope.row.productType)"
            size="small"
          >
            {{ getProductTypeText(scope.row.productType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="price" label="价格" width="100" align="center">
        <template slot-scope="scope">
          <span style="color: #f56c6c; font-weight: bold;">
            ¥{{ scope.row.price }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="getStatusTag(scope.row.status)"
            size="small"
          >
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="创建时间" width="160" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="updateTime" label="更新时间" width="160" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.updateTime | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import Pagination from "@/components/Pagination";
import {
  getItineraryListApi,
  deleteItineraryApi,
  onlineItineraryApi,
  offlineItineraryApi
} from "@/api/admin";

export default {
  name: "ItineraryManagement",
  components: { Pagination },
  data() {
    return {
      listQuery: {
        page: 1,
        pageSize: 10,
        keyword: '',
        destination: '',
        status: '',
        productType: ''
      },
      total: 0,
      listLoading: true,
      list: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.listLoading = true;
      try {
        const response = await getItineraryListApi(this.listQuery);
        this.list = response.data.list || [];
        this.total = response.data.total || 0;
      } catch (error) {
        console.error('获取行程列表失败:', error);
        this.$message.error('获取数据失败');
      } finally {
        this.listLoading = false;
      }
    },

    // 新增行程
    handleCreate() {
      this.$router.push('/itinerary/create');
    },

    // 编辑行程
    handleEdit(row) {
      this.$router.push(`/itinerary/edit/${row.id}`);
    },

    // 上架行程
    async handleOnline(row) {
      try {
        await onlineItineraryApi(row.id);
        this.$message.success('上架成功');
        this.getList();
      } catch (error) {
        console.error('上架失败:', error);
        this.$message.error('上架失败');
      }
    },

    // 下架行程
    async handleOffline(row) {
      try {
        await offlineItineraryApi(row.id);
        this.$message.success('下架成功');
        this.getList();
      } catch (error) {
        console.error('下架失败:', error);
        this.$message.error('下架失败');
      }
    },

    // 库存管理
    handleInventory(row) {
      this.$router.push(`/itinerary/inventory/${row.id}`);
    },

    // 删除行程
    handleDelete(row) {
      this.$confirm('确认删除该行程吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteItineraryApi(row.id);
          this.$message.success('删除成功');
          this.getList();
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 获取产品类型标签样式
    getProductTypeTag(type) {
      const tagMap = {
        'group': 'success',
        'free': 'info',
        'custom': 'warning'
      };
      return tagMap[type] || '';
    },

    // 获取产品类型文本
    getProductTypeText(type) {
      const textMap = {
        'group': '跟团游',
        'free': '自由行',
        'custom': '定制游'
      };
      return textMap[type] || type;
    },

    // 获取状态标签样式
    getStatusTag(status) {
      const tagMap = {
        'online': 'success',
        'offline': 'warning',
        'draft': 'info'
      };
      return tagMap[status] || '';
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'online': '上架',
        'offline': '下架',
        // 'draft': '草稿'
      };
      return textMap[status] || status;
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
}

.action-button {
  margin: 0 !important;
}
</style>
