import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/home",
    component: () => import("@/views/home/<USER>"),
    hidden: true,
  },
  {
    path: "/show_route",
    component: () => import("@/views/proManagement/route"),
    // component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/line_on_sale",
    component: () => import("@/views/home/<USER>"),
    hidden: true,
  },
  {
    path: "/auth-redirect",
    component: () => import("@/views/login/auth-redirect"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/error-page/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error-page/401"),
    hidden: true,
  },
  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index"),
        name: "Dashboard",
        meta: { title: "系统面板", icon: "dashboard", affix: true },
      },
    ],
  },

];

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: "/system",
    component: Layout,
    redirect: "/system",
    alwaysShow: true,
    name: "System",
    meta: {
      title: "系统管理",
      icon: "el-icon-s-home",
      roles: ["admin", "franchisee"],
    },

    children: [
      {
        path: "admin",
        component: () => import("@/views/admin/index"),
        name: "Admin",
        meta: {
          title: "管理员",
          roles: ["admin", "franchisee"],
        },
      },
      {
        path: "works",
        component: () => import("@/views/admin/works"),
        name: "Works",
        meta: {
          title: "排班表",
          roles: ["admin"],
        },
      },
      {
        path: "scheduling",
        component: () => import("@/views/scheduling/index"),
        name: "scheduling",
        meta: {
          title: "直播排班",
          roles: ["admin"],
        },
      },
      {
        path: "onlines",
        component: () => import("@/views/onlines/online.vue"),
        name: "onlines",
        meta: {
          title: "在线客服",
          roles: ["admin"],
        },
      },
      // {
      //   path: "/system/proManagement",
      //   component: () => import("@/views/proManagement/index"),
      //   name: "ProManagement",
      //   meta: {
      //     title: "商品管理",
      //     roles: ["admin"],
      //   },
      // },

      {
        path: "contract-setting",
        component: () => import("@/views/proManagement/contract-setting"),
        name: "ContractSetting",
        meta: {
          title: "合同管理",
          roles: ["admin"],
        },
      },
      {
        path: "proScheduling",
        component: () => import("@/views/proManagement/scheduling"),
        name: "ProScheduling",
        meta: {
          title: "班期管理",
          roles: ["admin"],
          hidden: true,
        },
      },
      {
        path: "ota-route-group",
        component: () => import("@/views/setting/ota-route-group"),
        name: "OtaRouteGroup",
        meta: {
          title: "OTA线路分组",
          roles: ["admin"],
        },
      },
      /*,
      {
        path: 'shortcut',
        component: () => import('@/views/shortcut/shortcutContent.vue'),
        name: 'shortcut',
        meta: {
          title: '快捷内容设置',
          roles: ['admin']
        }
      },
      {
        path: 'teams',
        component: () => import('@/views/admin/teams'),
        name: 'Teams',
        meta: {
          title: '团队',
          roles: ['admin']
        }
      }*/
    ],
  },
  {
    path: "/",
    component: Layout,
    children: [
      {
        path: "routeManagement",
        component: () => import("@/views/proManagement/route.vue"),
        name: "线路资质管理",
        meta: { title: "线路资质管理", icon: "el-icon-picture-outline-round", noCache: true,roles: ['admin', 'editor'] },
      },
    ],
  },
  {
    path: "/system/proManagement",
    component: Layout,
    redirect: "/system/proManagement",
    children: [
      {
        path: "proManagement",
        component: () => import("@/views/proManagement/index"),
        name: "ProManagement",
        meta: { title: "OTA线路管理", icon: "el-icon-platform-eleme", affix: true,
                   roles: ['admin', 'editor', 'franchisee'],},

      },
    ],
  },
  {
    path: "/system/proManagement-xs",
    component: Layout,
    redirect: "/system/proManagement-xs",
    children: [
      {
        path: "proManagement-xs",
        component: () => import("@/views/proManagement/index-xs"),
        name: "ProManagementXs",
        meta: {
          title: "私域线路管理",
          icon: "el-icon-eleme" ,
          roles: ['admin','isprivate'],
        },
      },
    ],
  },
  {
    path: "/itinerary",
    component: Layout,
    redirect: "/itinerary/index",
    alwaysShow: true,
    name: "ItineraryManagement",
    meta: {
      title: "行程管理",
      icon: "el-icon-map-location",
      roles: ['admin', 'editor'],
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/itinerary/index"),
        name: "ItineraryList",
        meta: {
          title: "行程列表",
          roles: ['admin', 'editor'],
        },
      },
      {
        path: "create",
        component: () => import("@/views/itinerary/form"),
        name: "ItineraryCreate",
        meta: {
          title: "新增行程",
          roles: ['admin', 'editor'],
          hidden: true,
        },
      },
      {
        path: "edit/:id",
        component: () => import("@/views/itinerary/form"),
        name: "ItineraryEdit",
        meta: {
          title: "编辑行程",
          roles: ['admin', 'editor'],
          hidden: true,
        },
      },
      {
        path: "inventory/:id",
        component: () => import("@/views/itinerary/inventory"),
        name: "ItineraryInventory",
        meta: {
          title: "库存管理",
          roles: ['admin', 'editor'],
          hidden: true,
        },
      },
    ],
  },

  {
    path: "/order",
    component: Layout,
    redirect: "/order/index",
    alwaysShow: true,
    name: "Orders",
    meta: {
      title: "订单管理",
      icon: "money",
      roles: ["order_index", "editor", "franchisee"],
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/order/index"),
        name: "OrderList",
        meta: {
          title: "订单列表",
          roles: ["order_pub", "editor", "franchisee"],
        },
      },
      {
        path: 'pub',
        component: () => import('@/views/order/pub'),
        name: 'OrderPub',
        meta: {
          title: '公海订单',
          roles: ['order_pub','editor']
        }
      },
      {
        path: "back",
        component: () => import("@/views/order/back"),
        name: "OrderBack",
        meta: {
          title: "流转订单",
          roles: ["order_back", "editor"],
        },
      },
      {
        path: "abandoned",
        component: () => import("@/views/order/abandoned"),
        name: "OrderBack",
        meta: {
          title: "已放弃订单",
          roles: ["admin"],
        },
      },
      {
        path: "used",
        component: () => import("@/views/order/used"),
        name: "OrderBack",
        meta: {
          title: "已使用订单",
          roles: ["order_back", "editor"],
        },
      },
      {
        path: "refunded",
        component: () => import("@/views/order/refunded"),
        name: "OrderBack",
        meta: {
          title: "已退款订单",
          roles: ["order_back", "editor", "franchisee"],
        },
      },
      // {
      //   path: "appointment",
      //   component: () => import("@/views/order/appointment"),
      //   name: "appointment",
      //   meta: {
      //     title: "预约记录",
      //     roles: ["order_back", "editor"],
      //   },
      // },
      {
        path: "afterSale",
        component: () => import("@/views/order/afterSale"),
        name: "afterSale",
        meta: {
          title: "抖音取消/退款申请",
          roles: ["order_back", "editor"],
        },
      },
    ],
  },
  {
    path: "/order-xs",
    component: Layout,
    redirect: "/order-xs/index",
    alwaysShow: true,
    name: "OrdersXs",
    meta: {
      title: "私域客户管理",
      icon: "user",
      roles: ["order_index", "editor", "franchisee", 'isprivate'],
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/order-xs/index"),
        name: "OrderListXs",
        meta: {
          title: "客户列表",
          roles: ["order_pub", "editor", "franchisee", 'isprivate'],
        },
      },
      // {
      //   path: 'pub',
      //   component: () => import('@/views/order-xs/pub'),
      //   name: 'OrderPubXs',
      //   meta: {
      //     title: '公海客户',
      //     roles: ['order_pub','editor', 'isprivate']
      //   }
      // },
      {
        path: "back",
        component: () => import("@/views/order-xs/back"),
        name: "OrderBack",
        meta: {
          title: "流转客户",
          roles: ["order_back", "editor", 'isprivate'],
        },
      },
      {
        path: "abandoned",
        component: () => import("@/views/order-xs/abandoned"),
        name: "OrderBackXs",
        meta: {
          title: "已放弃客户",
          roles: ["admin", 'isprivate'],
        },
      },
      // {
      //   path: "used",
      //   component: () => import("@/views/order-xs/used"),
      //   name: "OrderBack",
      //   meta: {
      //     title: "已使用客户",
      //     roles: ["order_back", "editor", 'isprivate'],
      //   },
      // },
      // {
      //   path: "refunded",
      //   component: () => import("@/views/order-xs/refunded"),
      //   name: "OrderBackXs",
      //   meta: {
      //     title: "已退款客户",
      //     roles: ["order_back", "editor", "franchisee", 'isprivate'],
      //   },
      // },

      {
        path: "contract",
        component: () => import("@/views/proManagement/contract"),
        name: "ProManagementContract",
        meta: {
          title: "线路资质",
          roles: ["admin", "order_pub", 'isprivate'],
        },
      },
      {
        path: "contract-browse",
        component: () => import("@/views/proManagement/contract-browse"),
        name: "ProManagementContractBrowse",
        meta: {
          title: "线路资质浏览",
          roles: ["admin", "order_pub", 'isprivate'],
        },
      },

      // {
      //   path: "appointment",
      //   component: () => import("@/views/order-xs/appointment"),
      //   name: "appointmentXs",
      //   meta: {
      //     title: "预约记录",
      //     roles: ["order_back", "editor"],
      //   },
      // },
    ],
  },
  {
    path: "/qa",
    component: Layout,
    redirect: "/qa/qa",
    alwaysShow: true,
    name: "Qa",
    meta: {
      title: "QA管理",
      icon: "el-icon-question",
      roles: ["order_index", "editor", "franchisee", 'isprivate'],
    },
    children: [
      {
        path: "problem",
        component: () => import("@/views/qa/problem.vue"),
        name: "problem",
        meta: {
          title: "QA常见问题",
          roles: ["order_pub", "editor", "franchisee", 'isprivate'],
        },
      },
      {
        path: "qa",
        component: () => import("@/views/qa/qa.vue"),
        name: "qa",
        meta: {
          title: "QA管理列表",
          roles: ["admin", 'isprivate'],
        },
      },
      {
        path: "city",
        component: () => import("@/views/qa/city.vue"),
        name: "city",
        meta: {
          title: "城市管理列表",
          roles: ["admin", 'isprivate'],
        },
      },
    ],
  },
  {
    path: "/data",
    component: Layout,
    redirect: "/data/index",
    alwaysShow: true,
    name: "Data",
    meta: {
      title: "数据统计",
      icon: "chart",
      roles: ["data_index"],
    },
    children: [
      {
        path: "product",
        component: () => import("@/views/order/product"),
        name: "productNameList",
        meta: {
          title: "产品统计",
          roles: ["order_pub", "editor"],
        },
      },
      {
        path: "index",
        component: () => import("@/views/data/index"),
        name: "Index",
        meta: {
          title: "跟进统计",
          roles: ["data_index"],
        },
      },
      {
        path: "stats",
        component: () => import("@/views/data/privateStats"),
        name: "PrivateStats",
        meta: {
          title: "私域客户统计",
          roles: ["admin", 'isprivate'],
        },
      },
      /* {
        path: 'online',
        component: () => import('@/views/data/online'),
        name: 'Index',
        meta: {
          title: '在线时长',
          roles: ['data_online']
        }
      },
      {
        path: 'anchor',
        component: () => import('@/views/data/anchor'),
        name: 'Anchor',
        meta: {
          title: '主播概况',
          roles: ['data_anchor']
        }
      },*/
      {
        path: "sale",
        component: () => import("@/views/data/sale"),
        name: "Sale",
        meta: {
          title: "销售统计",
          roles: ["data_sale"],
        },
      },
    ],
  },
  {
    path: "/log",
    component: Layout,
    redirect: "/log/index",
    alwaysShow: true,
    name: "Log",
    meta: {
      title: "日志记录",
      icon: "nested",
      roles: ["follow_index", "log_index", "editor", "franchisee"],
    },
    children: [
      {
        path: "follow",
        component: () => import("@/views/log/follow"),
        name: "Follow",
        meta: {
          title: "跟进记录",
          roles: ["follow_index", "editor", "franchisee"],
        },
      },
      {
        path: "index",
        component: () => import("@/views/log/index"),
        name: "LogIndex",
        meta: {
          title: "日志记录",
          roles: ["log_index"],
        },
      },
      {
        path: "order-allocate",
        component: () => import("@/views/log/order-allocate"),
        name: "OrderAllocateLog",
        meta: {
          title: "订单分配记录",
          roles: ["admin", "editor", "franchisee"],
        },
      },
    ],
  },
  {
    path: "/announcements",
    component: Layout,
    redirect: "/announcements/index",
    alwaysShow: true,
    name: "announcements",
    meta: {
      title: "公告管理",
      icon: "el-icon-s-promotion",
      roles: ["admin"],
    },
    children: [
      {
        path: "list",
        component: () => import("@/views/announcements/list"),
        name: "list",
        meta: {
          title: "公告列表",
          roles: ["admin"],
        },
      },
    ],
  },
  // {
  //   path: "/icon",
  //   component: Layout,
  //   children: [
  //     {
  //       path: "index",
  //       component: () => import("@/views/icons/index"),
  //       name: "Icons",
  //       meta: { title: "Icons", icon: "icon", noCache: true },
  //     },
  //   ],
  // },
  // 404 page must be placed at the end !!!
  { path: "*", redirect: "/404", hidden: true },
];

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
