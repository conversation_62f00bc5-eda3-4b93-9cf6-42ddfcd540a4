/*
import axios from 'axios';

const service = axios.create({
    baseURL: process.env.REACT_APP_API_URL,
    timeout: 60000,//请求超时时间
})

//请求拦截器
service.interceptors.request.use(
    config => {
        const token = localStorage.getItem('token');
        if (token){
            config.headers['Authorization'] = 'Bearer ' + token;
        }
        return config;
    },
    error => {
        console.log(error)
        return Promise.reject(error);
    }
)

export default service;*/
