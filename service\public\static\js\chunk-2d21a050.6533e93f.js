(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21a050"],{ba72:function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"管理员用户名"},model:{value:t.listQuery.admin,callback:function(e){t.$set(t.listQuery,"admin",e)},expression:"listQuery.admin"}}),a("el-date-picker",{staticClass:"filter-item",attrs:{type:"datetimerange","default-time":["00:00:00","23:59:59"],"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.listQuery.times,callback:function(e){t.$set(t.listQuery,"times",e)},expression:"listQuery.times"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v(" 搜索 ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{fixed:"",label:"日期",width:"120",prop:"date"}}),a("el-table-column",{attrs:{fixed:"",label:"姓名",width:"120",prop:"admin.name"}}),a("el-table-column",{attrs:{label:"管理员",width:"120",prop:"admin.username"}}),a("el-table-column",{attrs:{label:"订单数",width:"120",prop:"orders"}}),a("el-table-column",{attrs:{label:"订单总金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.order_amount/100)+" ")]}}])}),a("el-table-column",{attrs:{label:"核销数",width:"120",prop:"assets"}}),a("el-table-column",{attrs:{label:"核销金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.asset_amount/100)+" ")]}}])}),a("el-table-column",{attrs:{label:"退款订单",width:"120",prop:"refunds"}}),a("el-table-column",{attrs:{label:"退款金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.refund_amount/100)+" ")]}}])}),a("el-table-column",{attrs:{label:"已出行订单",width:"120",prop:"travels"}}),a("el-table-column",{attrs:{label:"已出行金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.travel_amount/100)+" ")]}}])})],1)],1)},s=[],i={name:"Datalist",data:function(){return{oss:null,list:[],listLoading:!0,listQuery:{}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.$axios.get("/admin/data/sale",{params:this.listQuery}).then((function(e){t.list=e.data.data,t.listLoading=!1}))}}},n=i,r=a("8a34"),o=Object(r["a"])(n,l,s,!1,null,null,null);e["default"]=o.exports}}]);