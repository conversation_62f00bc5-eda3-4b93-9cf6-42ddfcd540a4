<?php

namespace app\admin\controller;

use app\model\Admins;
use app\model\LiveRoom;
use app\model\LiveRoomWorks;
use app\model\Orders;
use app\model\Works;
use support\Request;

class LiveRoomController extends base {
    /**
     * 直播间列表
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index(Request $request) {
        $all = LiveRoom::select();
        return $this->success($all);
    }

    /**
     * 更新直播产品
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function saveProducts(Request $request) {
        $roomId = $request->post('live_room_id', '');
        if (empty($roomId)) {
            $this->error('请选择直播间');
        }
        $products = $request->post('product_ids', '');
        $productIds = explode(',', $products);
        foreach ($productIds as $productId) {
            if (empty($productId)) {
                continue;
            }
            $liveRoom = LiveRoom::whereRaw(sprintf('find_in_set(%s, `product_ids`)', $productId))->where('id', '!=', $roomId)->find();
            if (!empty($liveRoom)) {
                return $this->error(sprintf('产品ID:%s已经在%s存在', $productId, $liveRoom['name']));
            }
        }
        LiveRoom::where(['id' => $roomId])->update(['product_ids' => $products]);
        return $this->success([]);
    }

    /**
     * 排班列表
     * @param Request $request
     * @return \support\Response
     */
    public function roomWorks(Request $request)
    {
        $roomId = $request->get('live_room_id', '');
        if (!$roomId) {
            return $this->error('请选择直播间');
        }
        $weekDays = get_week_days();
        foreach ($weekDays as &$day) {
            $day['work'] = LiveRoomWorks::getDayWork($day['date'], $roomId);
        }

        return $this->success($weekDays);
    }

    /**
     * 排班
     * @param Request $request
     * @return \support\Response
     */
    public function saveRoomWorks(Request $request)
    {
        $id = $request->post('id', '0');
        $roomId = $request->post('live_room_id', '');
        $zhuboId = $request->post('zhubo_id', 0);
        $zhongkongId = $request->post('zhongkong_id', 0);
        $route = $request->post('route', '');
        $start = $request->post('start');
        $end = $request->post('end');
        $month = date('Ym', strtotime($start));
        $day = date('Ymd', strtotime($start));

        if (strtotime($start) < time()) {
            return $this->error(6000, '当前时间不可排班');
        }

        if (strtotime($start) >= strtotime($end)) {
            return $this->error(6000, '开始时间不可大于结束时间');
        }

        // 检测当前日期是否已有排班
        if (LiveRoomWorks::dayWorkIsExists($id, $roomId, $start, $end)) {
            return $this->error(6001, '当前时间段已有排班');
        }
        // 检测当前主播当天是否已排班
        if (LiveRoomWorks::zhuboWorkIsExists($id, $zhuboId, $day)) {
            return $this->error(6002, '当前主播已排班');
        }

        $data = [
            'live_room_id' => $roomId,
            'zhubo_id' => $zhuboId,
            'zhongkong_id' => $zhongkongId,
            'start' => $start,
            'end' => $end,
            'month' => $month,
            'day' => $day,
            'route' => $route,
        ];
        if ($id) {
            $result = LiveRoomWorks::where('id', $id)->update($data);
        } else {
            $result = LiveRoomWorks::create($data);
        }
        return $this->success($result);
    }

    /**
     * 可排班主播列表
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DbException
     */
    public function availableZhubo(Request $request) {
        $type = $request->get('type', 2);
        $id = $request->get('id', 0);
        $start = $request->get('start');
        if (empty($start)) {
            return $this->error('请选择时间');
        }
        $end = $request->get('end');
        $day = date('Ymd', strtotime($start));

        $query = Admins::where('status', 1)->where('type', $type);
        $list = $query->field(['id', 'name', 'mobile'])->select();

        // 获取已排班主播
        $adminIds = LiveRoomWorks::inWorkAdmin($day)->column('zhubo_id');
        foreach ($list as $val) {
            $val->disabled = false;
            if ($type == 2 && in_array($val->id, $adminIds)) {
                $val->disabled = true;
            }
        }

        return $this->success($list);
    }

    /**
     * 主播业绩查询
     * @param Request $request
     * @return \support\Response
     */
    public function zhuboStatistics(Request $request)
    {
        $start = $request->get('start', date('Y-m-01'));
        $end = $request->get('end', date('Y-m-d'));
        $times = $request->get('times');
        if (!empty($times) && is_array($times)) {
            $start = $times[0];
            $end = $times[1];
        }
        // 姓名，职务，手机号，订单金额，直播时长
        $mobile = $request->get('mobile', '');
        $query = Admins::whereIn('type', [2, 3])->where('shop_id', $request->admin->shop_id);
        if (!empty($mobile)) {
            $query->where('mobile', $mobile);
        }

        $list = $query->order('id', 'desc')->paginate($request->get('limit', 10));
        foreach ($list as $work) {
            $work->orders =  0;
            $work->total = 0;
            $work->asset_total =  0;
            $work->work_time = 0;

            $statics = LiveRoomWorks::staticOrder($work->type, $work->id, $start, $end);
            if ($statics) {
                $work->orders = $statics->orders;
                $work->total = $statics->total/100;
                $work->asset_total = $statics->asset_total/100;
                $work->work_time = $statics->work_time;
            }
        }
        return $this->success($list);
    }

    public function test() {
        $data = LiveRoomWorks::staticOrder(3, 38, '2024-09-17 16:00:00', '2024-09-18 15:59:59');
        return $this->success($data);
    }
}
