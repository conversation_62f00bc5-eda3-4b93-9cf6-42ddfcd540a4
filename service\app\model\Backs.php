<?php
namespace app\model;

use think\facade\Db;

class Backs extends base
{
    public function into() {
        return $this->belongsTo(Admins::class, 'admin_id')->visible(['name','username','avatar']);
    }

    public function outto() {
        return $this->belongsTo(Admins::class, 'admin')->visible(['name','username','avatar']);
    }

    public function apply() {
        return $this->belongsTo(Admins::class, 'apply_id')->visible(['name','username','avatar']);
    }

    public function orders(){
        return $this->belongsTo(Orders::class, 'order_id');
    }

    public static function change(Backs $item) {
        Db::transaction(function() use ($item) {
            Backs::where('id', $item->id)->where('status', 0)->update(['status' => 1]);
            if ($item->is_private == 1) {
                OrdersXs::where('id', $item->order_id)->where('admin_id', $item->admin)->update(['admin_id'=> $item->admin_id]);
            } else {
                Orders::where('id', $item->order_id)->where('admin_id', $item->admin)->update(['admin_id'=> $item->admin_id]);
            }
            Logs::todo($item->order_id, $item->admin, 8, null, $item->is_private); //转单
            $other = Backs::where('order_id', $item->order_id)->where('status', 0)->lock()->select();
            foreach($other as $o) {
                Backs::where('id', $o->id)->where('status', 0)->update(['status' => 3]);
                Logs::todo($o->order_id, $o->admin, 10, null, $item->is_private); //取消其他转单需求
            }
        });
    }

    public static function refuse(Backs $item) {
        Db::transaction(function() use ($item) {
            Backs::where('id', $item->id)->where('status', 0)->update(['status' => 2]);
            Logs::todo($item->order_id, $item->admin, 9, null, $item->is_private); //拒绝请求
        });
    }

    public static function cancel(Backs $item) {
        Db::transaction(function() use ($item) {
            Backs::where('id', $item->id)->where('status', 0)->update(['status' => 3]);
            Logs::todo($item->order_id, $item->admin, 10, null, $item->is_private); //拒绝请求
        });
    }
}
