<?php
namespace app\admin\controller;

use app\model\Admins;
use app\model\Orders;
use app\model\Logs;
use support\Request;

class LogController extends base
{
    public function Index(Request $request) {

        $admin = $request->get('admin');
        $sn = $request->get('sn');
        $times = $request->get('times');

        $query = Logs::with(['admin','orders'])
            ->join('orders', 'orders.id = logs.order_id')
            ->where('orders.shop_id', $request->admin->shop_id)
            ->order('logs.id', 'desc');

        if(!empty($admin)){
            $admin_id = Admins::where('username', $admin)->value('id');
            $query->where('logs.admin_id', $admin_id ?? 0);
        }

        if(!empty($sn)) {
            $order_id = Orders::where('sn', $sn)->value('id');
            $query->where('logs.order_id', $order_id ?? 0);
        }

        if(!empty($times) && count($times)) {
            $query->whereBetween('logs.create_time', [strtotime($times[0]),strtotime($times[1])]);
        }

        $list = $query->paginate($request->get('limit', 30));

        return $this->success($list->append(['action_name']));
    }
}
