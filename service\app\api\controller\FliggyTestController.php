<?php
declare(strict_types=1);

namespace app\api\controller;

use app\server\FliggySignatureService;
use support\Request;
use support\Response;
use Webman\App;

/**
 * 飞猪测试控制器
 */
final class FliggyTestController extends base
{
    public Request $request;
    public function __construct(){
        $this->request = App::request();
    }

    /**
     * 测试签名生成
     */
    public function testSignature(Request $request): Response
    {
        $appKey = 'test-ak1';
        $appSecret = 'test-ak1-password';
        $httpMethod = 'POST';
        $url = '/api/fliggy/create-order';
        $contentString = json_encode([
            "success" => "true",
            "code" => 200,
            "message" => "ok",
            "data" => [
                "testData" => "testData"
            ]
        ]);
        $timestamp = time() * 1000;
        $nonce = uniqid();
        $optionsKeysString = implode("\n", ["key1:value1", "key2:value2"]);

        $signature = FliggySignatureService::generateSignature(
            $appKey,
            $appSecret,
            $httpMethod,
            $url,
            $contentString,
            $timestamp,
            $nonce,
            $optionsKeysString
        );

        return new Response(200, ['Content-Type' => 'application/json'], json_encode([
            'appKey' => $appKey,
            'timestamp' => $timestamp,
            'nonce' => $nonce,
            'signature' => $signature,
            'contentString' => $contentString,
            'optionsKeysString' => $optionsKeysString,
            'headers' => [
                'x-app-key' => $appKey,
                'x-timestamp' => (string)$timestamp,
                'x-nonce' => $nonce,
                'x-signature' => $signature,
                'x-options-keys' => $optionsKeysString,
                'Content-Type' => 'application/json'
            ]
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 测试创建订单
     */
    public function testCreateOrder(Request $request): Response
    {
        $testData = [
            "bizType" => 2,
            "fliggyOrderId" => "TEST" . time(),
            "vendor" => [
                "type" => "test-vendor",
                "env" => "sandbox",
                "extend" => [
                    "apiKey" => "test-key",
                    "apiSecret" => "test-secret"
                ]
            ],
            "contacts" => [
                "mobile" => "13800138000",
                "email" => "<EMAIL>"
            ],
            "travellers" => [
                [
                    "certificateType" => 0,
                    "certificateId" => "110101199001011234",
                    "name" => "张三",
                    "firstName" => "三",
                    "lastName" => "张",
                    "mobile" => "13800138000",
                    "mobilePrefix" => "+86",
                    "sex" => "M",
                    "email" => "<EMAIL>",
                    "country" => "CN",
                    "birth" => "1990-01-01",
                    "type" => 0
                ]
            ],
            "adultNumber" => 1,
            "childNumber" => 0,
            "productList" => [
                [
                    "productId" => "PROD001",
                    "quantity" => 1,
                    "totalPrice" => 100000,
                    "price" => 100000,
                    "currencyType" => "CNY",
                    "travelDate" => "2024-12-25",
                    "crowdTypeId" => "ADULT",
                    "totalCouponPrice" => 0,
                    "totalActualPrice" => 100000
                ]
            ],
            "confirmTime" => "2024-10-15 13:00:00"
        ];

        return new Response(200, ['Content-Type' => 'application/json'], json_encode([
            'message' => '测试创建订单数据',
            'data' => $testData,
            'curl_example' => $this->generateCurlExample('/api/fliggy/create-order', $testData)
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 测试查询订单
     */
    public function testQueryOrder(Request $request): Response
    {
        $testData = [
            "vendor" => [
                "type" => "test-vendor",
                "env" => "sandbox",
                "extend" => [
                    "apiKey" => "test-key",
                    "apiSecret" => "test-secret"
                ]
            ],
            "bizType" => 2,
            "fliggyOrderId" => "TEST123456789",
            "orderId" => "FLG20241015000001"
        ];

        return new Response(200, ['Content-Type' => 'application/json'], json_encode([
            'message' => '测试查询订单数据',
            'data' => $testData,
            'curl_example' => $this->generateCurlExample('/api/fliggy/query-order', $testData)
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 测试退款
     */
    public function testRefund(Request $request): Response
    {
        $testData = [
            "bizType" => 2,
            "fliggyOrderId" => "TEST123456789",
            "orderId" => "FLG20241015000001",
            "vendor" => [
                "type" => "test-vendor",
                "env" => "sandbox",
                "extend" => [
                    "apiKey" => "test-key",
                    "apiSecret" => "test-secret"
                ]
            ],
            "refundType" => 1,
            "refundReason" => "用户主动取消",
            "refundFee" => 100000
        ];

        return new Response(200, ['Content-Type' => 'application/json'], json_encode([
            'message' => '测试退款数据',
            'data' => $testData,
            'curl_example' => $this->generateCurlExample('/api/fliggy/refund-order', $testData)
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 测试修改订单
     */
    public function testUpdateOrder(Request $request): Response
    {
        $testData = [
            "vendor" => [
                "type" => "test-vendor",
                "env" => "sandbox",
                "extend" => [
                    "apiKey" => "test-key",
                    "apiSecret" => "test-secret"
                ]
            ],
            "fliggyOrderId" => "TEST123456789",
            "orderId" => "FLG20241015000001",
            "travelDate" => "2024-12-26",
            "travellers" => [
                [
                    "certificateType" => 0,
                    "certificateId" => "110101199001011234",
                    "name" => "李四",
                    "firstName" => "四",
                    "lastName" => "李",
                    "mobile" => "13800138001",
                    "mobilePrefix" => "+86",
                    "sex" => "F",
                    "email" => "<EMAIL>",
                    "country" => "CN",
                    "birth" => "1990-01-01",
                    "type" => 0
                ]
            ],
            "bizType" => 2
        ];

        return new Response(200, ['Content-Type' => 'application/json'], json_encode([
            'message' => '测试修改订单数据',
            'data' => $testData,
            'curl_example' => $this->generateCurlExample('/api/fliggy/update-order', $testData)
        ], JSON_UNESCAPED_UNICODE));
    }

    /**
     * 生成curl测试示例
     */
    private function generateCurlExample(string $endpoint, array $data): string
    {
        $appKey = 'test-ak1';
        $appSecret = 'test-ak1-password';
        $httpMethod = 'POST';
        $contentString = json_encode($data);
        $timestamp = time() * 1000;
        $nonce = uniqid();
        $optionsKeysString = '';

        $signature = FliggySignatureService::generateSignature(
            $appKey,
            $appSecret,
            $httpMethod,
            $endpoint,
            $contentString,
            $timestamp,
            $nonce,
            $optionsKeysString
        );

        $host = $this->request->host();
        $protocol = $this->request->connection->transport === 'ssl' ? 'https' : 'http';

        return "curl -X POST '{$protocol}://{$host}{$endpoint}' \\
  -H 'Content-Type: application/json' \\
  -H 'x-app-key: {$appKey}' \\
  -H 'x-timestamp: {$timestamp}' \\
  -H 'x-nonce: {$nonce}' \\
  -H 'x-signature: {$signature}' \\
  -H 'x-options-keys: {$optionsKeysString}' \\
  -d '{$contentString}'";
    }
}
