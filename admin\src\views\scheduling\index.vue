<template>
  <div class="app-container">
    <div class="scheduling_head">
      <div class="head_lable">产品编号:</div>
      <div
        class="scheduling_tab"
        @click="handleClick(index)"
        :key="index"
        :class="activeName == item.id ? 'active' : ''"
        v-for="(item, index) in liveroomList"
      >
        {{ item.name }}
      </div>
      <el-button type="primary" style="margin-left: 210px" @click="handleEdit"
        >编辑</el-button
      >
    </div>
    <div class="scheduling_tab_pana">
      <div class="table-list" v-for="item in roomWorksList">
        <div class="table-list_item">
          <div class="tit">
            {{ item.date }}
          </div>
        </div>
        <div class="table-list_item">
          <div class="tit">
            {{ item.date_name
            }}<i
              @click="handleAdds('', item.date)"
              class="el-icon-circle-plus"
            ></i>
          </div>
        </div>
        <div class="table-list-box" v-for="it in item.work">
          <div class="border" @dblclick="handleAdds(it, item.date)">
            <div style="margin-bottom: 10px">
              AM {{ extractTime(it.start) }}~{{ extractTime(it.end) }}
            </div>
            <div>主播：{{ it.zhubo.name }}</div>
            <div>中控：{{ it.zhongkong.name }}</div>
            <div>路线：{{ it.route ? it.route : "无" }}</div>
          </div>
        </div>
      </div>
    </div>
    <liveroom-update
      v-if="addOrUpdateVisible"
      ref="addOrUpdate"
      @update="saveProducts"
    ></liveroom-update>
    <roomWorksUpdate
      v-if="roomVisible"
      ref="roomUpdate"
      :title="title"
      @update="saveRoomWorks"
    ></roomWorksUpdate>
  </div>
</template>

<script>
import {
  liveroom,
  roomWorks,
  availableZhubo,
  saveProducts,
  saveRoomWorks,
} from "@/api/scheduling";
import liveroomUpdate from "./liveroom-update.vue";
import roomWorksUpdate from "./room-works";
export default {
  name: "Scheduling",
  data() {
    return {
      list: [],
      activeName: "",
      listLoading: true,
      title: "添加排班",
      listQuery: {
        page: 1,
        limit: 20,
        title: "",
        status: "",
      },
      total: 0,
      dialogVisible: false,
      dialogType: "new",
      liveroomList: [],
      roomWorksList: [],
      product_ids: "",
      live_room_id: "",
      addOrUpdateVisible: false,
      roomVisible: false,
    };
  },
  components: {
    liveroomUpdate,
    roomWorksUpdate,
  },
  async mounted() {
    await this.liveroom();
    this.roomWorks();
  },
  methods: {
    extractTime(dateTimeStr) {
      // 定义正则表达式来匹配时间部分
      const regex = /^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/;
      const match = dateTimeStr.match(regex);

      if (!match) {
        throw new Error("不匹配");
      }

      // 提取时间部分
      return `${match[4]}:${match[5]}:${match[6]}`;
    },
    isCurrentTimeAfter(startTimeStr) {
      // 将开始时间字符串转换为Date对象
      const startTime = new Date(startTimeStr);

      // 获取当前时间
      const currentTime = new Date();

      // 比较当前时间和开始时间
      return currentTime >= startTime;
    },
    // 直播间列表
    async liveroom() {
      try {
        let { data } = await liveroom();
        this.liveroomList = data;
        this.activeName = this.liveroomList[0].id + "";
        this.live_room_id = this.liveroomList[0].id;
        this.product_ids = this.liveroomList[0].product_ids;
      } catch (err) {
        console.log(err);
      }
    },
    // 排班列表
    async roomWorks() {
      try {
        let { data } = await roomWorks(this.live_room_id);
        this.roomWorksList = data;
      } catch (err) {
        console.log(err);
      }
    },
    // tab切换
    handleClick(i) {
      let { id, product_ids } = this.liveroomList[i];
      this.live_room_id = id;
      this.product_ids = product_ids;
      this.activeName = id + "";
      this.roomWorks();
    },
    // 编辑直播间
    handleEdit() {
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs["addOrUpdate"].init(this.live_room_id, this.product_ids);
      });
    },
    // 添加排班
    handleAdds(form, date) {
      console.log(form);

      this.title = form.id ? "编辑排班" : "添加排班";
      if (form.id && this.isCurrentTimeAfter(form.start)) {
        return this.$message.error("当前时间无法编辑");
      }
      this.roomVisible = true;
      this.$nextTick(() => {
        this.$refs["roomUpdate"].init(this.live_room_id, { ...form }, date);
      });
    },
    saveProducts(form) {
      this.liveroom();
      saveProducts(form).then((res) => {
        this.$message({
          message: "直播间编辑成功",
          type: "success",
        });
      });
    },
    saveRoomWorks(form) {
      saveRoomWorks(form).then((res) => {
        this.roomWorks();
        this.$message({
          message: "添加排班成功",
          type: "success",
        });
      });
    },
    handleAdd() {},
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}
::v-deep .el-tabs__nav-wrap::after {
  display: none;
}
.filter-container,
.el-table {
  padding-bottom: 5px; /* 分页条的高度，以避免内容重叠 */
}
::v-deep .el-row {
  height: 50px;
  line-height: 50px;
}
.head_lable {
  font-size: 14px;
  color: #3d3d3d;
  font-weight: 700;
  margin-right: 10px;
}
.scheduling_head {
  display: flex;
  align-items: center;
  margin-bottom: 28px;
}
.scheduling_tab {
  display: inline-block;
  padding: 12px 28px;
  font-size: 16px;
  border: 1px solid #e2e2e2;
  border-radius: 5px;
  cursor: pointer;
  & + .scheduling_tab {
    margin-left: 10px;
  }
}
.active {
  border-color: #1269ff;
}
.el-icon-circle-plus {
  color: #367af0;
  font-size: 16px;
}
.scheduling_tab_pana {
  display: flex;
  justify-content: space-between;
  //   align-items: center;
  .table-list {
    flex-basis: 14.3%;
    .table-list_item {
      height: 56px;
      background: #e4ecff;
      display: flex;
      //   justify-content: center;
      align-items: center;
      border: 1px solid #d7ddeb;
      margin-right: -1px;
      margin-bottom: -1px;
      .tit {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
      }
    }
    .table-list-box {
      padding: 15px;
      font-size: 14px;
      color: #ffffff;
      border: 1px solid #d7ddeb;
      margin-right: -1px;
      margin-bottom: -1px;
      .border {
        padding: 13px;
        background: #367af0;
        cursor: pointer;
      }
    }
  }
}
</style>
