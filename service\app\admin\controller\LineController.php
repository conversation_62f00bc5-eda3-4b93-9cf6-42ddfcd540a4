<?php

namespace app\admin\controller;

use app\model\Contract;
use app\model\Line;
use app\model\Orders;
use app\model\OrdersXs;
use app\model\Products;
use support\Request;

class LineController extends base
{
    public function list(Request $request)
    {

        $title = $request->get('title');
        $limit = $request->get('limit', 10);
        $status = $request->get('status', 1);

        $list = Products::query()
                ->where('is_private',1)
                ->where('shop_id', $request->admin->shop_id)
                ->when(!empty($title), function ($query) use ($title) {
                    $query->where('product_name', 'like', '%'.$title.'%');
                })
            ->when(!empty($status), function ($query) use ($status) {
                $query->where('status',  $status);
            })
            ->paginate($limit);



        return $this->success($list);


    }

    public function save(Request $request)
    {
        $id = $request->post('id','');
        $post = $request->post();
        if($id){
            $line = Line::find($id);
        }else{
            $line = new Line();
            if(empty($post['title'])){
                return $this->error(2001, '线路名称不能为空');
            }
        }
        if(!empty($post['image'])){
            $line->image = $post['image'];
        }

        if(!empty($post['title'])){
            $line->title = $post['title'];
        }

        if(isset($post['status'])){
            $line->status = $post['status'];
        }
        $line->shop_id = $request->admin->shop_id;

        $line->save();

        return $this->success(null);
    }

    public function addOrder(Request $request)
    {
        $post = $request->post();
        $remark = isset($post['remark'])?$post['remark']:'';
        try{
            if(empty($post['product_id'])){
                throw new \Exception('请选择产品');
            }
            $product = Products::find($post['product_id']);
            if(empty($product)){
                throw new \Exception('产品不存在');
            }
            if(empty($post['mobile'])){
                // throw new \Exception('请输入手机号');
            }
            if(empty($post['level'])){
                throw new \Exception('等级不能为空');
            }

            if(empty($post['source'])){
                throw new \Exception('来源不能为空');
            }
            $order = new OrdersXs();
            $order->sn = getTradeNo('XS');
            $order->product_id = $product->third_product_id;
            $order->product_name = $product->product_name;
            $order->os = 21;
            $order->category_desc = 0;
            $order->wechat = $post['wechat'];
            $order->create_at = time().'000';
            $order->mobile = $post['mobile'];
            $order->remark = $remark;
            $order->level = $post['level'];
            $order->os = $post['source'];
            $order->evidence_image = isset($post['evidence_image'])?$post['evidence_image']:'';
            $order->fans_status = 1;
            $order->shop_id = $request->admin->shop_id;
            $order->create_admin_id = $request->admin->id;
            if ($request->admin->is_super != 1 && $request->admin->is_private == 1) {
                $order->admin_id = $request->admin->id;
            }


            $back = $order->save();
            return $this->success(null);
        }catch (\Exception $e){
            return $this->error(2001, $e->getMessage());
        }
    }

    public function parameter()
    {
        $levels = Line::LEVELS_MAP;
        $source = Line::SOURCE_MAP;

        $data = [
            'levels' => $levels,
            'source' => $source,
        ];
        return $this->success($data);
    }

    public function contract(Request $request)
    {
        $title = $request->get('title');
        $product_name = $request->get('product_name');

        $limit = $request->get('limit', 10);
        $list = Contract::with(['product'])
            ->where('shop_id', $request->admin->shop_id)
            ->when(!empty($title), function ($query) use ($title) {
                $query->where('title', 'like', '%'.$title.'%');
            })
            ->when(!empty($product_name), function ($query) use ($product_name) {
                $product_ids = Products::whereLike('product_name', "%{$product_name}%")->column('id');
                $query->whereIn('product_id', $product_ids);
            })

            ->paginate($limit);



        return $this->success($list);
    }

    public function saveContract(Request $request)
    {
        $id = $request->post('id',0);
        $post = $request->post();
//        try{
            if(empty($post['title'])){
                throw new \Exception('标题不能为空');
            }
            if($id){
                $contract = Contract::find($id);
            }else{
                $contract = new Contract();
            }
            $data = [
                'title' => $post['title'],
                'product_id' => $post['product_id'],
                'ye_license' => $post['ye_license'],
                'jy_license' => $post['jy_license'],
                'pay_qr' => $post['pay_qr'],
                'hotel_pictures' => $post['hotel_pictures'],
                'restaurant_picture' => $post['restaurant_picture'],
                'shop_id' => $request->admin->shop_id,
            ];


            if($id){
                $data['id'] = $id;
            }


            $contract->save($data);
            return $this->success([]);
//        }catch (\Exception $e){
//            return $this->error(2001, $e->getMessage());
//        }


    }

    public function copyContract(Request $request)
    {
        $id = $request->post('id',0);
        $contract = Contract::find($id)?->toArray();
        if(empty($contract)){
            return $this->error(2001, '合同不存在');
        }
        $contract['title'] = $contract['title'].' 副本';
        unset($contract['id']);
        Contract::save($contract);
        return $this->success([]);
    }

    public function deleteContract(Request $request){
        $id = $request->post('id');
        Contract::where('id',$id)->delete();
        return $this->success([]);

    }

    public function reddot(Request $request)
    {
        $time = time() - 86400 * 7;
        $order_num = Orders::where('admin_id', $request->admin->id)
            ->where('shop_id','=',$request->admin->shop_id)
            ->where('create_time','>',$time)
            ->where(function ($query){
                $query->where('order_status', 0)->whereOr(function ($query){
                    $query->whereIn('order_status', [1,2])->where('next_follow','<',time() * 1000);
                });
            })
            ->count();
        $private_num = OrdersXs::where('admin_id', $request->admin->id)
            ->where('shop_id','=',$request->admin->shop_id)
            ->where('create_time','>',$time)
            ->where(function ($query){
                $query->where('order_status', 0)->whereOr(function ($query){
                    $query->whereIn('order_status', [1,2])->where('next_follow','<',time() * 1000);
                });
            })
            ->count();
        $data = [
            'order_num' => $order_num,
            'private_num' => $private_num,
        ];
        return $this->success($data);
    }


}
