<?php

namespace app\admin\controller;

use app\model\Admins;
use app\model\Uploads;
use app\model\Backs;
use app\model\Finances;
use app\model\Onlines;
use app\model\Orders;
use DateTime;
use Qiniu\Auth;
use support\Log;
use support\Redis;
use support\Request;

class UploadController extends base
{
    public function index(Request $request)
    {
        $file = $request->file("file");

        if (!$file || !$file->isValid()) {
            return $this->error(400, '文件上传失败');
        }

        $ext = $file->getUploadExtension();
        $allowedExts = ['jpg', 'jpeg', 'png', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'pdf'];
        if (!in_array(strtolower($ext), $allowedExts)) {
            return $this->error(400, '不支持的文件类型');
        }

        $maxSize = 10 * 1024 * 1024; // 10MB
        if ($file->getSize() > $maxSize) {
            return $this->error(400, '文件大小不能超过10MB');
        }

        $now = date_create();
        $savepath = sprintf("/uploads/%d/%s/%s", $request->admin->id, $now->format("YmdHisu"), $file->getUploadName());
        $uploadDir = public_path() . dirname($savepath);

        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $item = new Uploads();
        $item->admin_id = $request->admin->id;
        $item->filesize = $file->getSize();
        $item->filepath = $savepath;
        $item->mime = $file->getUploadMimeType();
        $item->create_at = $now->getTimestamp();

        $file->move(public_path() . $savepath);
        $item->save();

        return $this->success($savepath);
    }
}
