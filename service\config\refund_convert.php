<?php

/**
 * 退款转私域配置
 */
return [
    // 是否启用退款转私域功能
    'enabled' => env('REFUND_CONVERT_ENABLED', true),

    // 退款转私域的私域订单来源标识
    'private_os_code' => 21,
    // 退款转私域的私域订单来源标识
    'private_product_id' => '70712049314676',

    // 退款转私域的私域订单默认等级
    'default_level' => 2,

    // 退款转私域的私域订单默认状态
    'default_status' => 0, // 待跟进

    // 是否检查重复创建（同一客户同一产品30天内）
    'check_duplicate' => true,

    // 重复检查的天数
    'duplicate_check_days' => 30,

    // 是否发送短信通知给分配的客服
    'notify_admin' => false,

    // 是否记录详细日志
    'enable_logging' => true,

    // 过滤条件
    'filters' => [
        // 是否过滤黑名单手机号
        'filter_blacks' => true,

        // 是否过滤刷单手机号
        'filter_fake_mobiles' => true,

        // 是否需要有效手机号
        'require_mobile' => true,
    ],

    // 支持的退款状态映射
    'refund_status_map' => [
        1 => 5, // 美团(甄选)：退款状态为 5
        7 => 5, // 美团(新国旅)：退款状态为 5
        3 => 4, // 抖音(甄选)：退款状态为 4
        5 => 4, // 抖音(新国旅)：退款状态为 4
        2 => 1, // 快手：退款状态为 1 (已取消)
        // 6 => ?, // 同程(视频号)：待定义
        // 8 => ?, // 同程(抖音)：待定义
    ]
];
