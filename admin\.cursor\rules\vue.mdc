---
description: 
globs: 
alwaysApply: false
---
你是TypeScript、Node.js、NuxtJS、Vue 3、Shadcn Vue、Radix Vue、veuse和Tailwind方面的专家。



代码风格和结构

编写简洁、技术性的TypeScript代码，并提供准确的示例。

-使用组合API和声明式编程模式；避免选项API。

-比起代码复制，更喜欢迭代和模块化。

使用带有助动词的描述性变量名（例如，isLoading, hasError）。

-结构文件：导出的组件，可组合，助手，静态内容，类型。



命名约定

-使用小写带破折号的目录（例如，components/auth-wizard）。

-组件名称使用PascalCase（例如，AuthWizard.vue）。

-对可组合组件使用camelCase（例如，useAuthState.ts）。



使用打印稿

-所有代码都使用TypeScript；选择类型而不是接口。

-避免枚举；使用const对象代替。

使用Vue 3和TypeScript，利用defineComponent和PropType。



语法和格式

-方法和计算属性使用箭头函数。

-避免在条件语句中使用不必要的花括号；对简单的语句使用简洁的语法。

-使用模板语法进行声明性呈现。



UI和样式

-使用Shadcn Vue， Radix Vue和Tailwind组件和样式。

-使用顺风CSS实现响应式设计；使用移动优先的方法。



性能优化

-利用next的内置性能优化。

—异步组件使用悬念。

-实现延迟加载路由和组件。

-优化图像：使用WebP格式，包括大小数据，实现延迟加载。



关键的约定

—对于常见的可组合项和实用程序函数，使用veuse。

—使用Pinia进行状态管理。

-优化Web vital （LCP， CLS， FID）。

-利用next的组件和可组合组件的自动导入功能。



Nuxt-specific指南

-遵循next 3目录结构（例如，pages/, components/, composables/）。

-使用next的内置功能：

-组件和可组合组件的自动导入。

—pages/目录下基于文件的路由。

—Server /目录下的服务器路由。

-利用next插件实现全局功能。

—使用useFetch和useAsyncData获取数据。

-使用next的useHead和useSeoMeta实现SEO最佳实践。



Vue 3和组合API最佳实践

-使用<script setup>语法来定义简洁的组件。

-利用ref、reactive和computed进行reactive状态管理。

-在适当的时候使用提供/注入进行依赖注入。

-实现可重用逻辑的自定义组合。




请参考官方的nuext .js和Vue.js文档，了解最新的数据获取、呈现和路由最佳实践。