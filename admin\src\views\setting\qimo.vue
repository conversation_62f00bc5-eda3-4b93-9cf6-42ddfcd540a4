<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.status"
        placeholder="坐席状态"
        clearable
        class="filter-item"
        style="width: 140px"
      >
        <el-option
          v-for="(label, value) in statusOptions"
          :key="value"
          :label="label"
          :value="value"
        />
      </el-select>
      
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-search"
        @click="getList"
      >
        搜索
      </el-button>
      
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        添加坐席
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" prop="id" align="center" width="80" />
      
      <el-table-column label="坐席名称" prop="name" align="center" width="120" />
      
      <el-table-column label="账户ID" prop="account_id" align="center" width="150" />
      
      <el-table-column label="用户名" prop="username" align="center" width="120" />
      
      <el-table-column label="PBX地址" prop="pbx_url" align="center" width="200">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.pbx_url" placement="top">
            <span>{{ scope.row.pbx_url | truncate(30) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column label="登录类型" prop="login_type" align="center" width="100" />
      
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusName(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleUpdate(scope.row)">
            编辑
          </el-button>
          <el-button 
            v-if="scope.row.status === 1"
            size="mini" 
            type="warning" 
            @click="handleUpdateStatus(scope.row, 0)"
          >
            禁用
          </el-button>
          <el-button 
            v-else-if="scope.row.status === 0"
            size="mini" 
            type="success" 
            @click="handleUpdateStatus(scope.row, 1)"
          >
            启用
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
        style="width: 400px; margin-left:50px;"
      >
        <el-form-item label="坐席名称" prop="name">
          <el-input v-model="temp.name" placeholder="请输入坐席名称" />
        </el-form-item>
        
        <el-form-item label="账户ID" prop="account_id">
          <el-input v-model="temp.account_id" placeholder="请输入账户ID" />
        </el-form-item>
        
        <el-form-item label="用户名" prop="username">
          <el-input v-model="temp.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input v-model="temp.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        
        <el-form-item label="PBX地址" prop="pbx_url">
          <el-input v-model="temp.pbx_url" placeholder="请输入PBX地址，如：https://sh-hw-cc-v4.7moor.com" />
        </el-form-item>
        
        <el-form-item label="登录类型" prop="login_type">
          <el-select v-model="temp.login_type" placeholder="请选择登录类型" style="width: 100%;">
            <el-option label="本地登录" value="Local" />
            <el-option label="远程登录" value="Remote" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="temp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述信息"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'QimoAgents',
  filters: {
    truncate(value, length = 20) {
      if (!value) return ''
      if (value.length <= length) return value
      return value.substring(0, length) + '...'
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        pageSize: 20,
        status: ''
      },
      statusOptions: {
        0: '禁用',
        1: '空闲',
        2: '忙碌',
        3: '通话中'
      },
      temp: {
        id: undefined,
        name: '',
        account_id: '',
        username: '',
        password: '',
        pbx_url: '',
        login_type: 'Local',
        description: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑坐席',
        create: '添加坐席'
      },
      rules: {
        name: [{ required: true, message: '坐席名称是必填项', trigger: 'blur' }],
        account_id: [{ required: true, message: '账户ID是必填项', trigger: 'blur' }],
        username: [{ required: true, message: '用户名是必填项', trigger: 'blur' }],
        password: [{ required: true, message: '密码是必填项', trigger: 'blur' }],
        pbx_url: [
          { required: true, message: 'PBX地址是必填项', trigger: 'blur' },
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      request({
        url: '/admin/qimo/agents',
        method: 'get',
        params: this.listQuery
      }).then(response => {
        this.list = response.data.list || []
        this.total = response.data.total || 0
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    
    getStatusName(status) {
      return this.statusOptions[status] || '未知'
    },
    
    getStatusType(status) {
      const typeMap = {
        0: 'info',
        1: 'success',
        2: 'warning',
        3: 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        account_id: '',
        username: '',
        password: '',
        pbx_url: '',
        login_type: 'Local',
        description: ''
      }
    },
    
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          request({
                            url: '/admin/qimo/createAgent',
            method: 'post',
            data: this.temp
          }).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          request({
                            url: `/admin/qimo/updateAgent/${tempData.id}`,
            method: 'post',
            data: tempData
          }).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    
    handleUpdateStatus(row, status) {
      const statusText = this.getStatusName(status)
      this.$confirm(`确定要${statusText}该坐席吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
                          url: `/admin/qimo/updateAgent/${row.id}`,
          method: 'post',
          data: { status }
        }).then(() => {
          row.status = status
          this.$message({
            type: 'success',
            message: `${statusText}成功!`
          })
        })
      })
    },
    
    handleDelete(row) {
      this.$confirm('此操作将永久删除该坐席, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        request({
                          url: `/admin/qimo/deleteAgent/${row.id}`,
          method: 'post'
        }).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          const index = this.list.indexOf(row)
          this.list.splice(index, 1)
        })
      })
    }
  }
}
</script>

<style scoped>
.edit-input {
  padding-right: 100px;
}
.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
</style> 