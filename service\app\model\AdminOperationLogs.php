<?php
namespace app\model;

use Illuminate\Support\Facades\Log;

class AdminOperationLogs extends base
{
    protected $table = 'admin_operation_logs';

    /**
     * 关联被操作的管理员
     */
    public function admin()
    {
        return $this->belongsTo(Admins::class, 'admin_id')->visible(['id', 'name', 'username']);
    }

    /**
     * 关联操作者
     */
    public function operator()
    {
        return $this->belongsTo(Admins::class, 'operator_id')->visible(['id', 'name', 'username']);
    }

    /**
     * 获取字段变更信息
     */
    public function getFieldChangesAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 记录管理员操作日志
     * @param int $adminId 被操作的管理员ID
     * @param object $operator 操作者ID
     * @param string $operationType 操作类型
     * @param array $fieldChanges 字段变更记录
     * @param string $remark 备注
     * @param string $ipAddress IP地址
     * @param string $userAgent 用户代理
     * @param string $logModule 操作模块
     * @return bool
     */
    public static function recordLog($adminId, $operator, $operationType, $fieldChanges = [], $remark = '', $ipAddress = '', $userAgent = '', $logModule = 'admin')
    {
        $log = new self();
        $log->admin_id = $adminId;
        $log->operator_id = $operator->id;
        $log->shop_id = $operator->shop_id;
        $log->operation_type = $operationType;
        $log->log_module = $logModule;
        $log->operation_time = time();
        $log->ip_address = $ipAddress;
        $log->user_agent = $userAgent;
        $log->remark = $remark;

        if (!empty($fieldChanges)) {
            $log->field_changes = json_encode($fieldChanges, JSON_UNESCAPED_UNICODE);
        }

        return $log->save();
    }

    /**
     * 获取操作类型中文名称
     */
    public function getOperationTypeNameAttr()
    {
        $typeMap = [
            'create' => '创建',
            'update' => '更新',
            'disable' => '禁用',
            'enable' => '启用',
            'reset_password' => '重置密码'
        ];

        return $typeMap[$this->operation_type] ?? $this->operation_type;
    }

    /**
     * 获取模块中文名称
     */
    public function getLogModuleNameAttr()
    {
        $moduleMap = [
            'admin' => '管理员管理',
            'ota_route_group' => 'OTA线路分组'
        ];

        return $moduleMap[$this->log_module] ?? $this->log_module;
    }

    /**
     * 获取指定模块的操作日志
     * @param string $module 模块名称
     * @param array $params 查询参数
     * @return array
     */
    public static function getModuleLogs($module, $params = [])
    {
        $query = self::where('log_module', $module);

        // 操作者筛选
        if (!empty($params['operator_id'])) {
            $query->where('operator_id', $params['operator_id']);
        }

        // 门店
        if (!empty($params['shop_id'])) {
            $query->where('shop_id', $params['shop_id']);
        }

        // 操作类型筛选
        if (!empty($params['operation_type'])) {
            $query->where('operation_type', $params['operation_type']);
        }

        // 时间范围筛选
        if (!empty($params['start_time'])) {
            $query->where('operation_time', '>=', strtotime($params['start_time']));
        }
        if (!empty($params['end_time'])) {
            $query->where('operation_time', '<=', strtotime($params['end_time'] . ' 23:59:59'));
        }

        // 分页参数
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;

        $total = $query->count();
        $list = $query->with(['operator'])
                     ->order('operation_time', 'desc')
                     ->page($page, $limit)
                     ->select();

        return [
            'total' => $total,
            'list' => $list->append(['operation_type_name'])
        ];
    }

    /**
     * 获取操作时间格式化
     */
    public function getOperationTimeFormatAttr()
    {
        return date('Y-m-d H:i:s', $this->operation_time);
    }

    /**
     * 比较管理员数据变更
     * @param array $oldData 原数据
     * @param array $newData 新数据
     * @return array
     */
    public static function compareAdminData($oldData, $newData)
    {
        $changes = [];
        $fieldNames = [
            'username' => '用户名',
            'name' => '姓名',
            'mobile' => '手机号',
            'wechat' => '微信号',
            'wechat_pic' => '微信二维码',
            'dy_nickname' => '抖音昵称',
            'is_order' => '是否分配',
            'is_private' => '是否私域',
            'is_anchor' => '是否主播',
            'is_franchisee' => '是否加盟商',
            'is_xs' => '是否私域',
            'product_ids' => '线路权限',
            'product_xs_ids' => '私域线路权限',
            'route_group_ids' => '线路组权限',
            'route_type' => '路线类型',
            'type' => '类型',
            'status' => '状态'
        ];

        // 权限字段，需要特殊处理变更记录
        $permissionFields = ['product_ids', 'product_xs_ids', 'route_group_ids'];

        foreach ($fieldNames as $field => $label) {
            if (!isset($oldData[$field]) || !isset($newData[$field])) {
                continue;
            }

            $oldValue = $oldData[$field] ?? '';
            $newValue = $newData[$field] ?? '';

            if ($oldValue != $newValue) {
                // 权限字段特殊处理
                if (in_array($field, $permissionFields)) {
                    $permissionChange = self::comparePermissionField($field, $oldValue, $newValue);
                    if (!empty($permissionChange)) {
                        $changes[$field] = [
                            'label' => $label,
                            'old_value' => '',
                            'new_value' => $permissionChange['new_value'],
                            'changes' => $permissionChange['changes']
                        ];
                    }
                } else {
                    $changes[$field] = [
                        'label' => $label,
                        'old_value' => self::formatFieldValue($field, $oldValue),
                        'new_value' => self::formatFieldValue($field, $newValue)
                    ];
                }
            }
        }

        return $changes;
    }

    /**
     * 格式化字段值显示
     * @param string $field 字段名
     * @param mixed $value 字段值
     * @return string
     */
    public static function formatFieldValue($field, $value)
    {
        if (empty($value) && $value !== '0' && $value !== 0) {
            return '未设置';
        }

        switch ($field) {
            case 'is_order':
            case 'is_anchor':
            case 'is_franchisee':
            case 'is_xs':
            case 'is_private':
            case 'status':
                return $value == 1 ? '是' : '否';

            case 'product_ids':
            case 'product_xs_ids':
                return self::formatProductNames($value);

            case 'route_group_ids':
                return self::formatRouteGroupNames($value);

            default:
                return $value;
        }
    }

    /**
     * 格式化产品名称显示
     * @param string $productIds 产品ID列表，逗号分隔
     * @return string
     */
    private static function formatProductNames($productIds)
    {
        if (empty($productIds)) {
            return '未设置';
        }

        $ids = array_filter(explode(',', $productIds));
        if (empty($ids)) {
            return '未设置';
        }

        try {
            $products = \app\model\Products::whereIn('id', $ids)
                                         ->field('product_name')
                                         ->select();

            if (empty($products)) {
                return '产品不存在(' . $productIds . ')';
            }

            $names = [];
            foreach ($products as $product) {
                $names[] = $product->product_name;
            }

            return implode('、', $names);
        } catch (\Exception $e) {
            return '产品ID(' . $productIds . ')';
        }
    }

    /**
     * 格式化线路组名称显示
     * @param string $groupIds 线路组ID列表，逗号分隔
     * @return string
     */
    private static function formatRouteGroupNames($groupIds)
    {
        if (empty($groupIds)) {
            return '未设置';
        }

        $ids = array_filter(explode(',', $groupIds));
        if (empty($ids)) {
            return '未设置';
        }

        try {
            $groups = \app\model\OtaRouteGroups::whereIn('id', $ids)
                                             ->field('group_name')
                                             ->select();

            if (empty($groups)) {
                return '分组不存在(' . $groupIds . ')';
            }

            $names = [];
            foreach ($groups as $group) {
                $names[] = $group->group_name;
            }

            return implode('、', $names);
        } catch (\Exception $e) {
            return '分组ID(' . $groupIds . ')';
        }
    }

    /**
     * 比较权限字段变更，只记录增加和删除的部分
     * @param string $field 字段名
     * @param string $oldValue 旧值
     * @param string $newValue 新值
     * @return array
     */
    private static function comparePermissionField($field, $oldValue, $newValue)
    {
        $oldIds = array_filter(explode(',', $oldValue ?? ''));
        $newIds = array_filter(explode(',', $newValue ?? ''));

        // 计算增加和删除的ID
        $addedIds = array_diff($newIds, $oldIds);
        $removedIds = array_diff($oldIds, $newIds);

        if (empty($addedIds) && empty($removedIds)) {
            return [];
        }

        $changes = [];
        $oldText = '';
        $newText = '';

        // 处理删除的项目
        if (!empty($removedIds)) {
            $removedNames = self::getPermissionNames($field, $removedIds);
            $changes['removed'] = $removedNames;
            $oldText .= '删除: ' . implode('、', $removedNames);
        }

        // 处理添加的项目
        if (!empty($addedIds)) {
            $addedNames = self::getPermissionNames($field, $addedIds);
            $changes['added'] = $addedNames;
            if (!empty($oldText)) {
                $newText = $oldText . ' | ';
            }
            $newText .= '新增: ' . implode('、', $addedNames);
        } else {
            $newText = $oldText;
        }

        return [
            'old_value' => self::formatFieldValue($field, $oldValue),
            'new_value' => $newText,
            'changes' => $changes
        ];
    }

    /**
     * 获取权限名称
     * @param string $field 字段名
     * @param array $ids ID数组
     * @return array
     */
    private static function getPermissionNames($field, $ids)
    {
        if (empty($ids)) {
            return [];
        }

        try {
            switch ($field) {
                case 'product_ids':
                case 'product_xs_ids':
                    $items = \app\model\Products::whereIn('id', $ids)
                                               ->field('product_name')
                                               ->select();
                    return array_column($items->toArray(), 'product_name');

                case 'route_group_ids':
                    $items = \app\model\OtaRouteGroups::whereIn('id', $ids)
                                                     ->field('group_name')
                                                     ->select();
                    return array_column($items->toArray(), 'group_name');

                default:
                    return $ids;
            }
        } catch (\Exception $e) {
            return $ids;
        }
    }
}
