-- 创建飞猪订单表
CREATE TABLE `fliggy_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `fliggy_order_id` varchar(100) NOT NULL COMMENT '飞猪订单号',
  `order_id` varchar(100) DEFAULT NULL COMMENT '系统商内部订单号',
  `biz_type` tinyint(4) NOT NULL DEFAULT '2' COMMENT '业务类型 2-线路业务',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '订单状态 1-待确认 2-已确认 3-已取消 4-已完成 5-已退款',
  `adult_number` int(11) DEFAULT '0' COMMENT '成人数',
  `child_number` int(11) DEFAULT '0' COMMENT '儿童数',
  `travel_date` date DEFAULT NULL COMMENT '出行日期',
  `confirm_time` int(11) DEFAULT NULL COMMENT '二次确认时间戳',
  `refund_status` tinyint(4) DEFAULT NULL COMMENT '退款状态 1-退款成功 2-退款失败 3-退款处理中',
  `refund_fee` int(11) DEFAULT '0' COMMENT '退款金额（分）',
  `refund_reason` varchar(500) DEFAULT NULL COMMENT '退款原因',
  `refund_time` int(11) DEFAULT NULL COMMENT '退款时间戳',
  `vendor` json DEFAULT NULL COMMENT '系统商信息',
  `contacts` json DEFAULT NULL COMMENT '联系人信息',
  `travellers` json DEFAULT NULL COMMENT '出行人信息',
  `product_list` json DEFAULT NULL COMMENT '产品列表',
  `order_info` json DEFAULT NULL COMMENT '订单详细信息',
  `extend_map` json DEFAULT NULL COMMENT '扩展信息',
  `create_at` int(11) NOT NULL COMMENT '创建时间戳',
  `update_at` int(11) NOT NULL COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_fliggy_order_id` (`fliggy_order_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_at` (`create_at`),
  KEY `idx_travel_date` (`travel_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='飞猪订单表';

-- 创建飞猪订单日志表
CREATE TABLE `fliggy_order_logs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `fliggy_order_id` varchar(100) NOT NULL COMMENT '飞猪订单号',
  `order_id` varchar(100) DEFAULT NULL COMMENT '系统商内部订单号',
  `action_type` varchar(50) NOT NULL COMMENT '操作类型 create-创建 query-查询 refund-退款 update-修改',
  `request_data` json DEFAULT NULL COMMENT '请求数据',
  `response_data` json DEFAULT NULL COMMENT '响应数据',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '处理状态 1-成功 2-失败',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `create_at` int(11) NOT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_fliggy_order_id` (`fliggy_order_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_at` (`create_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='飞猪订单操作日志表';

-- 创建飞猪配置表
CREATE TABLE `fliggy_configs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_key` varchar(100) NOT NULL COMMENT '应用公钥',
  `app_secret` varchar(200) NOT NULL COMMENT '应用私钥',
  `env` varchar(20) NOT NULL DEFAULT 'sandbox' COMMENT '环境 sandbox-测试 production-生产',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 1-启用 2-禁用',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `create_at` int(11) NOT NULL COMMENT '创建时间戳',
  `update_at` int(11) NOT NULL COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_app_key` (`app_key`),
  KEY `idx_env` (`env`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='飞猪配置表';

-- 插入默认测试配置
INSERT INTO `fliggy_configs` (`app_key`, `app_secret`, `env`, `status`, `description`, `create_at`, `update_at`) VALUES
('test-ak1', 'test-ak1-password', 'sandbox', 1, '测试环境配置', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());


