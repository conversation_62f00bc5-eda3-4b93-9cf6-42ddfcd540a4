(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2488cbde"],{"0483":function(t,e,n){"use strict";n("37d2")},"17eb":function(t,e,n){"use strict";n("3b3b")},"37d2":function(t,e,n){},"3b3b":function(t,e,n){},dae2:function(t,e,n){"use strict";n.r(e);var s=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"lineOnSale"},[s("div",{staticClass:"lineOnSale_list"},[s("img",{attrs:{src:n("eef5"),alt:""}}),s("div",{staticClass:"lineOnSale_centent"},[s("div",{staticClass:"lineOnSale_title"},[t._v("天上人间—马尔代夫7日自由行7天")]),s("div",{staticClass:"lineOnSale_price"},[s("div",{staticClass:"desc"},[s("p",[t._v("行程天数：")]),s("overflowConcealment",{attrs:{content:"天上人间—马尔代夫7日自尔代夫7日自由行7天天上人间—马尔代夫7夫7日自由日自由行7天"}})],1),s("div",{staticClass:"desc"},[s("p",[t._v("购物店：")]),s("overflowConcealment",{attrs:{content:"马尔代夫"}})],1)]),s("div",{staticClass:"lineOnSale_price"},[s("div",{staticClass:"desc"},[s("p",[t._v("是否含大交通：")]),s("overflowConcealment",{attrs:{content:"否"}})],1),s("div",{staticClass:"desc"},[s("p",[t._v("可售卖平台：")]),s("overflowConcealment",{attrs:{content:"否"}})],1)]),s("div",{staticClass:"lineOnSale_price"},[s("div",{staticClass:"desc"},[s("p",[t._v("卖点：")]),s("overflowConcealment",{attrs:{content:"誉为印度洋的珍珠"}})],1),s("div",{staticClass:"desc"},[s("p",[t._v("佣金：")]),s("overflowConcealment",{attrs:{content:"6521615"}})],1)]),s("div",{staticClass:"lineOnSale_price"},[s("div",{staticClass:"desc"},[s("p",[t._v("赠送项目：")]),s("overflowConcealment",{attrs:{content:"沙巴岛一日游"}})],1),s("div",{staticClass:"desc"},[s("p",[t._v("卖价：")]),s("overflowConcealment",{attrs:{content:"6546466"}})],1)])]),s("div",{staticClass:"lineOnSale_right"},[s("div",{staticClass:"lineOnSale_btn"},[s("el-button",{staticClass:"btn",attrs:{type:"primary"}},[t._v("下载行程")]),s("el-button",{staticClass:"btn",attrs:{type:"primary"}},[t._v("下载话术")]),s("el-button",{staticClass:"btn",attrs:{type:"primary"}},[t._v("下载图片")]),s("el-button",{staticClass:"btn",attrs:{type:"primary"}},[t._v("下载视频")])],1)])])])},a=[],i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("p",{ref:"desc_conten",staticClass:"desc_conten"},[t._v(t._s(t.content)),n("el-button",{directives:[{name:"show",rawName:"v-show",value:t.isTruncated,expression:"isTruncated"}],staticClass:"desc_btn",attrs:{type:"primary"},on:{click:function(e){return t.open(t.content)}}},[t._v("查看")])],1)},c=[],o={name:"overflowConcealment",data:function(){return{isTruncated:!1}},props:{content:{type:String,default:""}},mounted:function(){this.checkTruncation(),window.addEventListener("resize",this.checkTruncation)},destroyed:function(){window.removeEventListener("resize",this.checkTruncation)},methods:{open:function(t){this.$alert(t,"内容",{confirmButtonText:"确定"})},checkTruncation:function(){this.isTruncated=this.$refs["desc_conten"].offsetWidth<this.$refs["desc_conten"].scrollWidth}}},l=o,r=(n("17eb"),n("8a34")),d=Object(r["a"])(l,i,c,!1,null,"0c9295b8",null),v=d.exports,u={name:"lineOnSale",data:function(){return{isTruncated:!1}},components:{overflowConcealment:v},mounted:function(){},methods:{handleDownload:function(t){t?window.open(t):this.$message({showClose:!0,message:"暂无下载链接"})}}},f=u,p=(n("0483"),Object(r["a"])(f,s,a,!1,null,"4a88b338",null));e["default"]=p.exports},eef5:function(t,e,n){t.exports=n.p+"static/img/fff.01376ab1.png"}}]);