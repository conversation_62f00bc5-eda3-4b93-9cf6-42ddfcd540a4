(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5d465274"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),Math.easeInOutQuad=function(t,e,a,l){return t/=l/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var l=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function i(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,a){var s=o(),n=t-s,r=20,c=0;e="undefined"===typeof e?500:e;var u=function(){c+=r;var t=Math.easeInOutQuad(c,s,n,e);i(t),c<e?l(u):a&&"function"===typeof a&&a()};u()}},"2cbf":function(t,e,a){"use strict";a("bac3")},"333d":function(t,e,a){"use strict";var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[a("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},i=[],o=(a("374d"),a("09f4")),s={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(o["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(o["a"])(0,800)}}},n=s,r=(a("2cbf"),a("8a34")),c=Object(r["a"])(n,l,i,!1,null,"6af373ef",null);e["a"]=c.exports},"3cef":function(t,e,a){"use strict";a.d(e,"d",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"g",(function(){return s})),a.d(e,"a",(function(){return n})),a.d(e,"h",(function(){return r})),a.d(e,"b",(function(){return c})),a.d(e,"f",(function(){return u})),a.d(e,"e",(function(){return d}));var l=a("b775");function i(t){return Object(l["a"])({url:"/admin/ota-route-group/index",method:"get",params:t})}function o(t){return Object(l["a"])({url:"/admin/ota-route-group/show",method:"get",params:t})}function s(t){return Object(l["a"])({url:"/admin/ota-route-group/save",method:"post",data:t})}function n(t){return Object(l["a"])({url:"/admin/ota-route-group/delete",method:"post",data:t})}function r(t){return Object(l["a"])({url:"/admin/ota-route-group/updateStatus",method:"post",data:t})}function c(){return Object(l["a"])({url:"/admin/ota-route-group/getActiveGroups",method:"get"})}function u(){return Object(l["a"])({url:"/admin/ota-route-group/getProducts",method:"get"})}function d(t){return Object(l["a"])({url:"/admin/ota-route-group/logs",method:"get",params:t})}},5604:function(t,e,a){},"9bb2":function(t,e,a){"use strict";a("5604")},bac3:function(t,e,a){},bc9f:function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.listQuery}},[a("el-form-item",{attrs:{label:"组名"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入组名",clearable:""},model:{value:t.listQuery.group_name,callback:function(e){t.$set(t.listQuery,"group_name",e)},expression:"listQuery.group_name"}})],1),a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},[a("el-option",{attrs:{label:"启用",value:1}}),a("el-option",{attrs:{label:"禁用",value:0}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.handleFilter}},[t._v("搜索")]),a("el-button",{on:{click:t.resetFilter}},[t._v("重置")]),a("el-button",{attrs:{type:"success"},on:{click:t.handleCreate}},[t._v("新增分组")]),a("el-button",{attrs:{type:"info"},on:{click:t.handleViewLogs}},[t._v("操作日志")])],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{label:"ID",width:"60",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.id)+" ")]}}])}),a("el-table-column",{attrs:{label:"组名",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.group_name)+" ")]}}])}),a("el-table-column",{attrs:{label:"包含线路","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.product_names)+" ")]}}])}),a("el-table-column",{attrs:{label:"排序权重",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.sort_order)+" ")]}}])}),a("el-table-column",{attrs:{label:"状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-text":"启用","active-value":1,"inactive-value":0,"inactive-text":"禁用"},on:{change:function(a){return t.handleStatusChange(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:"描述",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.description||"--")+" ")]}}])}),a("el-table-column",{attrs:{label:"创建时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.parseTime(e.row.created_at,"{y}-{m}-{d} {h}:{i}"))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",width:"180",align:"center",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return t.handleEdit(e.row)}}},[t._v(" 编辑 ")]),a("el-button",{attrs:{type:"info",size:"mini"},on:{click:function(a){return t.handleViewDetail(e.row)}}},[t._v(" 详情 ")]),a("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v(" 删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}),a("el-dialog",{attrs:{title:"create"===t.dialogType?"新增线路分组":"编辑线路分组",visible:t.dialogVisible,width:"60%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{ref:"dataForm",attrs:{model:t.temp,rules:t.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"组名",prop:"group_name"}},[a("el-input",{attrs:{placeholder:"请输入组名"},model:{value:t.temp.group_name,callback:function(e){t.$set(t.temp,"group_name",e)},expression:"temp.group_name"}})],1),a("el-form-item",{attrs:{label:"包含线路",prop:"product_ids"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"",placeholder:"请选择线路"},model:{value:t.temp.product_ids,callback:function(e){t.$set(t.temp,"product_ids",e)},expression:"temp.product_ids"}},t._l(t.products,(function(t){return a("el-option",{key:t.id,attrs:{label:t.product_name,value:t.id}})})),1)],1),a("el-form-item",{attrs:{label:"排序权重"}},[a("el-input-number",{attrs:{min:0,max:9999,placeholder:"数字越大排序越靠前"},model:{value:t.temp.sort_order,callback:function(e){t.$set(t.temp,"sort_order",e)},expression:"temp.sort_order"}})],1),a("el-form-item",{attrs:{label:"状态"}},[a("el-radio-group",{model:{value:t.temp.status,callback:function(e){t.$set(t.temp,"status",e)},expression:"temp.status"}},[a("el-radio",{attrs:{label:1}},[t._v("启用")]),a("el-radio",{attrs:{label:0}},[t._v("禁用")])],1)],1),a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入描述（可选）"},model:{value:t.temp.description,callback:function(e){t.$set(t.temp,"description",e)},expression:"temp.description"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v(" 取消 ")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleSave}},[t._v(" 确定 ")])],1)],1),a("el-dialog",{attrs:{title:"线路分组详情",visible:t.detailDialogVisible,width:"60%"},on:{"update:visible":function(e){t.detailDialogVisible=e}}},[t.currentRecord?a("div",[a("el-card",{staticClass:"detail-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("基本信息")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("组名：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.group_name))])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("状态：")]),a("span",{staticClass:"detail-value"},[a("el-tag",{attrs:{type:1===t.currentRecord.status?"success":"danger"}},[t._v(" "+t._s(1===t.currentRecord.status?"启用":"禁用")+" ")])],1)])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("排序权重：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.sort_order))])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("创建时间：")]),a("span",{staticClass:"detail-value"},[t._v(" "+t._s(t.parseTime(t.currentRecord.created_at,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")])])])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("描述：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.description||"--"))])])])],1)],1),a("el-card",{staticClass:"detail-card",staticStyle:{"margin-top":"15px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("包含的线路")])]),t.currentRecord.products&&t.currentRecord.products.length>0?a("div",[a("el-table",{attrs:{data:t.currentRecord.products,border:""}},[a("el-table-column",{attrs:{label:"线路ID",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.id)+" ")]}}],null,!1,3411552848)}),a("el-table-column",{attrs:{label:"线路名称",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.product_name)+" ")]}}],null,!1,49116974)}),a("el-table-column",{attrs:{label:"第三方产品ID",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.third_product_id||"--")+" ")]}}],null,!1,2030914872)})],1)],1):a("div",{staticClass:"no-data"},[a("el-empty",{attrs:{description:"暂无线路"}})],1)])],1):t._e(),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.detailDialogVisible=!1}}},[t._v("关闭")])],1)]),a("el-dialog",{attrs:{title:"操作日志",visible:t.logsDialogVisible,width:"80%"},on:{"update:visible":function(e){t.logsDialogVisible=e}}},[a("div",{staticClass:"operation-log-container"},[a("div",{staticClass:"filter-container"},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.logsQuery}},[a("el-form-item",{attrs:{label:"操作类型"}},[a("el-select",{attrs:{placeholder:"请选择操作类型",clearable:""},model:{value:t.logsQuery.operation_type,callback:function(e){t.$set(t.logsQuery,"operation_type",e)},expression:"logsQuery.operation_type"}},[a("el-option",{attrs:{label:"创建",value:"create"}}),a("el-option",{attrs:{label:"更新",value:"update"}}),a("el-option",{attrs:{label:"删除",value:"delete"}}),a("el-option",{attrs:{label:"启用",value:"enable"}}),a("el-option",{attrs:{label:"禁用",value:"disable"}})],1)],1),a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:t.logDateRange,callback:function(e){t.logDateRange=e},expression:"logDateRange"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:t.handleLogsFilter}},[t._v("搜索")]),a("el-button",{on:{click:t.resetLogsFilter}},[t._v("重置")])],1)],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.logsLoading,expression:"logsLoading"}],staticStyle:{width:"100%"},attrs:{data:t.logsList,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{label:"操作时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.parseTime(1e3*e.row.operation_time,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])}),a("el-table-column",{attrs:{label:"操作者",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.operator&&e.row.operator.name||"--")+" ")]}}])}),a("el-table-column",{attrs:{label:"操作类型",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:t.getOperationTypeTag(e.row.operation_type)}},[t._v(" "+t._s(e.row.operation_type_name)+" ")])]}}])}),a("el-table-column",{attrs:{label:"变更内容","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.field_changes&&Object.keys(e.row.field_changes).length>0?a("div",[t._l(t.getLimitedChanges(e.row.field_changes),(function(e,l,i){return a("div",{key:l,staticClass:"change-item"},[a("span",{staticClass:"field-label"},[t._v(t._s(e.label)+":")]),a("span",{staticClass:"old-value"},[t._v(t._s(e.old_value||"--"))]),a("span",{staticClass:"arrow"},[t._v(" → ")]),a("span",{staticClass:"new-value"},[t._v(t._s(e.new_value||"--"))])])})),Object.keys(e.row.field_changes).length>2?a("div",{staticClass:"more-changes"},[a("el-button",{staticStyle:{color:"#409EFF",padding:"0"},attrs:{type:"text",size:"mini"},on:{click:function(a){return t.handleLogDetail(e.row)}}},[t._v(" 还有 "+t._s(Object.keys(e.row.field_changes).length-2)+" 项变更，查看详情 ")])],1):t._e()],2):a("span",{staticClass:"text-muted"},[t._v(t._s(e.row.remark||"--"))])]}}])}),a("el-table-column",{attrs:{label:"IP地址",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.ip_address||"--")+" ")]}}])}),a("el-table-column",{attrs:{label:"操作",width:"80",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return t.handleLogDetail(e.row)}}},[t._v(" 详情 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.logsTotal>0,expression:"logsTotal > 0"}],attrs:{total:t.logsTotal,page:t.logsQuery.page,limit:t.logsQuery.limit},on:{"update:page":function(e){return t.$set(t.logsQuery,"page",e)},"update:limit":function(e){return t.$set(t.logsQuery,"limit",e)},pagination:t.getLogsList}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.logsDialogVisible=!1}}},[t._v("关闭")])],1)]),a("el-dialog",{attrs:{title:"操作日志详情",visible:t.logDetailDialog,width:"60%"},on:{"update:visible":function(e){t.logDetailDialog=e}}},[t.currentLogRecord?a("div",[a("el-card",{staticClass:"detail-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("基本信息")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("操作时间：")]),a("span",{staticClass:"detail-value"},[t._v(" "+t._s(t.parseTime(1e3*t.currentLogRecord.operation_time,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("操作类型：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentLogRecord.operation_type_name))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("操作者：")]),a("span",{staticClass:"detail-value"},[t._v(" "+t._s(t.currentLogRecord.operator&&t.currentLogRecord.operator.name||"--")+" ("+t._s(t.currentLogRecord.operator&&t.currentLogRecord.operator.username||"--")+") ")])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("IP地址：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentLogRecord.ip_address||"--"))])])])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("备注：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentLogRecord.remark||"--"))])])])],1)],1),t.currentLogRecord.field_changes&&Object.keys(t.currentLogRecord.field_changes).length>0?a("el-card",{staticClass:"detail-card",staticStyle:{"margin-top":"15px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("变更详情")])]),a("el-table",{attrs:{data:t.getFormattedChanges(t.currentLogRecord.field_changes),border:""}},[a("el-table-column",{attrs:{label:"字段",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.label)+" ")]}}],null,!1,3382659611)}),a("el-table-column",{attrs:{label:"变更前",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.old_value||"--")+" ")]}}],null,!1,2948110094)}),a("el-table-column",{attrs:{label:"变更后",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.new_value||"--")+" ")]}}],null,!1,3423505013)})],1)],1):t._e()],1):t._e(),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.logDetailDialog=!1}}},[t._v("关闭")])],1)])],1)},i=[],o=a("d09a"),s=(a("1a06"),a("11c4"),a("4cc3"),a("5227"),a("3cef")),n=a("ed08"),r=a("333d"),c={name:"OtaRouteGroup",components:{Pagination:r["a"]},data:function(){return{list:[],total:0,listLoading:!0,listQuery:{page:1,limit:20,group_name:"",status:""},dialogVisible:!1,detailDialogVisible:!1,logsDialogVisible:!1,logDetailDialog:!1,dialogType:"create",temp:{id:null,group_name:"",product_ids:[],status:1,sort_order:0,description:""},currentRecord:null,products:[],rules:{group_name:[{required:!0,message:"请输入组名",trigger:"blur"}],product_ids:[{required:!0,message:"请选择线路",trigger:"change"}]},logsList:[],logsTotal:0,logsLoading:!1,logsQuery:{page:1,limit:20,operation_type:"",start_time:"",end_time:""},currentLogRecord:null,logDateRange:[]}},created:function(){this.getList(),this.getProducts()},methods:{parseTime:n["d"],getList:function(){var t=this;this.listLoading=!0,Object(s["d"])(this.listQuery).then((function(e){t.list=e.data.data||[],t.total=e.data.total||0,t.listLoading=!1})).catch((function(e){console.error("获取线路分组列表失败:",e),t.listLoading=!1}))},getProducts:function(){var t=this;Object(s["f"])().then((function(e){t.products=e.data||[]})).catch((function(t){console.error("获取产品列表失败:",t)}))},handleFilter:function(){this.listQuery.page=1,this.getList()},resetFilter:function(){this.listQuery={page:1,limit:20,group_name:"",status:""},this.getList()},handleCreate:function(){var t=this;this.temp={id:null,group_name:"",product_ids:[],status:1,sort_order:0,description:""},this.dialogType="create",this.dialogVisible=!0,this.$nextTick((function(){t.$refs.dataForm.clearValidate()}))},handleEdit:function(t){var e=this;Object(s["c"])({id:t.id}).then((function(t){var a=t.data;e.temp={id:a.id,group_name:a.group_name,product_ids:a.product_ids_array?a.product_ids_array.map((function(t){return parseInt(t)})):[],status:a.status,sort_order:a.sort_order,description:a.description||""},e.dialogType="edit",e.dialogVisible=!0,e.$nextTick((function(){e.$refs.dataForm.clearValidate()}))})).catch((function(t){console.error("获取分组详情失败:",t),e.$message.error("获取分组详情失败")}))},handleSave:function(){var t=this;this.$refs.dataForm.validate((function(e){e&&Object(s["g"])(t.temp).then((function(e){t.$message.success("保存成功"),t.dialogVisible=!1,t.getList()})).catch((function(e){var a;console.error("保存失败:",e),t.$message.error((null===(a=e.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.msg)||"保存失败")}))}))},handleDelete:function(t){var e=this;this.$confirm("确定要删除该线路分组吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(s["a"])({id:t.id}).then((function(t){e.$message.success("删除成功"),e.getList()})).catch((function(t){var a;console.error("删除失败:",t),e.$message.error((null===(a=t.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.msg)||"删除失败")}))}))},handleStatusChange:function(t){var e=this;Object(s["h"])({id:t.id,status:t.status}).then((function(t){e.$message.success("状态更新成功")})).catch((function(a){console.error("状态更新失败:",a),e.$message.error("状态更新失败"),t.status=1===t.status?0:1}))},handleViewDetail:function(t){var e=this;Object(s["c"])({id:t.id}).then((function(t){e.currentRecord=t.data,e.detailDialogVisible=!0})).catch((function(t){console.error("获取详情失败:",t),e.$message.error("获取详情失败")}))},handleViewLogs:function(){this.logsDialogVisible=!0,this.resetLogsFilter(),this.getLogsList()},getLogsList:function(){var t=this;this.logsLoading=!0;var e=Object(o["a"])({},this.logsQuery);this.logDateRange&&2===this.logDateRange.length&&(e.start_time=this.logDateRange[0],e.end_time=this.logDateRange[1]),Object(s["e"])(e).then((function(e){t.logsList=e.data.data||[],t.logsTotal=e.data.total||0,t.logsLoading=!1})).catch((function(e){console.error("获取操作日志失败:",e),t.logsLoading=!1}))},handleLogsFilter:function(){this.logsQuery.page=1,this.getLogsList()},resetLogsFilter:function(){this.logsQuery={page:1,limit:20,operation_type:"",start_time:"",end_time:""},this.logDateRange=[],this.getLogsList()},handleDateRangeChange:function(t){t&&2===t.length?(this.logsQuery.start_time=t[0],this.logsQuery.end_time=t[1]):(this.logsQuery.start_time="",this.logsQuery.end_time="")},handleLogDetail:function(t){this.currentLogRecord=t,this.logDetailDialog=!0},getFormattedChanges:function(t){return Object.keys(t).map((function(e){return{field:e,label:t[e].label,old_value:t[e].old_value,new_value:t[e].new_value}}))},getLimitedChanges:function(t){for(var e={},a=Object.keys(t),l=0;l<Math.min(2,a.length);l++){var i=a[l];e[i]=t[i]}return e},getOperationTypeTag:function(t){var e={create:"success",update:"primary",delete:"danger",enable:"success",disable:"warning"};return e[t]||"info"}}},u=c,d=(a("9bb2"),a("8a34")),p=Object(d["a"])(u,l,i,!1,null,"b8163fec",null);e["default"]=p.exports}}]);