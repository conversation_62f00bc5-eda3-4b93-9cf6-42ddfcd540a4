<?php
/**
 * Here is your custom functions.
 */
use support\Container;
use support\Response;
use base\base\DS;
use base\helper\Conversion;
use base\helper\Date;
use base\helper\Output;
use base\helper\Snowflake;
use base\auth\UserInfo;

/**
 * Is Url In Domain With Path
 * @param $urlToCheck
 * @param $baseUrl
 * @return bool
 */
function isUrlInDomainWithPath($urlToCheck,$baseUrl): bool
{

    // 解析基础 URL
    $baseUrlParts = parse_url($baseUrl);
    if (!$baseUrlParts) {
        return false; // 解析失败
    }

    $basePath = $baseUrlParts['path'] ?? '';

    // 去除路径末尾的斜杠（如果有的话）
    $basePath = rtrim($basePath, '/');

    // 解析要检查的 URL（只提取主机名）
    $urlToCheckParts = parse_url($urlToCheck);
    if (!$urlToCheckParts) {
        return false; // 解析失败
    }

    $checkHost = $urlToCheckParts['host'] ?? '';

    // 比较主机名是否相同，并且检查路径是否包含要检查的 URL 的主机名（这里其实是检查整个路径）
    if ($basePath === $checkHost && strpos($urlToCheck,$basePath) !== false) {
        return true;
    }
    return false;
}

function get_week_days() {
    // 获取当前日期
    $currentDate = new DateTime();

    // 获取本周的周一（使用ISO-8601标准：一周的开始是周一）
    $currentDate->modify('this week monday');

    // 存储周一到周日的日期
    $weekDays = [];

    // 循环获取本周的每一天
    for ($i = 0; $i < 7; $i++) {
        // 获取当前日期并添加到数组
        $weekDays[] = [
            'date' => $currentDate->format('Y-m-d')
        ];

        // 将日期调整到下一个日期（例如，周二 -> 周三）
        $currentDate->modify('+1 day');
    }

    return $weekDays;
}

function getNewOrderId($type) {
    list($msec, $sec) = explode(' ', microtime());
    $msectime = number_format((floatval($msec) + floatval($sec)) * 1000, 0, '', '');
    $orderId = $type . $msectime . mt_rand(10000, max(intval($msec * 10000) + 10000, 98369));
    return $orderId;
}

function calculateDateRange($startDays, $endDays) {
    // 获取当前日期
    $currentDate = new DateTime();

    // 计算开始日期 (当前时间减去开始天数)
    $startDate = clone $currentDate;
    $startDate->modify("-$startDays days");

    // 计算结束日期 (当前时间减去结束天数)
    $endDate = clone $currentDate;
    $endDate->modify("-$endDays days");

    // 返回格式化后的日期
    return [
        $endDate->format('Y-m-d H:i:s'), // 结束日期
        $startDate->format('Y-m-d'),  // 开始日期
    ];
}

function get_ip_address($ip) {
    $key = 'ip:' . $ip;
    if ($ipAddress =  \support\Redis::get($key)) {
        return $ipAddress;
    }
    $apiKey = 'a343376de75af8'; // 需注册获取免费额度
    $url = "https://ipinfo.io/{$ip}/json?token={$apiKey}&language=zh-CN";

    $response = file_get_contents($url);

    $data = json_decode($response, true);
    if (!$data) {
        return '';
    }

    $str = ($data['country'] ?? '') . '-' . ($data['region'] ?? '') . '-'. ($data['city'] ?? '');
    $ipAddress = (new \app\server\BaiduApiService())->translate($str, 'auto', 'zh');
    \support\Redis::set($key, $ipAddress);
    return $ipAddress;
}

function getTradeNo($head = '')
{
    list($msec, $sec) = explode(' ', microtime());
    $a = mt_rand(10, 99999)  . substr($msec, 2, 5);
    $num = time() + $a + substr(str_shuffle(str_repeat('123456789', 5)), 0, 10);

    return $head.date('md').$num;
}


if ( ! function_exists("str2Arr")) {
    /**
     * 字符串转数组
     *
     * @param string $text 待转内容
     * @param string $spear 分隔符
     * @param null|array $allow 限定规则
     *
     * @return array
     *
     * @uses lixuecong <<EMAIL>>
     *
     */
    function str2Arr(string $text, string $spear = ',', ?array $allow = null): array
    {
        return Conversion::str2Arr($text, $spear, $allow);
    }
}

if ( ! function_exists("arr2Int")) {
    /**
     * 字符串转数组
     *
     * @param array $arr 数组
     *
     * @return array
     *
     * @uses lixuecong <<EMAIL>>
     */
    function arr2Int(array $arr): array
    {
        foreach ($arr as &$value) {
            $value = (int)$value;
        }

        return $arr;
    }
}


if ( ! function_exists("arr2Str")) {
    /**
     * 数组转字符串
     *
     * @param array $data 待转数组
     * @param string $spear 分隔字符
     * @param null|array $allow 限定规则
     *
     * @return string
     *
     * @uses lixuecong <<EMAIL>>
     */
    function arr2Str(array $data, string $spear = ',', ?array $allow = null): string
    {
        return Conversion::arr2Str($data, $spear, $allow);
    }
}

if ( ! function_exists("arr2Json")) {
    /**
     * 数组转JSON
     *
     * @param array $array
     * @param int $options
     *
     * @return mixed
     * @throws JsonException
     */
    function arr2Json(array $array, int $options = JSON_UNESCAPED_UNICODE)
    {
        return Conversion::arr2Json($array, $options);
    }
}


if ( ! function_exists('get_date_format_range')) {
    /**
     * 获取指定日期段内每一天的日期
     *
     * @param string $start_time 开始日期  20180612
     * @param string $end_time 结束日期  20180620
     * @param string $format 日期格式
     *
     * @return Array
     */

    function get_date_format_range(string $start_time, string $end_time, string $format = 'Ymd'): array
    {
        $start_timestamp = strtotime($start_time);
        $end_timestamp   = strtotime($end_time);
        $days            = ($end_timestamp - $start_timestamp) / 86400 + 1;
        $date            = [];
        for ($i = 0; $i < $days; $i++) {
            $date[] = date($format, $start_timestamp + (86400 * $i));
        }

        return $date;
    }
}


if ( ! function_exists('get_date_rate')) {
    function get_date_rate($startDate, $endDate): array
    {
        //    if (date('Y-m-d', strtotime($startDate)) !== $startDate || date('Y-m-d', strtotime($endDate)) !== $endDate) {
        //        return '日期格式不正确';
        //    }
        $temp_date   = date('Y-m-d', strtotime($startDate));
        $endDate     = date('Y-m-d', strtotime($endDate));
        $return_data = [];
        $i           = 0;

        while (strtotime($temp_date) < strtotime($endDate)) {
            $temp         = [];
            $month        = strtotime('first day of +' . $i . ' month', strtotime($startDate));
            $temp['name'] = date('m', $month);
            if ($i === 0) {
                $temp['startDate'] = date('Y-m-d', strtotime($startDate));
            } else {
                $temp['startDate'] = date('Y-m-01', $month);
            }
            if (date('Y-m', strtotime($temp['startDate'])) === date('Y-m', strtotime($endDate))) {
                $temp['endDate'] = date('Y-m-d', strtotime($endDate));
            } else {
                $temp['endDate'] = date('Y-m-t', $month);
            }

            $temp_date     = $temp['endDate'];
            $return_data[] = $temp;
            $i++;
        }


        return $return_data;
    }
}

if ( ! function_exists("success")) {
    /**
     * 正常输出
     *
     * @param array|null $data
     * @param string $msg
     * @param int $code
     *
     *
     * @return Response
     * @author: lixuecong <<EMAIL>>
     *
     * @copyright  2022/2/22 16:15
     */
    function success(?array $data = [], string $msg = 'success', int $code = 200)
    {
        return Output::success($data, $msg, $code);
    }
}

if ( ! function_exists("error")) {
    /**
     * 异常输出
     *
     * @param int $code
     * @param string $msg
     * @param array|null $data
     *
     * @return \support\Response
     *
     * @author: lixuecong <<EMAIL>>
     *
     * @copyright  2022/2/22 16:15
     */
    function error(int $code = 404, string $msg = 'error', ?array $data = []): \support\Response
    {
        return Output::error($code, $msg, $data);
    }
}
function errorNew($msg = 'error',$data = [],$code = 500){
    return Output::error($code, $msg, $data);
}
