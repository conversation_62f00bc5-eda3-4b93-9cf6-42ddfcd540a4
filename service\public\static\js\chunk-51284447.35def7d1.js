(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-51284447"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),Math.easeInOutQuad=function(t,e,a,r){return t/=r/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var r=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,a){var s=i(),o=t-s,c=20,l=0;e="undefined"===typeof e?500:e;var d=function(){l+=c;var t=Math.easeInOutQuad(l,s,o,e);n(t),l<e?r(d):a&&"function"===typeof a&&a()};d()}},"0e1f":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-row",{staticClass:"stats-row",attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"stats-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("今日分配总数")])]),a("div",{staticClass:"stats-number"},[t._v(t._s(t.statistics.total_count||0))])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"stats-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("分配客服数")])]),a("div",{staticClass:"stats-number"},[t._v(t._s(t.statistics.admin_stats&&t.statistics.admin_stats.length||0))])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"stats-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("涉及门店数")])]),a("div",{staticClass:"stats-number"},[t._v(t._s(t.statistics.shop_stats&&t.statistics.shop_stats.length||0))])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"stats-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("平均每小时")])]),a("div",{staticClass:"stats-number"},[t._v(t._s(t.avgPerHour))])])],1)],1),a("el-card",{staticClass:"filter-container"},[a("el-form",{ref:"filterForm",staticClass:"demo-form-inline",attrs:{model:t.listQuery,inline:!0}},[a("el-form-item",{attrs:{label:"订单号"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入订单号",clearable:""},model:{value:t.listQuery.order_sn,callback:function(e){t.$set(t.listQuery,"order_sn",e)},expression:"listQuery.order_sn"}})],1),a("el-form-item",{attrs:{label:"客服"}},[a("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择客服",clearable:""},model:{value:t.listQuery.admin_id,callback:function(e){t.$set(t.listQuery,"admin_id",e)},expression:"listQuery.admin_id"}},t._l(t.adminOptions,(function(t){return a("el-option",{key:t.id,attrs:{label:t.username,value:t.id}})})),1)],1),a("el-form-item",{attrs:{label:"门店"}},[a("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择门店",clearable:""},model:{value:t.listQuery.shop_id,callback:function(e){t.$set(t.listQuery,"shop_id",e)},expression:"listQuery.shop_id"}},t._l(t.shopOptions,(function(t){return a("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),a("el-form-item",{attrs:{label:"分配时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd","value-format":"yyyy-MM-dd"},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleFilter}},[t._v(" 搜索 ")]),a("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:t.resetFilter}},[t._v(" 重置 ")]),a("el-button",{attrs:{type:"success",icon:"el-icon-download"},on:{click:t.handleExport}},[t._v(" 导出 ")])],1)],1)],1),a("el-card",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,"element-loading-text":"加载中",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.id)+" ")]}}])}),a("el-table-column",{attrs:{label:"订单号",width:"180",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:"info"}},[t._v(t._s(e.row.order_sn))])]}}])}),a("el-table-column",{attrs:{label:"客服信息",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",[a("div",[t._v(t._s(e.row.admin&&e.row.admin.name||"--"))]),a("div",{staticClass:"text-muted"},[t._v(t._s(e.row.admin&&e.row.admin.username||"--"))])])]}}])}),a("el-table-column",{attrs:{label:"门店",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.shop&&e.row.shop.name||"--")+" ")]}}])}),a("el-table-column",{attrs:{label:"分配时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.parseTime(1e3*e.row.allocate_time,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])}),a("el-table-column",{attrs:{label:"分配方式",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tag",{attrs:{type:t.getMethodTagType(e.row.extra_data&&e.row.extra_data.allocate_method)}},[t._v(" "+t._s(e.row.extra_data&&e.row.extra_data.allocate_method||"未知")+" ")])]}}])}),a("el-table-column",{attrs:{label:"手机号",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.extra_data&&e.row.extra_data.mobile||"--")+" ")]}}])}),a("el-table-column",{attrs:{label:"线路权限","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("route-permission-display",{attrs:{"route-permission":e.row.route_permission,"product-cache":t.productCache},on:{"view-more":t.handleViewMoreRoute}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"100","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return t.handleDetail(e.row)}}},[t._v(" 详情 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}})],1),a("el-dialog",{attrs:{title:"分配记录详情",visible:t.detailDialogVisible,width:"60%"},on:{"update:visible":function(e){t.detailDialogVisible=e}}},[t.currentRecord?a("div",[a("el-card",{staticClass:"detail-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("基本信息")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("记录ID：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.id))])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("订单号：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.order_sn))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("客服姓名：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.admin&&t.currentRecord.admin.name||"--"))])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("客服用户名：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.admin&&t.currentRecord.admin.username||"--"))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("门店名称：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.shop&&t.currentRecord.shop.name||"--"))])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("分配时间：")]),a("span",{staticClass:"detail-value"},[t._v(" "+t._s(t.parseTime(1e3*t.currentRecord.allocate_time,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")])])])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("线路权限：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.route_permission||"--"))])])])],1)],1),a("el-card",{staticClass:"detail-card",staticStyle:{"margin-top":"15px"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("额外信息")])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("分配方式：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.extra_data&&t.currentRecord.extra_data.allocate_method||"--"))])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("手机号：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.extra_data&&t.currentRecord.extra_data.mobile||"--"))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("商品ID：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.extra_data&&t.currentRecord.extra_data.product_id||"--"))])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("商品名称：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.extra_data&&t.currentRecord.extra_data.product_name||"--"))])])])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("订单类型：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.extra_data&&t.currentRecord.extra_data.order_type||"normal"))])])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"detail-item"},[a("span",{staticClass:"detail-label"},[t._v("数据来源：")]),a("span",{staticClass:"detail-value"},[t._v(t._s(t.currentRecord.extra_data&&t.currentRecord.extra_data.source||"--"))])])])],1)],1),t.currentRecord.extra_data&&t.currentRecord.extra_data.old_order_sn?a("div",{staticStyle:{"margin-top":"15px"}},[a("el-alert",{attrs:{title:"历史分配信息",description:"基于历史订单 "+(t.currentRecord.extra_data&&t.currentRecord.extra_data.old_order_sn)+" 进行分配",type:"info","show-icon":"",closable:!1}})],1):t._e()],1):t._e(),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.detailDialogVisible=!1}}},[t._v("关闭")])],1)]),a("el-dialog",{attrs:{title:"线路权限详情",visible:t.routePermissionDialog,width:"60%"},on:{"update:visible":function(e){t.routePermissionDialog=e}}},[t.currentRouteProducts.length>0?a("div",[a("div",{staticClass:"route-products-grid"},t._l(t.currentRouteProducts,(function(e){return a("el-card",{key:e.id,staticClass:"product-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"product-info"},[a("div",{staticClass:"product-name"},[t._v(t._s(e.name))]),a("div",{staticClass:"product-id"},[t._v("ID: "+t._s(e.id))]),e.third_product_id?a("div",{staticClass:"product-third-id"},[t._v(" 第三方ID: "+t._s(e.third_product_id)+" ")]):t._e()])])})),1)]):a("div",{staticClass:"no-products"},[a("el-empty",{attrs:{description:"暂无线路权限"}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.routePermissionDialog=!1}}},[t._v("关闭")])],1)])],1)},n=[],i=a("d09a"),s=a("756f"),o=a("0fc4"),c=a("7921"),l=(a("e168"),a("3dd5"),a("1652"),a("e224"),a("4cc3"),a("8b03"),a("7019"),a("452e"),a("9919"),a("5227"),a("90c8"),a("4a51"),a("3363"),a("bd1a"),a("f2e9"),a("3399"),a("b775"));function d(t){return Object(l["a"])({url:"/admin/order-allocate-log/index",method:"get",params:t})}function u(t){return Object(l["a"])({url:"/admin/order-allocate-log/statistics",method:"get",params:t})}function p(t){return Object(l["a"])({url:"/admin/order-allocate-log/show",method:"get",params:t})}function m(t){return Object(l["a"])({url:"/admin/order-allocate-log/getProducts",method:"get",params:{ids:t}})}var h=a("50fc"),f=a("ed08"),g=a("333d"),_={name:"OrderAllocateLog",components:{Pagination:g["a"],RoutePermissionDisplay:{props:{routePermission:{type:String,default:""},productCache:{type:Object,default:function(){return{}}}},data:function(){return{products:[],loading:!1}},computed:{displayProducts:function(){return this.products.slice(0,3)},hasMore:function(){return this.products.length>3},displayText:function(){if(0===this.products.length)return"--";var t=this.displayProducts.map((function(t){return t.name}));return t.join("，")+(this.hasMore?"...":"")}},watch:{routePermission:{immediate:!0,handler:function(t){this.loadProducts(t)}},productCache:{deep:!0,handler:function(){this.loadProducts(this.routePermission)}}},methods:{parseRoutePermission:function(t){if(!t||"--"===t)return[];var e=t.split(",").filter((function(t){return t.trim()}));return e.map((function(t){return parseInt(t.trim())})).filter((function(t){return!isNaN(t)}))},loadProducts:function(t){var e=this,a=this.parseRoutePermission(t);0!==a.length?this.products=a.map((function(t){return e.productCache[t]||{id:t,name:"产品".concat(t)}})):this.products=[]},handleViewMore:function(){this.$emit("view-more",this.routePermission)}},render:function(t){if(0===this.products.length)return t("span","--");var e=this.displayProducts.map((function(e){return t("el-tag",{props:{size:"small"},class:"route-tag",key:e.id},e.name)}));return this.hasMore&&e.push(t("el-button",{props:{type:"text",size:"mini"},class:"view-more-btn",on:{click:this.handleViewMore}},"查看更多(".concat(this.products.length,")"))),t("div",{class:"route-permission-display"},e)}}},data:function(){return{list:[],total:0,listLoading:!0,statistics:{},adminOptions:[],shopOptions:[],dateRange:[],listQuery:{page:1,limit:20,order_sn:"",admin_id:"",shop_id:"",start_time:"",end_time:""},detailDialogVisible:!1,currentRecord:null,productCache:{},routePermissionDialog:!1,currentRouteProducts:[]}},computed:{avgPerHour:function(){if(!this.statistics.hourly_stats||0===this.statistics.hourly_stats.length)return 0;var t=this.statistics.hourly_stats.reduce((function(t,e){return t+e.count}),0);return Math.round(t/24)}},created:function(){this.getList(),this.getStatistics(),this.getAdminOptions()},methods:{parseTime:f["d"],parseRoutePermission:function(t){if(!t||"--"===t)return[];var e=t.split(",").filter((function(t){return t.trim()}));return e.map((function(t){return parseInt(t.trim())})).filter((function(t){return!isNaN(t)}))},getProductNames:function(t){var e=this;return Object(c["a"])(Object(o["a"])().mark((function a(){var r,n;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t&&0!==t.length){a.next=2;break}return a.abrupt("return",[]);case 2:if(r=t.filter((function(t){return!e.productCache[t]})),!(r.length>0)){a.next=14;break}return a.prev=4,a.next=7,m(r.join(","));case 7:n=a.sent,Object.assign(e.productCache,n.data||{}),a.next=14;break;case 11:a.prev=11,a.t0=a["catch"](4),console.error("获取产品信息失败:",a.t0);case 14:return a.abrupt("return",t.map((function(t){return e.productCache[t]||{id:t,name:"产品".concat(t)}})));case 15:case"end":return a.stop()}}),a,null,[[4,11]])})))()},formatRoutePermission:function(t){var e=arguments,a=this;return Object(c["a"])(Object(o["a"])().mark((function r(){var n,i,s,c,l,d;return Object(o["a"])().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(n=e.length>1&&void 0!==e[1]&&e[1],i=a.parseRoutePermission(t),0!==i.length){r.next=4;break}return r.abrupt("return",{text:"--",products:[],hasMore:!1});case 4:return r.next=6,a.getProductNames(i);case 6:return s=r.sent,c=n?s:s.slice(0,3),l=s.length>3,d=c.map((function(t){return t.name})).join("，")+(l&&!n?"...":""),r.abrupt("return",{text:d,products:s,hasMore:l,displayProducts:c});case 11:case"end":return r.stop()}}),r)})))()},handleViewMoreRoute:function(t){var e=this;return Object(c["a"])(Object(o["a"])().mark((function a(){var r;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.formatRoutePermission(t,!0);case 2:r=a.sent,e.currentRouteProducts=r.products,e.routePermissionDialog=!0;case 5:case"end":return a.stop()}}),a)})))()},getList:function(){var t=this;this.listLoading=!0,this.dateRange&&2===this.dateRange.length?(this.listQuery.start_time=this.dateRange[0],this.listQuery.end_time=this.dateRange[1]):(this.listQuery.start_time="",this.listQuery.end_time=""),d(this.listQuery).then(function(){var e=Object(c["a"])(Object(o["a"])().mark((function e(a){var r;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.list=a.data.data,t.total=a.data.total,r=a.data.data.map((function(t){return t.shop})).filter((function(t){return t})),t.shopOptions=Object(s["a"])(new Map(r.map((function(t){return[t.id,t]}))).values()),e.next=6,t.preloadProductInfo(t.list);case 6:t.listLoading=!1;case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(){t.listLoading=!1}))},preloadProductInfo:function(t){var e=this;return Object(c["a"])(Object(o["a"])().mark((function a(){var r,n;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(r=new Set,t.forEach((function(t){var a=e.parseRoutePermission(t.route_permission);a.forEach((function(t){return r.add(t)}))})),!(r.size>0)){a.next=13;break}return a.prev=3,a.next=6,m(Array.from(r).join(","));case 6:n=a.sent,Object.assign(e.productCache,n.data||{}),a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](3),console.error("预加载产品信息失败:",a.t0);case 13:case"end":return a.stop()}}),a,null,[[3,10]])})))()},getStatistics:function(){var t=this,e={start_time:this.dateRange&&this.dateRange[0]||(new Date).toISOString().split("T")[0],end_time:this.dateRange&&this.dateRange[1]||(new Date).toISOString().split("T")[0]};u(e).then((function(e){t.statistics=e.data||{}}))},getAdminOptions:function(){var t=this;Object(h["c"])({limit:1e3}).then((function(e){e.data&&e.data.data?t.adminOptions=e.data.data||[]:e.data&&Array.isArray(e.data)?t.adminOptions=e.data:t.adminOptions=[]})).catch((function(e){console.error("获取客服列表失败:",e),t.adminOptions=[]}))},handleFilter:function(){this.listQuery.page=1,this.getList(),this.getStatistics()},resetFilter:function(){this.listQuery={page:1,limit:20,order_sn:"",admin_id:"",shop_id:"",start_time:"",end_time:""},this.dateRange=[],this.getList(),this.getStatistics()},handleDetail:function(t){var e=this;p({id:t.id}).then((function(t){e.currentRecord=t.data,e.detailDialogVisible=!0})).catch((function(t){console.error("获取详情失败:",t),e.$message.error("获取记录详情失败")}))},handleExport:function(){var t=Object(i["a"])({},this.listQuery);delete t.page,delete t.limit,this.dateRange&&2===this.dateRange.length&&(t.start_time=this.dateRange[0],t.end_time=this.dateRange[1]);var e=Object.keys(t).filter((function(e){return""!==t[e]&&null!==t[e]})).map((function(e){return"".concat(e,"=").concat(encodeURIComponent(t[e]))})).join("&"),a="".concat("/","/admin/order-allocate-log/export?").concat(e);window.open(a)},getMethodTagType:function(t){var e={"在线客服分配":"primary","历史客服分配":"success","过滤手机号分配":"warning","公海重新分配":"info","分配失败":"danger"};return e[t]||"info"}}},v=_,b=(a("a366"),a("8a34")),y=Object(b["a"])(v,r,n,!1,null,"0379e48c",null);e["default"]=y.exports},"2cbf":function(t,e,a){"use strict";a("bac3")},"333d":function(t,e,a){"use strict";var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[a("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},n=[],i=(a("374d"),a("09f4")),s={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(i["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(i["a"])(0,800)}}},o=s,c=(a("2cbf"),a("8a34")),l=Object(c["a"])(o,r,n,!1,null,"6af373ef",null);e["a"]=l.exports},"50fc":function(t,e,a){"use strict";a.d(e,"f",(function(){return n})),a.d(e,"b",(function(){return i})),a.d(e,"h",(function(){return s})),a.d(e,"k",(function(){return o})),a.d(e,"e",(function(){return c})),a.d(e,"a",(function(){return l})),a.d(e,"j",(function(){return d})),a.d(e,"m",(function(){return u})),a.d(e,"g",(function(){return p})),a.d(e,"i",(function(){return m})),a.d(e,"l",(function(){return h})),a.d(e,"d",(function(){return f})),a.d(e,"n",(function(){return g})),a.d(e,"c",(function(){return _}));a("e168");var r=a("b775");function n(){return Object(r["a"])({url:"admin/products/list",method:"get"})}function i(t){return Object(r["a"])({url:"/admin/products/add",method:"post",data:t})}function s(t){var e,a;return Object(r["a"])({url:"/admin/products/list?page=".concat(t.page,"&limit=").concat(t.limit,"&product_name=").concat(null!==(e=t.product_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(a=t.third_product_id)&&void 0!==a?a:""),method:"get"})}function o(t){return Object(r["a"])({url:"/admin/products/add",method:"post",data:t})}function c(t){return Object(r["a"])({url:"/admin/products/productschedules?id=".concat(t.id,"&date=").concat(t.date),method:"get"})}function l(t){return Object(r["a"])({url:"/admin/products/addproductschedules",method:"post",data:t})}function d(t){var e,a;return Object(r["a"])({url:"/admin/routes/list?page=".concat(t.page,"&limit=").concat(t.limit,"&route_name=").concat(null!==(e=t.route_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(a=t.third_product_id)&&void 0!==a?a:""),method:"get"})}function u(t){return Object(r["a"])({url:"/admin/routes/add",method:"post",data:t})}function p(){return Object(r["a"])({url:"admin/products-xs/list",method:"get"})}function m(t){var e,a;return Object(r["a"])({url:"/admin/products-xs/list?page=".concat(t.page,"&limit=").concat(t.limit,"&product_name=").concat(null!==(e=t.product_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(a=t.third_product_id)&&void 0!==a?a:""),method:"get"})}function h(t){return Object(r["a"])({url:"/admin/products-xs/add",method:"post",data:t})}function f(){return Object(r["a"])({url:"/admin/setting/getContractSetting",method:"get"})}function g(t){return Object(r["a"])({url:"/admin/setting/savecontractsetting",method:"post",data:t})}function _(t){return Object(r["a"])({url:"/admin/admin/index",method:"get",params:t})}},"893e":function(t,e,a){},9919:function(t,e,a){"use strict";var r=a("054c"),n=a("6665");t.exports=r("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),n)},a366:function(t,e,a){"use strict";a("893e")},bac3:function(t,e,a){}}]);