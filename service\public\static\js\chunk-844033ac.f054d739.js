(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-844033ac"],{"09f4":function(t,e,n){"use strict";n.d(e,"a",(function(){return l})),Math.easeInOutQuad=function(t,e,n,s){return t/=s/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var s=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function a(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(t,e,n){var l=i(),o=t-l,c=20,r=0;e="undefined"===typeof e?500:e;var u=function(){r+=c;var t=Math.easeInOutQuad(r,l,o,e);a(t),r<e?s(u):n&&"function"===typeof n&&n()};u()}},"38af":function(t,e,n){},"53b1":function(t,e,n){"use strict";n("38af")},"67f2":function(t,e,n){"use strict";var s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[n("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},a=[],i=(n("374d"),n("09f4")),l={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(i["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(i["a"])(0,800)}}},o=l,c=(n("7d30"),n("8a34")),r=Object(c["a"])(o,s,a,!1,null,"28fdfbeb",null);e["a"]=r.exports},7140:function(t,e,n){},"7d30":function(t,e,n){"use strict";n("7140")},ca9b:function(t,e,n){"use strict";var s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.visible?n("div",{staticClass:"softphone-container"},[n("div",{staticClass:"softphone-header"},[n("div",{staticClass:"status-info"},[n("span",{staticClass:"status-text"},[t._v(t._s(t.statusText))]),"通话中"===t.statusText&&t.callDuration>0?n("span",{staticClass:"call-time"},[t._v(t._s(t.formatTime(t.callDuration)))]):t._e()]),n("div",{staticClass:"header-actions"},[t.isInCall||"呼叫中"===t.statusText||"通话中"===t.statusText?n("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-phone-outline",title:"挂断"},on:{click:t.hangup}}):n("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-phone",title:"拨号"},on:{click:t.toggleCall}}),n("el-button",{attrs:{size:"mini",icon:"el-icon-close",title:"关闭"},on:{click:t.closePanel}})],1)]),n("div",{staticClass:"softphone-body"},[n("div",{staticClass:"settings-section"},[n("div",{staticClass:"setting-title"},[t._v("通话设置")]),n("div",{staticClass:"setting-item"},[n("label",[t._v("接听方式")]),n("el-select",{attrs:{size:"small","popper-append-to-body":!1,"popper-class":"softphone-select-dropdown"},on:{change:t.onLoginTypeChange},model:{value:t.loginType,callback:function(e){t.loginType=e},expression:"loginType"}},[n("el-option",{attrs:{label:"手机号",value:"Local"}}),n("el-option",{attrs:{label:"sip话机",value:"sip"}}),n("el-option",{attrs:{label:"webrtc",value:"gateway"}})],1)],1),n("div",{staticClass:"setting-item"},[n("label",[t._v("手机号码")]),n("div",{staticClass:"phone-display"},[t._v(t._s(t.agentPhone))])]),t.softphoneInstance?t.softphoneInstance.callApi?n("div",{staticClass:"setting-item"},[n("label",[t._v("状态")]),n("div",{staticStyle:{color:"#67c23a","font-size":"12px"}},[t._v("已就绪")])]):n("div",{staticClass:"setting-item"},[n("label",[t._v("状态")]),n("div",{staticStyle:{color:"#e6a23c","font-size":"12px"}},[t._v("初始化中...")])]):n("div",{staticClass:"setting-item"},[n("label",[t._v("状态")]),n("div",{staticStyle:{color:"#f56c6c","font-size":"12px"}},[t._v("未初始化")])])]),t.showDialer?n("div",{staticClass:"dialer-section"},[n("div",{staticClass:"dialer-input"},[n("el-input",{attrs:{placeholder:"请输入电话号码",size:"small"},model:{value:t.dialNumber,callback:function(e){t.dialNumber=e},expression:"dialNumber"}})],1),n("div",{staticClass:"dialer-buttons"},[n("div",{staticClass:"number-pad"},t._l(["1","2","3","4","5","6","7","8","9","*","0","#"],(function(e){return n("el-button",{key:e,attrs:{size:"mini"},on:{click:function(n){return t.addNumber(e)}}},[t._v(t._s(e))])})),1)]),n("div",{staticClass:"call-actions"},[n("el-button",{attrs:{type:"success",size:"small",icon:"el-icon-phone",disabled:!t.dialNumber},on:{click:function(e){return t.makeCall()}}},[t._v("拨号")]),n("el-button",{attrs:{size:"small",icon:"el-icon-delete"},on:{click:t.clearNumber}},[t._v("清除")])],1)]):t._e(),t.isInCall?n("div",{staticClass:"call-controls"},[n("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-phone-outline"},on:{click:t.hangup}},[t._v("挂断")]),n("el-button",{attrs:{type:"warning",size:"small",icon:t.isHeld?"el-icon-video-play":"el-icon-video-pause"},on:{click:t.toggleHold}},[t._v(t._s(t.isHeld?"取消保持":"保持"))])],1):t._e(),n("div",{staticClass:"action-buttons"},[n("el-button",{attrs:{size:"small",icon:"el-icon-setting"},on:{click:function(e){t.showDialer=!t.showDialer}}},[t._v(t._s(t.showDialer?"隐藏拨号盘":"显示拨号盘"))]),n("el-button",{attrs:{size:"small",type:"warning",icon:"el-icon-refresh",title:"清理状态"},on:{click:t.forceCleanState}},[t._v("清理")])],1)])]):t._e()},a=[],i=n("0fc4"),l=n("7921"),o=(n("e168"),n("d987"),n("90c8"),n("fa87"),n("6de1"),n("04b0"),n("bd1a"),n("f2e9"),{name:"SoftPhone",props:{visible:{type:Boolean,default:!0},softphoneInstance:{type:Object,default:null},initialNumber:{type:String,default:""},currentLoginType:{type:String,default:"Local"}},data:function(){return{statusText:"空闲",callDuration:0,isInCall:!1,isHeld:!1,loginType:this.currentLoginType,agentPhone:"8000@xglgj",showDialer:!0,dialNumber:"",callTimer:null}},watch:{softphoneInstance:{handler:function(t){t&&this.initSoftphoneEvents(t)},immediate:!0},initialNumber:{handler:function(t){t&&(this.dialNumber=t)},immediate:!0},currentLoginType:{handler:function(t,e){e&&t!==e&&(console.log("接听方式变化，从",e,"到",t),this.loginType=t,this.softphoneInstance&&this.onLoginTypeChange(t))},immediate:!0}},methods:{formatTime:function(t){var e=Math.floor(t/3600),n=Math.floor(t%3600/60),s=t%60;return e>0?"".concat(e.toString().padStart(2,"0"),":").concat(n.toString().padStart(2,"0"),":").concat(s.toString().padStart(2,"0")):"".concat(n.toString().padStart(2,"0"),":").concat(s.toString().padStart(2,"0"))},initSoftphoneEvents:function(t){var e=this;t&&t.attachEvent({success:function(){console.log("软电话事件绑定成功"),setTimeout((function(){e.forceCleanState()}),1e3)},message:function(t){console.log("收到软电话消息:",t),t.event?e.handleCallEvent(t.event):t.Event?e.handleCallEvent(t):Array.isArray(t)&&t.length>0&&t[0].Event?t.forEach((function(t){e.handleCallEvent(t)})):e.handleCallEvent(t)},error:function(t){console.log("软电话事件绑定失败:",t)}})},handleCallEvent:function(t){if(t)if(console.log("通话事件:",t),"ChannelStatus"!==t.Event){if(t.type)switch(console.log("通话事件类型:",t.type),t.type){case"peerstate":this.updateAgentStatus(t.typeValue);break;case"dialing":this.statusText="呼叫中",this.isInCall=!1,this.callDuration=0;break;case"dialTalking":case"innerTalking":console.log("通话已接通，开始计时"),this.statusText="通话中",this.isInCall=!0,this.startCallTimer();break;case"hangup":this.endCall();break;default:console.log("其他事件类型:",t.type)}}else switch(console.log("收到ChannelStatus事件:",t.ChannelStatus),t.ChannelStatus){case"Hangup":console.log("通话已挂断，重置状态"),this.endCall();break;case"Ringing":this.statusText="呼叫中",this.isInCall=!1,this.callDuration=0;break;case"Up":case"Answer":this.statusText="通话中",this.isInCall=!0,this.startCallTimer();break;default:console.log("其他ChannelStatus:",t.ChannelStatus)}},updateAgentStatus:function(t){switch(console.log("更新坐席状态:",t),t){case"0":console.log("坐席状态变为空闲，重置通话状态"),this.endCall();break;case"1":this.statusText="忙碌";break;default:this.statusText="未知状态"}},startCallTimer:function(){var t=this;this.callTimer&&clearInterval(this.callTimer),this.callDuration=0,this.callTimer=setInterval((function(){t.callDuration++}),1e3)},stopCallTimer:function(){this.callTimer&&(clearInterval(this.callTimer),this.callTimer=null),this.callDuration=0},endCall:function(){var t=this;console.log("结束通话，重置所有状态"),this.stopCallTimer(),this.statusText="空闲",this.isInCall=!1,this.isHeld=!1,this.callDuration=0,this.$nextTick((function(){t.callDuration=0,console.log("通话状态已完全重置 - isInCall:",t.isInCall,"callDuration:",t.callDuration,"statusText:",t.statusText)})),this.$emit("call-ended")},toggleCall:function(){this.isInCall?this.hangup():this.showDialer=!0},makeCall:function(t){var e,n=this;if(e="string"===typeof t&&t.length>0?t:this.dialNumber,e&&""!==e.trim())if(this.softphoneInstance)if(this.softphoneInstance.callApi){if(this.dialNumber=e,console.log("发起拨号前状态检查 - isInCall:",this.isInCall,"statusText:",this.statusText,"callDuration:",this.callDuration),this.isInCall||"呼叫中"===this.statusText||"通话中"===this.statusText||this.callDuration>0)return console.log("检测到通话状态异常，提示用户确认"),void this.$confirm("当前已有通话，是否先挂断现有通话再发起新呼叫？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.forceCleanState(),setTimeout((function(){n.makeCall(e)}),1e3)}));try{console.log("发起外呼:",e),console.log("softphoneInstance:",this.softphoneInstance),console.log("callApi:",this.softphoneInstance.callApi),this.statusText="呼叫中",this.softphoneInstance.callApi.dialout({calleeNumber:e,success:function(t){console.log("外呼成功:",t),n.$message.success("呼叫发起成功")},fail:function(t){console.log("外呼失败:",t),t.message&&t.message.includes("calling")?n.$message.error("坐席正在通话中，请先挂断现有通话再发起新呼叫"):n.$message.error("呼叫失败: ".concat(t.message||"未知错误")),n.statusText="空闲"}})}catch(s){console.error("呼叫异常:",s),this.$message.error("呼叫异常: "+s.message),this.statusText="空闲"}}else this.$message.error("软电话API未就绪，请等待初始化完成");else this.$message.error("软电话未初始化，请先初始化软电话");else this.$message.error("请输入电话号码")},hangup:function(){var t=this;if(this.softphoneInstance)if(this.softphoneInstance.callApi){console.log("执行挂断操作，当前状态:",this.statusText);try{this.softphoneInstance.callApi.hangup({success:function(){console.log("挂断成功"),t.$message.success("挂断成功"),t.endCall()},fail:function(e){console.log("挂断失败:",e),t.$message.error("挂断失败: ".concat(e.message||"未知错误")),t.endCall()}})}catch(e){console.error("挂断异常:",e),this.$message.error("挂断操作异常"),this.endCall()}}else this.$message.warning("软电话API未就绪");else this.$message.warning("软电话未初始化")},toggleHold:function(){var t=this;this.softphoneInstance&&(this.isHeld?this.softphoneInstance.callApi.unhold({success:function(){t.isHeld=!1,t.$message.success("已取消保持")},fail:function(e){t.$message.error("取消保持失败")}}):this.softphoneInstance.callApi.hold({success:function(){t.isHeld=!0,t.$message.success("已保持通话")},fail:function(e){t.$message.error("保持通话失败")}}))},addNumber:function(t){this.dialNumber+=t},clearNumber:function(){this.dialNumber=""},onLoginTypeChange:function(t){this.$emit("login-type-change",t),this.reInitSoftphone(t)},reInitSoftphone:function(t){var e=this;return Object(l["a"])(Object(i["a"])().mark((function n(){return Object(i["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:try{e.softphoneInstance&&e.softphoneInstance.destroy&&"function"===typeof e.softphoneInstance.destroy&&e.softphoneInstance.destroy(),e.statusText="重新登录中...",e.$message.info("正在切换到".concat("Local"===t?"手机号":"sip"===t?"SIP话机":"WebRTC","模式...")),e.$emit("reinit-softphone",t)}catch(s){console.error("重新初始化失败:",s),e.$message.error("切换登录方式失败")}case 1:case"end":return n.stop()}}),n)})))()},closePanel:function(){this.$emit("close")},updateStatus:function(t){this.statusText=t},forceCleanState:function(){var t=this;if(console.log("强制清理软电话状态"),this.callTimer&&(clearInterval(this.callTimer),this.callTimer=null),this.statusText="空闲",this.isInCall=!1,this.isHeld=!1,this.callDuration=0,this.softphoneInstance&&this.softphoneInstance.callApi)try{this.softphoneInstance.callApi.hangup({success:function(){return console.log("清理残留通话成功")},fail:function(){return console.log("无残留通话需要清理")}})}catch(e){console.log("清理状态时出错:",e)}this.$nextTick((function(){t.callDuration=0,t.isInCall=!1,t.statusText="空闲",console.log("强制清理完成 - isInCall:",t.isInCall,"callDuration:",t.callDuration,"statusText:",t.statusText)})),this.$message.success("状态已清理")},syncCallState:function(t){console.log("手动同步通话状态:",t),"idle"===t||"hangup"===t?this.endCall():"calling"===t||"dialing"===t?(this.statusText="呼叫中",this.isInCall=!1,this.callDuration=0):"talking"!==t&&"connected"!==t||(this.statusText="通话中",this.isInCall=!0,this.startCallTimer())}},beforeDestroy:function(){this.stopCallTimer()}}),c=o,r=(n("53b1"),n("8a34")),u=Object(r["a"])(c,s,a,!1,null,"02143e16",null);e["a"]=u.exports},f8b7:function(t,e,n){"use strict";n.d(e,"a",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"b",(function(){return l})),n.d(e,"d",(function(){return o}));var s=n("b775");function a(t){return Object(s["a"])({url:"/admin/order/back",method:"post",data:t})}function i(t){return Object(s["a"])({url:"admin/order/backBatch",method:"post",data:t})}function l(t){return Object(s["a"])({url:"/admin/order-xs/back",method:"post",data:t})}function o(t){return Object(s["a"])({url:"admin/order-xs/backBatch",method:"post",data:t})}}}]);