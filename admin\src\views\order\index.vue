<template>
  <div class="app-container">
    <!-- 软电话控件 -->
    <SoftPhone 
      ref="softphone"
      :visible="softphoneVisible"
      :softphoneInstance="moorSoftphone"
      :initialNumber="currentPhoneNumber"
      :currentLoginType="currentLoginType"
      @close="softphoneVisible = false"
      @login-type-change="onLoginTypeChange"
      @reinit-softphone="onReinitSoftphone"
    />
    
    <div class="filter-container">
      <el-input
        v-model="listQuery.sn"
        placeholder="订单号"
        style="width: 200px"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.product_name"
        placeholder="标题"
        style="width: 200px"
        class="filter-item"
      />

      <el-input
        v-model="listQuery.mobile"
        placeholder="手机号"
        style="width: 200px"
        class="filter-item"
      />

      <el-input
        v-model="listQuery.zhubo"
        placeholder="主播"
        style="width: 100px"
        class="filter-item"
      />

      <el-input
        v-model="listQuery.admin"
        placeholder="客服"
        style="width: 100px"
        class="filter-item"
      />

      <el-cascader
        v-model="listQuery.os_status"
        placeholder="平台状态"
        :options="oss"
        clearable
        :props="{ checkStrictly: true }"
        class="filter-item"
        @change="handleChange"
      />

      <el-select
        v-model="listQuery.status"
        filterable
        placeholder="跟进状态"
        class="filter-item"
        style="width: 120px"
      >
        <el-option key="" label="请选择" value="" />
        <el-option
          v-for="(v, k) in status_arr"
          :key="k"
          :label="v"
          :value="k"
        />
      </el-select>

      <el-select
        v-model="listQuery.timetype"
        filterable
        placeholder="时间"
        class="filter-item"
        style="width: 120px"
      >
        <el-option key="" label="请选择" value="" />
        <el-option
          v-for="(v, k) in timetype_arr"
          :key="k"
          :label="v"
          :value="k"
        />
      </el-select>

      <el-date-picker
        v-model="listQuery.times"
        class="filter-item"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        :default-time="['00:00:00', '23:59:59']"
        end-placeholder="结束日期"
      />
      <el-select
        v-model="listQuery.appointment_status"
        filterable
        clearable
        placeholder="预约状态"
        class="filter-item"
        style="width: 120px"
      >
        <el-option key="0" label="未预约" value="0" />
        <el-option key="1" label="已预约(未处理)" value="1" />
        <el-option key="2" label="已预约(已处理)" value="2" />
      </el-select>
      <el-select
        v-model="listQuery.un_use_type"
        filterable
        clearable
        placeholder="待使用"
        class="filter-item"
        style="width: 120px"
      >
        <el-option key="1" label="30天待使用" value="1" />
        <el-option key="2" label="60天待使用" value="2" />
        <el-option key="3" label="80天待使用" value="3" />
      </el-select>


      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList"
      >
        搜索
      </el-button>

      <el-button
        v-if="checkPermission(['admin', 'franchisee'])"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList(1)"
      >
        导出
      </el-button>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="dialog2Visible = true"
      >
        核单
      </el-button>
      <el-button
        type="primary"
        :disabled="multipleSelection.length == 0"
        class="filter-item"
        icon="el-icon-refresh"
        @click="onCirculationAll()"
      >
        批量流转
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      :height="tableMaxHeight"
      @selection-change="handleSelectionChange"
      @sort-change="orderSort"
    >
      <el-table-column type="selection" width="40"> </el-table-column>
      <el-table-column align="center" width="160" fixed label="操作">
        <template slot-scope="scope">
          <el-button-group>
            <el-button
              :type="
                scope.row.backs && scope.row.backs.status == 0
                  ? types[7]
                  : types[4]
              "
              size="small"
              icon="el-icon-refresh"
              @click="onCirculation(scope.row)"
            >
              {{
                scope.row.backs && scope.row.backs.status == 0
                  ? "流转中"
                  : "流转出"
              }}
            </el-button>
            <el-button
              :type="types[scope.row.order_status]"
              size="small"
              icon="el-icon-edit"
              @click="onInfo(scope.row)"
            >
              跟进
            </el-button>
            <el-button
              size="small"
              icon="el-icon-thumb"
              @click="onOneClickRepair(scope.row, true)"
            >
              同步
            </el-button>
            <el-button
              v-if="scope.row.is_direct_mode && scope.row.appointment_status == 1"
              size="small"
              icon="el-icon-thumb"
              @click="confirmOrder(scope.row)"
            >
              确认接单
            </el-button>

            <el-button
              v-if="scope.row.is_direct_mode && scope.row.appointment_status == 2"
              size="small"
              icon="el-icon-thumb"
              @click="confirmOrder(scope.row, 2)"
            >
              协助取消/退款
            </el-button>

            <el-button
              v-if="scope.row.appointment_status == 1 && !scope.row.is_direct_mode"
              size="small"
              icon="el-icon-edit"
              @click="onOneClickYyHandle(scope.row)"
            >
              预约处理
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed label="电话" width="140">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="copyToClipboard(scope.row.mobile)">{{ scope.row.mobile }}</el-button>
            <!-- <el-button 
              type="text" 
              icon="el-icon-phone" 
              @click="callPhone(scope.row.mobile)"
              style="color: #409EFF; margin-left: 8px;"
              title="点击呼叫"
            ></el-button> -->
          </div>
          <span style="display: block; font-size: 8px"
            >{{ scope.row.mobileInfo.area }}-{{
              scope.row.mobileInfo.originalIsp
            }}</span
          >
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        fixed
        label="平台"
        width="80"
        prop="os_name"
      />

      <el-table-column align="center" fixed label="直播" width="60">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.is_zhibo">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        fixed
        label="客服"
        width="80"
        prop="admin.username"
      />

      <el-table-column
        width="300"
        align="center"
        label="跟进备注"
        prop="remark"
      />

      <el-table-column align="center" label="订单号" width="180">
        <template slot-scope="scope">
          <el-button type="text" @click="copyToClipboard(scope.row.sn)">{{ scope.row.sn }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="总金额" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.total_price / 100 }}</span>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        width="80"
        label="人数"
        prop="quantity"
      />
      
      <el-table-column align="center" label="预约状态（抖音）" width="80">
        <template slot-scope="scope">
          <div
            style="padding: 1px 5px; border-radius: 3px"
            :style="{
              color: order_status[scope.row.appointment_status],
              border: `1px solid ${order_status[scope.row.appointment_status]}`,
            }"
            type="primary"
          >
            {{
              scope.row.appointment_status == 1
                ? "已预约(未处理)"
                : scope.row.appointment_status == 2
                ? "已预约(已处理)"
                : "未预约"
            }}
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="状态" width="80" >
        <template slot-scope="scope">
          <div
            style="padding: 1px 5px; border-radius: 3px"
            :style="{
              color: order_status[scope.row.order_status],
              border: `1px solid ${order_status[scope.row.order_status]}`,
            }"
            type="primary"
          >
            {{ scope.row.order_status_name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="跟进状态" width="90">
        <template slot-scope="scope">
          <div
            style="padding: 1px 5px; border-radius: 3px"
            :style="{
              color: follow_status[scope.row.status],
              border: `1px solid ${follow_status[scope.row.status]}`,
            }"
            type="primary"
          >
            {{ scope.row.status_name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="500px"
        label="标题"
        prop="product_name"
      />
      <el-table-column sortable="custom" align="center" label="商品ID" width="90" prop="product_id" />
      <el-table-column sortable="custom" align="center" label="核销日期" width="90" prop="verification_date" />
      <el-table-column width="150" align="center" label="下单时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.create_at | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column width="150" align="center" label="派单时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.give_time | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column width="150" align="center" label="出行日期">
        <template slot-scope="scope">
          <span>{{
            scope.row.travel_date
          }}</span>
        </template>
      </el-table-column>

      
      

      <el-table-column
        align="center"
        label="联系人"
        width="120"
        prop="contact"
      />

      <el-table-column width="150" align="center" label="出行时间">
        <template slot-scope="scope">
          <span>{{ scope.row.travel_date | parseTime("{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>

      <el-table-column width="150" align="center" label="最后跟进时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.last_follow | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="核单" width="80">
        <template slot-scope="scope">
          <i v-if="scope.row.is_check == 1" class="el-icon-check" />
          <i v-if="scope.row.is_check == 2" class="el-icon-close" />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="138"
        label="分类"
        prop="category_desc"
      />

      <el-table-column
        align="center"
        label="主播"
        width="80"
        prop="anchor.username"
      />
      <el-table-column width="138" align="center" label="修改时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.update_time | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="setMode"
    />

    <el-dialog title="订单跟进" :visible.sync="dialogVisible">
      <el-form label-width="130px" :model="item">
        <el-form-item label="产品名称">
          {{ item.product_name }}
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品状态">
              {{ item.order_status_name }}
            </el-form-item>
            <el-form-item label="数量">
              {{ item.quantity }}
            </el-form-item>
            <el-form-item label="联系人">
              {{ item.contact }}
            </el-form-item>
            <el-form-item label="手机">
              {{ item.mobile }}
            </el-form-item>
            <el-form-item label="下单时间">
              {{ item.create_at | parseTime("{y}-{m}-{d} {h}:{i}") }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="人员">
              <el-row>
                <el-col :span="3">大人</el-col>
                <el-col :span="5"
                  ><el-input
                    v-model="item.personnel.adult"
                    name="adult"
                    placeholder="大人"
                /></el-col>
                <el-col :span="3">老人</el-col>
                <el-col :span="5"
                  ><el-input
                    v-model="item.personnel.old"
                    name="old"
                    placeholder="老人"
                /></el-col>
                <el-col :span="3">小孩</el-col>
                <el-col :span="5"
                  ><el-input
                    v-model="item.personnel.child"
                    name="child"
                    placeholder="小孩"
                /></el-col>
              </el-row>
            </el-form-item>

            <el-form-item v-if="item.status !== 1" label="核销码">
              <el-input
                v-model="item.check_sn"
                name="check_sn"
                placeholder="请输入平台核销码"
              />
            </el-form-item>

            <el-form-item label="加微信" v-if="item.status !== 2">
              <el-checkbox
                v-model="item.is_wechat"
                :true-label="1"
                :false-label="0"
                >已加微信</el-checkbox
              >
            </el-form-item>

            <el-form-item required pros="travel_date" label="出游日期">
              <el-date-picker
                v-model="item.travel_date"
                type="date"
                placeholder="选择日期时间"
              />
            </el-form-item>

            <el-form-item v-if="item.status !== 1" label="返回日期">
              <el-date-picker
                v-model="item.travel_end"
                type="date"
                placeholder="选择日期时间"
              />
            </el-form-item>

            <el-form-item
              required
              pros="next_follow"
              label="下次跟进时间"
              v-if="item.status !== 2"
            >
              <el-date-picker
                v-model="next_follow"
                type="datetime"
                placeholder="选择日期时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="跟进状态">
          <template v-for="(v, k) in status_arr">
            <el-radio v-if="k > 0" v-model="item.status" :label="k" border>{{
              v
            }}</el-radio>
          </template>
        </el-form-item>
        <el-form-item required pros="desc" label="跟进说明">
          <el-input v-model="item.desc" type="textarea" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onSave(item)">保 存</el-button>
      </div>

      <el-tabs v-model="active" type="border-card">
        <el-tab-pane name="follow" label="跟进记录">
          <el-table :data="item.follow" style="width: 100%">
            <el-table-column label="日期" width="138">
              <template slot-scope="scope">
                <span>{{
                  scope.row.create_time | parseTime("{y}-{m}-{d} {h}:{i}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="跟进人" width="110" prop="admin.username" />
            <el-table-column label="状态" width="80">
              <template slot-scope="scope">
                <span>{{ status_arr[scope.row.status] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="desc" label="跟进说明" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane name="finance" label="财务记录">
          <el-table :data="item.finance" style="width: 100%">
            <el-table-column label="日期">
              <template slot-scope="scope">
                <span>{{
                  scope.row.create_time | parseTime("{y}-{m}-{d} {h}:{i}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型" width="110">
              <template slot-scope="scope">
                <span>{{ type_arr[scope.row.type] }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.total / 100 }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog :title="confirmTitle" :visible.sync="orderConfirmDialogVisible">
      <el-form label-width="130px" :model="confirmItem">
        <el-form-item label="产品名称">
          {{ confirmItem.product_name }}
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品状态">
              {{ confirmItem.order_status_name }}
            </el-form-item>
            <el-form-item label="数量">
              {{ confirmItem.quantity }}
            </el-form-item>
            <el-form-item label="手机">
              {{ confirmItem.mobile }}
            </el-form-item>
            <el-form-item label="下单时间">
              {{ confirmItem.create_at | parseTime("{y}-{m}-{d} {h}:{i}") }}
            </el-form-item>
            <el-form-item label="支付金额">
              {{ confirmItem.actual_price/100 }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="confirmItem.dyOrderAppointments.number_of_guests" label="人员">
              <el-row>
                <el-col :span="3">大人</el-col>
                <el-col :span="5"
                  ><el-input
                    v-model="confirmItem.dyOrderAppointments.number_of_guests.adult"
                    name="adult"
                    placeholder="大人"
                /></el-col>
                <el-col :span="3">小孩</el-col>
                <el-col :span="5"
                  ><el-input
                    v-model="confirmItem.dyOrderAppointments.number_of_guests.child"
                    name="child"
                    placeholder="小孩"
                /></el-col>
              </el-row>
            </el-form-item>

            <el-form-item label="出游日期">
              {{ confirmItem.dyOrderAppointments.book_info.book_start_date }}
            </el-form-item>

            <el-form-item label="返回日期">
              {{ confirmItem.dyOrderAppointments.book_info.book_end_date }}
            </el-form-item>

          </el-col>
        </el-row>
        <el-form-item label="预约详情">
          <el-table
            v-if="confirmItem.dyOrderAppointments.book_info.occupancies"
            :data="confirmItem.dyOrderAppointments.book_info.occupancies"
            style="width: 100%;margin-bottom: 0;">
            <el-table-column
              prop="name"
              label="出行人"
              width="180">
            </el-table-column>
            <el-table-column
              prop="license_id"
              label="证件号"
              width="180">
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>

      <div v-if="confirmItem.appointment_status == 1" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dyOrderConfirm(confirmItem, 1)">确认接单</el-button>
        <el-button type="primary" @click="dyOrderConfirm(confirmItem, 2)">拒绝</el-button>
      </div>

      <div v-if="confirmItem.appointment_status == 2" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dyOrderCancel(confirmItem, 2)">仅取消预约</el-button>
        <el-button type="primary" @click="dyOrderCancel(confirmItem, 1)">取消预约并全额退款</el-button>
      </div>
    </el-dialog>

    <el-dialog title="纯核销" :visible.sync="dialog2Visible">
      <el-form label-width="160px" :model="form">
        <el-form-item label="平台">
          <el-radio v-model="form.os" label="1">美团</el-radio>
        </el-form-item>
        <el-form-item label="核销码">
          <el-input v-model="form.check_sn" placeholder="请输入平台核销码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onPass(form)">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog title="申请转出订单" :visible.sync="applyVisible">
      <el-form label-width="160px" :model="item3" :rules="rules" ref="ruleForm">
        <el-form-item label="标题:" v-if="!isAll">
          <el-input v-model="item3.product_name" disabled />
        </el-form-item>
        <el-form-item label="订单号:" v-if="!isAll">
          <el-input v-model="item3.sn" disabled />
        </el-form-item>
        <el-form-item label="流转对象:" style="width: 600px" prop="flowObj">
          <el-select
            v-model="item3.flowObj"
            placeholder="请选择"
            @change="onChange2"
          >
            <el-form-item
              style="display: inline-flex; text-align: left; width: 770px"
            >
              <el-option
                v-for="item in adminList"
                :key="item.value"
                style="
                  width: 250px;
                  display: inline-flex;
                  word-break: break-all;
                "
                :label="item.username"
                :value="item.id"
              />
            </el-form-item>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- scope.row.backs&&scope.row.backs.status==2? -->
        <el-button
          v-if="item3.backs && item3.backs.status == 0"
          type="primary"
          @click="onCancel(item3.flowObj)"
          >取 消</el-button
        >
        <el-button
          v-else
          type="primary"
          @click="onCirculationSave(item3.flowObj)"
          >确 认</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import Pagination from '@/Wangeditor/Pagination'
import Pagination from "@/components/PaginationFixed";
import { orderBack, orderbackBatch } from "@/api/order";
import checkPermission from '@/utils/permission';
import Softphone from '7moor-softphone-sdk';
import SoftPhone from '@/components/SoftPhone';
export default {
  name: "Orderlist",
  components: { Pagination, SoftPhone },
  data() {
    return {
      active: "follow",
      types: {
        0: "",
        1: "",
        2: "",
        3: "primary",
        4: "success",
        5: "warning",
        6: "danger",
        7: "info",
      },
      types2: { 1: "primary", 2: "success", 3: "warning" },
      status_arr: ["待跟进", "跟进中", "已核销", "核销失败", "放弃跟单", "加入公海"],
      type_arr: ["-", "收益", "支出"],
      timetype_arr: {},
      order_status: [
        "#9e9f9c",
        "#04bcd9",
        "#fc9904",
        "#1193f4",
        "#48b14b",
        "#eb1662",
        "#9d1cb5",
      ],
      follow_status: [
        "#9e9f9c",
        "#04bcd9",
        "#fc9904",
        "#1193f4",
        "#48b14b",
        "#eb1662",
      ],
      options: [],
      value: null,
      next_follow: null,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        times: [],
        status: null,
        admin: null,
        zhubo: null,
        os_status: [],
        appointment_status: "",
        un_use_type: "",
      },
      item: { next_follow: "", personnel: {} },
      confirmItem: { next_follow: "", personnel: { adult: "" }, dyOrderAppointments:{book_info:{}, number_of_guests:{}} },
      follow: [],

      dialogVisible: false,
      dialog2Visible: false,
      applyVisible: false,
      notice:false,
      orderConfirmDialogVisible:false,
      oss: [],
      isSynchronization: false,
      item3: {
        sn: null,
        backs: null,
        flowObj: "",
        os: null, // 初始值，你可以根据需要设置为 1、2 或 3
      },
      multipleSelection: [],
      sn: [],
      adminList: [],
      form: {},
      isAll: false,
      rules: {
        flowObj: [
          { required: true, message: "请选择流转对象", trigger: "change" },
        ],
      },
      currentSort: {
        prop: null, // 排序字段
        order: null // 排序顺序
      },
      confirmTitle:"确认接单",
      moorSoftphone: null, // 七陌软电话实例
      softphoneVisible: false, // 软电话控件显示状态
      currentPhoneNumber: '', // 当前要拨打的电话号码
      currentLoginType: 'Local' // 当前登录类型
    };
  },
  async created() {
    this.listQuery.zhubo = this.$route.query.zhubo || null;
    if (this.$route.query.start && this.$route.query.end) {
      this.listQuery.times = [this.$route.query.start, this.$route.query.end];
    }
    this.setQuery("status");
    this.setQuery("os_status");
    this.setQuery("times");
    this.setQuery("appointment_status");
    this.getShortcutContent();
    this.getAdminList();
  },
  mounted() {
    this.setMode();
    if (this.$route.query.id) {
      this.onInfo({ id: this.$route.query.id });
    }
  },
  computed: {
    tableMaxHeight() {
      return window.innerHeight - 320 + "px";
    },
  },
  watch: {
    $route(to, from) {
      this.onInfo({ id: this.$route.query.id });
    },
  },
  methods: {
    checkPermission,
    handleSelectionChange(val) {
      this.multipleSelection = val;
      const data = [];
      this.multipleSelection.map((item) => {
        data.push(item.sn);
      });
      this.sn = data;
    },
    setQuery(key) {
      if (this.$route.query.hasOwnProperty(key)) {
        this.listQuery[key] = this.$route.query[key];
      } else {
        this.listQuery[key] = "";
      }
    },
    async setMode() {
      await this.getList();
      // await this.setOneClickRepair();
      // await this.getList();
    },
    async setOneClickRepair() {
      let arr = this.list.map((res) => {
        return res.id;
      });
      await this.onOneClickRepair({ id: arr.join() });
    },
    orderSort({ column, prop, order }) {
      if (column.order == 'ascending') {
        this.listQuery.order_by = 'verify_date_asc'
      }
      if (column.order == 'descending') {
        this.listQuery.order_by = 'verify_date_desc'
      }
      if (this.currentSort.prop !== prop) {
        this.currentSort = { prop, order };
      } else {
        // 如果排序字段没变，则切换排序顺序
        this.currentSort.order = this.currentSort.order === 'ascending' ? 'descending' : 'ascending';
      }
      console.log({ column, prop, order });
      this.getList();
    },
    async getList($is_excel, $refreash = 1) {
      this.listQuery.excel = null;
      if ($is_excel == 1) {
        this.listQuery.excel = 1;
        const isdate = this.listQuery.times[0] instanceof Date;
        const params = {
          ...this.listQuery,
          times: [
            isdate ? this.listQuery.times[0].toISOString() : "",
            isdate ? this.listQuery.times[1].toISOString() : "",
          ],
        };
        window.open("/admin/order/index?" + this.objectToQuery(params));
        return;
      }

      await this.$axios
        .get("/admin/order/index", { params: this.listQuery })
        .then((response) => {
          this.list = response.data.data;
          this.total = response.data.total;
          (this.timetype_arr = response.ext.timetype),
            (this.oss = response.ext.oss);
          this.listLoading = false;
          this.isSynchronization = true;
        });
      if ($refreash == 1) {
        await this.setOneClickRepair();
      }
    },
    objectToQuery(obj) {
      return Object.keys(obj)
        .map((key) => {
          const value = obj[key];
          if (value == undefined || value == null) return "";
          return encodeURIComponent(key) + "=" + encodeURIComponent(value);
        })
        .join("&");
    },
    onInfo(item) {
      this.value = null;
      this.next_follow = null;
      this.$set(item, "next_follow", null);
      // this.item = item;
      this.active = "follow";
      this.$axios
        .get("/admin/order/info", { params: { id: item.id } })
        .then((res) => {
          this.item = res.data;
          this.dialogVisible = true;
        })
        .catch((err) => {});
    },
    confirmOrder(item, type=1) {
      if (type == 1) {
        this.confirmTitle = '确认接单';
      }
      if (type == 2) {
        this.confirmTitle = '协助取消/退款';
      }
      this.$axios
        .get("/admin/order/info", { params: { id: item.id } })
        .then((res) => {
          this.confirmItem = res.data;
          this.orderConfirmDialogVisible = true;
        })
        .catch((err) => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    getAdminList(typeDesc = "") {
      this.$axios
        .get("/admin/admin/index", {
          params: { limit: 100, status: 1, is_order: 1, type_desc: typeDesc },
        })
        .then((response) => {
          this.adminList = response.data.data;
          this.listLoading = false;
        })
        .catch((err) => {});
    },
    onCirculation(item) {
      this.getAdminList(item.category_desc);
      this.applyVisible = true;
      this.isAll = false;
      this.item3 = { ...item, os: Number(item.os) };
      console.log(this.item3);
      if (this.item3.backs && this.item3.backs.admin_id) {
        this.item3.flowObj = this.item3.backs.admin_id;
      } else {
        this.resetForm("ruleForm");
      }
    },
    // 批量设置
    onCirculationAll() {
      this.applyVisible = true;
      this.isAll = true;
    },
    //确定
    onCirculationSave(to_admin_id) {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          let res;
          if (this.isAll) {
            this.$confirm("是否批量流转订单", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(async () => {
              res = await orderbackBatch({
                sn: this.sn,
                to_admin_id: to_admin_id,
              });
              if (res.data) {
                this.$message({
                  message: "批量流转订单成功",
                  type: "success",
                });
                this.applyVisible = false;
                this.isAll = false;
                this.getList();
              }
            });
          } else {
            res = await orderBack({
              sn: this.item3.sn,
              os: this.item3.os,
              to_admin_id: to_admin_id,
            });
            if (res.data) {
              this.$message({
                message: "流转订单成功",
                type: "success",
              });
              this.applyVisible = false;
              this.isAll = false;
              this.getList();
            }
          }
        } else {
          return false;
        }
      });
    },
    // 取消
    onCancel() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$axios
            .post("/admin/order/backcancel", { id: this.item3.id })
            .then((res) => {
              this.applyVisible = false;
              this.isAll = false;
              this.getList();
            })
            .catch((err) => {
              console.log(err);
            });
        } else {
          return false;
        }
      });
    },
    onBack() {
      this.$axios
        .post("/admin/order/back", this.item)
        .then((res) => {
          this.dialogVisible = false;
          this.item = {};
          this.getList();
        })
        .catch((err) => {});
    },
    onSave(item) {
      this.$axios
        .post("/admin/order/save", {
          id: item.id,
          check_sn: item.check_sn,
          is_wechat: item.is_wechat,
          travel_end: item.travel_end,
          travel_date: item.travel_date,
          desc: item.desc,
          status: item.status,
          next_follow: this.next_follow,
          personnel: this.item.personnel,
        })
        .then((res) => {
          this.dialogVisible = false;
          this.item = { next_follow: "", personnel: { adult: "" } };
          this.$router.push({
            path: "/order/index",
          });
        })
        .catch((err) => {});
    },
    onPass(form) {
      this.$axios
        .post("/admin/order/pass", { check_sn: form.check_sn })
        .then((res) => {
          this.dialog2Visible = false;
          this.form = {};
        })
        .catch((err) => {});
    },
    onChange(from) {
      this.$set(
        this.item,
        "desc",
        from + (this.item.desc != undefined ? this.item.desc : "")
      );
    },
    onChange2(from) {
      this.$set(
        this.item,
        "to_admin_id",
        from + (this.item.admin_id != undefined ? this.item.admin_id : "")
      );
    },
    handleChange(os) {
      console.log(os);
    },
    getShortcutContent() {
      this.listLoading = true;
      this.$axios
        .get("/admin/shortcutContent/list", {
          params: { page: 1, limit: 50, status: 1 },
        })
        .then((response) => {
          for (const r of response.data.data) {
            this.options.push({ value: r.id, label: r.content });
          }
        })
        .catch(() => {});
    },
    onOneClickRepair(item, notice = false) {
      this.notice = notice;
      this.$axios
        .post("/admin/order/oneClickRepair", { id: item.id })
        .then((res) => {
          if (this.notice) {
            this.$notify({
              title: "成功",
              message: "同步完成",
              type: "success",
            });
          }
          this.getList(0, 0);
        })
        .catch((err) => {
          // this.$notify.error({
          //   title: "同步失败",
          //   message: err,
          // });
        });
    },
    dyOrderCancel(item, cancel_type) {
      this.$axios
        .post("/admin/order/dyOrderCancel", { id: item.id, cancel_type:cancel_type })
        .then((res) => {
          this.$notify({
            title: "成功",
            message: "短信已发送给客人",
            type: "success",
          });
          this.orderConfirmDialogVisible = false;
          this.getList();
        })
        .catch((err) => {
          this.$notify.error({
            title: "发送失败",
            message: err,
          });
        });
    },
    dyOrderConfirm(item, confirm_result) {
      this.$axios
        .post("/admin/order/dyOrderConfirm", { id: item.id, confirm_result:confirm_result })
        .then((res) => {
          this.$notify({
            title: "成功",
            message: "操作成功",
            type: "success",
          });
          this.orderConfirmDialogVisible = false;
          this.getList();
        })
        .catch((err) => {
          this.$notify.error({
            title: "接单失败",
            message: err,
          });
        });
    },
    onOneClickYyHandle(item) {
      this.$axios
        .post("/admin/order/changeAppointmentStatus", { id: item.id })
        .then((res) => {
          this.dialogVisible = false;
          this.$notify({
            title: "成功",
            message: "已处理",
            type: "success",
          });
          // this.getList();
        })
        .catch((err) => {
          this.$notify.error({
            title: "错误",
            message: err,
          });
        });
    },
    stripHtml(html) {
      const div = document.createElement("div");
      div.innerHTML = html;
      return div.textContent || div.innerText || "";
    },
    copyToClipboard(html) {
      const text = this.stripHtml(html);
      const input = document.createElement("textarea");
      input.value = text;
      document.body.appendChild(input);
      input.select();
      document.execCommand("copy");
      document.body.removeChild(input);
      this.$message({
        showClose: true,
        message: "内容已复制",
        type: "success",
      });
    },
    async callPhone(phoneNumber) {
      if (!phoneNumber) {
        this.$message.error('电话号码不能为空');
        return;
      }
      
      if (phoneNumber.length !== 11) {
        this.$message.error('请输入正确的手机号码');
        return;
      }
      
      try {
        // 设置当前电话号码
        this.currentPhoneNumber = phoneNumber;
        
        // 显示软电话控件（不自动拨号，由客服手动确认）
        this.softphoneVisible = true;
        
        // 如果软电话控件已经显示且已初始化，设置号码到拨号区域但不拨号
        if (this.moorSoftphone) {
          // 只设置号码，不自动拨号
          return;
        }
        
        // 获取空闲坐席配置
        const agentResponse = await this.$axios.get('/admin/qimo/getAvailableAgent');
        
        if (!agentResponse.data) {
          this.$message.error('暂无空闲坐席，请稍后再试');
          return;
        }
        
        const agentConfig = agentResponse.data;
        
        // 使用NPM SDK方式创建Softphone实例
        console.log('创建Softphone实例...');
        
        // 先销毁之前的实例
        if (this.moorSoftphone) {
          console.log('销毁之前的Softphone实例');
          // this.moorSoftphone.destroy && this.moorSoftphone.destroy();
        }
        
        const vm = new Softphone({
          accountId: 'T00000032238',
          agentNumber: '8000@xglgj',
          password: 'PZy12jxT8000',
          loginType: 'Local',
          proxy_url: 'https://sh-hw-cc-v4.7moor.com',
          success: () => {
            console.log('Softphone初始化成功！');
            this.moorSoftphone = vm; // 保存实例到组件
            // 只绑定事件，不自动拨号
            this.initSoftphoneEvents(vm, null);
          },
          error: (error) => {
            console.log('Softphone初始化失败:', error);
            this.$message.error('软电话初始化失败');
          }
        });
        
        console.log('Softphone实例创建完成，等待初始化...');
        
      } catch (error) {
        console.error('呼叫失败:', error);
        this.$message.error(error.message || '呼叫失败');
      }
    },
    
    // 初始化软电话事件监听
    async initSoftphoneEvents(vm, phoneNumber) {
      try {
        // 绑定通话事件
        vm.attachEvent({
          success: () => {
            console.log('事件绑定成功');
          },
          message: (res) => {
            console.log('通话事件:', res.event);
            // 处理各种通话事件
            if (res.event) {
              switch (res.event.type) {
                case 'peerstate':
                  console.log('座席状态变化:', res.event.typeValue);
                  break;
                case 'dialing':
                  console.log('呼叫中...');
                  this.$message.info('正在拨号...');
                  break;
                case 'dialTalking':
                  console.log('外呼通话中');
                  this.$message.success('通话已接通');
                  break;
                case 'innerTalking':
                  console.log('呼入通话中');
                  break;
                default:
                  console.log('其他事件:', res.event.type);
              }
            }
          },
          error: (error) => {
            console.log('事件绑定异常:', error);
          }
        });
        
        // 等待一下确保事件绑定完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 如果有电话号码，自动发起呼叫
        if (phoneNumber) {
          // 记录呼叫
          await this.$axios.post('/admin/qimo/makeCall', {
            callee_number: phoneNumber,
            agent_id: 1 // 使用固定agent_id
          });
          
          // 发起外呼
          console.log('发起外呼:', phoneNumber);
          vm.callApi.dialout({
            calleeNumber: phoneNumber,
            success: (result) => {
              console.log('外呼成功:', result);
              this.$message.success('呼叫发起成功');
            },
            fail: (error) => {
              console.log('外呼失败:', error);
              this.$message.error(`呼叫失败: ${error.message || '未知错误'}`);
            }
          });
        }
        
      } catch (error) {
        console.error('初始化事件失败:', error);
        this.$message.error('软电话事件初始化失败');
      }
    },
    loadScript(src) {
      return new Promise((resolve, reject) => {
        if (window.moorSimpleSoftphone) {
          resolve();
          return;
        }
        
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    },
    tryAlternativeCall(phoneNumber) {
      console.log('尝试备用呼叫方法');
      
      const sdk = window.moorSimpleSoftphone;
      
      // 方式2：尝试call方法
      if (typeof sdk.call === 'function') {
        console.log('尝试call方法');
        try {
          const result = sdk.call(phoneNumber);
          console.log('call方法结果:', result);
          if (result !== false && result !== null) {
            this.$message.success('呼叫发起成功(call方法)');
            return;
          }
        } catch (error) {
          console.log('call方法失败:', error);
        }
      }
      
      // 所有方法都失败
      this.$message.error('呼叫功能暂不可用，请联系管理员');
      console.log('所有呼叫方法都失败，SDK状态:', {
        initState: sdk.initState,
        isInit: sdk.isInit,
        isInitApp: sdk.isInitApp,
        availableMethods: Object.keys(sdk).filter(key => typeof sdk[key] === 'function')
      });
    },
    
    // 登录类型变化处理
    onLoginTypeChange(newType) {
      console.log('登录类型变化为:', newType);
      this.currentLoginType = newType; // 同步到父组件的状态
    },
    
    // 重新初始化软电话
    async onReinitSoftphone(newLoginType) {
      try {
        console.log('重新初始化软电话，登录类型:', newLoginType);
        
        // 销毁现有实例
        if (this.moorSoftphone) {
          try {
            if (this.moorSoftphone.destroy && typeof this.moorSoftphone.destroy === 'function') {
              this.moorSoftphone.destroy();
            }
          } catch (e) {
            console.log('销毁实例时出错:', e);
          }
          this.moorSoftphone = null;
        }
        
        // 短暂等待，确保销毁完成
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 获取坐席配置
        const agentResponse = await this.$axios.get('/admin/qimo/getAvailableAgent');
        if (!agentResponse.data) {
          this.$message.error('暂无空闲坐席，请稍后再试');
          return;
        }
        
        // 创建新的实例
        console.log('创建新的Softphone实例，loginType:', newLoginType);
        const vm = new Softphone({
          accountId: 'T00000032238',
          agentNumber: '8000@xglgj',
          password: 'PZy12jxT8000',
          loginType: newLoginType,
          proxy_url: 'https://sh-hw-cc-v4.7moor.com',
          success: () => {
            console.log('重新初始化成功！loginType:', newLoginType);
            this.moorSoftphone = vm;
            this.$message.success(`已切换到${newLoginType === 'Local' ? '手机号' : newLoginType === 'sip' ? 'SIP话机' : 'WebRTC'}模式`);
            
            // 更新软电话控件状态为空闲
            this.$nextTick(() => {
              this.$refs.softphone?.updateStatus('空闲');
            });
            
            // 绑定事件（不自动拨号）
            this.initSoftphoneEvents(vm, null);
          },
          error: (error) => {
            console.log('重新初始化失败:', error);
            this.$message.error(`切换登录方式失败: ${error.message || '未知错误'}`);
          }
        });
        
        console.log('Softphone实例创建调用完成，等待初始化回调...');
        
      } catch (error) {
        console.error('重新初始化异常:', error);
        this.$message.error('切换登录方式异常: ' + error.message);
      }
    },
  },
};
  </script>
  
  <style scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 5px; /* 分页条的高度，以避免内容重叠 */
}

/* 紧凑型表格样式 */
::v-deep .el-table {
  font-size: 15px;
  color: #333;
}

::v-deep .el-table thead {
  font-weight: 500;
  color: #2c3e50;
}

::v-deep .el-table td {
  padding: 4px 0;
}

 ::v-deep .el-table th {
   padding: 4px 4px;
 }
 ::v-deep .el-table td {
   padding: 4px 4px;
 }
::v-deep .el-table .cell {
  line-height: 1.4;
  padding-left: 4px;
  padding-right: 4px;
}

::v-deep .el-button--mini, ::v-deep .el-button--small {
  padding: 6px 9px;
  font-size: 12.5px;
}

::v-deep .el-tag {
  padding: 0 6px;
  height: 22px;
  line-height: 22px;
}

::v-deep .el-button-group .el-button--small {
  padding: 6px 9px;
}

/* 改善阅读体验 */
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #f8f9fa;
}

::v-deep .el-table__row:hover > td {
  background-color: #ecf5ff !important;
}

</style>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 5px; /* 分页条的高度，以避免内容重叠 */
}

/* 紧凑型表格样式 */
::v-deep .el-table {
  font-size: 15px;
  color: #333;
}

::v-deep .el-table thead {
  font-weight: 500;
  color: #2c3e50;
}

::v-deep .el-table td {
  padding: 4px 0;
}

 ::v-deep .el-table th {
   padding: 4px 4px;
 }
 ::v-deep .el-table td {
   padding: 4px 4px;
 }
::v-deep .el-table .cell {
  line-height: 1.4;
  padding-left: 4px;
  padding-right: 4px;
}

::v-deep .el-button--mini, ::v-deep .el-button--small {
  padding: 6px 9px;
  font-size: 12.5px;
}

::v-deep .el-tag {
  padding: 0 6px;
  height: 22px;
  line-height: 22px;
}

::v-deep .el-button-group .el-button--small {
  padding: 6px 9px;
}

/* 改善阅读体验 */
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #f8f9fa;
}

::v-deep .el-table__row:hover > td {
  background-color: #ecf5ff !important;
}

</style>
