<?php

namespace app\command;

use app\common\Error;
use app\model\Admins;
use app\model\Follows;
use app\model\Orders as OrdersModel;
use app\model\Products;
use app\server\Orders;
use app\server\Orders as ServerOrders;
use support\Log;
use support\Redis;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Output\OutputInterface;


class AutoFishPubOrder extends Command
{
    protected static $defaultName = 'auto_fish_pub_order';
    protected static $defaultDescription = '自动分配公海订单。';

    /**
     * @return void
     */
    protected function configure()
    {
        // $this->addArgument('name', InputArgument::OPTIONAL, 'Name description');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output($output, self::$defaultName . " started, " . date('Y-m-d H:i:s'));

        $orders = $this->orders();
        $total = count($orders);

        $this->output($output, "got {$total} orders");

        if (1 > $total) {
            return self::SUCCESS;
        }

        foreach($orders as $order) {
            $this->giveOrder($order);
        }

        return self::SUCCESS;
    }

    private function output(OutputInterface $output, string $message)
    {
        $output->writeln(sprintf('{"time":"%s", "message":"%s"}', date('Y-m-d H:i:s'), $message));
    }

    /**
     * 重新分配订单
     * @param $order
     * @throws \think\db\exception\BindParamException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function giveOrder($order) {
        try {
            $follow = Follows::query()->where('order_id', $order->id)->where('status', 5)->order('id', 'desc')->find();
            $product = Products::query()->where('third_product_id', $order->product_id)->find();
            $adminId = 0;
            $allocateMethod = '';
            $extraData = [
                'mobile' => $order->mobile,
                'product_id' => $order->product_id,
                'source' => '公海订单重新分配',
                'previous_admin_id' => $follow->admin_id ?? 0
            ];
            
            if (!empty($product)) {
                $adminId = Admins::where('status', 1)->where('id', '!=', $follow->admin_id)->whereFindInSet('product_ids', $product->id)->value('id');
                $allocateMethod = '公海重新分配';
            }
            
            if (!$adminId) {
                $allocateMethod = '分配失败';
                throw new \Exception('没有可分配用户');
            }
            
            // 获取门店ID
            $shopId = Admins::where('id', $adminId)->value('shop_id');
            
            $order->admin_id = $adminId;
            $order->is_public_pool = 0;
            $order->save();
            
            // 记录分配日志
            if ($order->sn) {
                \app\server\Orders::recordAllocateLog(
                    $order->sn,
                    $adminId,
                    $shopId,
                    $allocateMethod,
                    $extraData
                );
            }
        } catch (\Exception $e) {
            Log::info(sprintf('重新分配订单失败：%s, 失败原因：%s', json_encode($order), $e->getMessage().$e->getFile().$e->getLine())  );
        }
    }

    /**
     * @return OrdersModel[]
     */
    private function orders(): array
    {
        $startDate = '2025-02-01 00:00:00';
        $endDate = date('Y-m-d H:i:s', strtotime('-7 days'));
        $list = OrdersModel::where('is_public_pool', '=', 1)
            ->where('admin_id', 0)
            ->whereBetweenTime('last_pool_date', $startDate, $endDate)
            ->select()->all();
        return $list;
    }
}
