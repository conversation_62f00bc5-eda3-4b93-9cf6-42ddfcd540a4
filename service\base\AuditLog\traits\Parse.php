<?php

namespace base\AuditLog\traits;

use base\base\DS;
use think\helper\Str;
use base\AuditLog\AirField;

trait Parse
{

    private array $instanceQuery = [];

    /**
     * 解析 message 的参数
     *
     * @param $message
     *
     * @return string
     */
    private function parse($message): string
    {
        $data = new DS($this->context);

        preg_match_all("~#\[.[a-zA-Z0-9_.]{1,100}.]~", $message, $response);
        if ( ! empty($response[0])) {
            foreach ($response[0] as $str) {
                $key       = mb_substr($str, 2, -1, 'UTF-8');
                $value_default = $this->config->get('value.default', '');
                $str_value =  "%" . $data->get($key, $value_default) . "%";
                $message   = preg_replace("~#\[$key]~", $str_value, $message);
            }
        }

        return $message;
    }

    /**
     * 解析 desc 的参数
     *
     * @param string $mode
     * @param $data
     *
     * @return string
     */
    private function infoParse(string $mode, $data): string
    {
        $message = $this->config->get('desc.' . $mode);
        $data    = ds($data);
        preg_match_all("~#\[.[a-zA-Z0-9_.]{1,100}.]~", $message, $response);
        $value_default = $this->config->get('value.default','');
        if ( ! empty($response[0])) {
            foreach ($response[0] as $str) {
                $key       = mb_substr($str, 2, -1, 'UTF-8');
                $str_value =   "%" . $data->get($key, $value_default) . "%";
                $message   = preg_replace("~#\[$key]~", $str_value, $message);
            }
        }

        return $message;
    }

    /**
     * 解析值
     *
     * @access protected
     *
     * @param string $field 字段|字段别名
     * @param int|string $value 待转换的值
     *
     * @return string|int
     */
    private function getParse(string $field, int|string $value,)
    {
        $fun = 'parse' . $field;

        return method_exists($this, $fun) ? $this->$fun($value, ds($this->context)) : $value;
    }

    /**
     * 解析值
     *
     * @access protected
     *
     * @param string $field 字段名
     *
     * @return AirField
     */
    private function getListParse(string $field): AirField
    {
        if ( ! isset($instance[$field])) {
            $fun              = 'getField' . Str::studly($field);
            $instance[$field] = method_exists($this, $fun) ? $this->$fun(new AirField()) : new AirField();
            $instance[$field]->initField();
        }

        return $instance[$field];
    }

}