<?php

namespace app\command;

use app\model\Orders;
use app\model\Orders as OrdersModel;
use app\server\DyApiService;
use support\Log;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;


class SyncDyOrderStatus extends Command
{
    protected static $defaultName = 'sync_dy_order';
    protected static $defaultDescription = '同步抖音订单状态。';

    /**
     * @return void
     */
    protected function configure()
    {
        // $this->addArgument('name', InputArgument::OPTIONAL, 'Name description');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output($output, self::$defaultName . " started, " . date('Y-m-d H:i:s'));

        $orders = $this->orders();
        $total = count($orders);

        $this->output($output, "got {$total} orders");

        if (1 > $total) {
            return self::SUCCESS;
        }

        $this->sync($orders, $output);

        return self::SUCCESS;
    }

    private function output(OutputInterface $output, string $message)
    {
        $output->writeln(sprintf('{"time":"%s", "message":"%s"}', date('Y-m-d H:i:s'), $message));
        Log::info($message);
    }

    /**
     * 同步订单状态
     * @param $orders
     * @throws \think\db\exception\BindParamException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function sync($orders, $output) {
        try {
            $dyApiService = new DyApiService();
            foreach ($orders as $order) {
                $dyOrder = $dyApiService->send(DyApiService::ORDER_QUERY, [
                    'page_num' => 1,
                    'page_size'=> 1,
                    'order_id' => $order->sn,
                    'account_id' => env('DY_ACCOUNT_ID')
                ]);

                if (isset($dyOrder['data']['orders']) && !empty($dyOrder['data']['orders'])) {
                    $detail = $dyOrder['data']['orders'][0];
                    $status = $detail['order_status'];
                    /**
                     * 订单状态
                    （OrderStatus_Init 初始化 OrderStatus = 0
                    OrderStatus_WaitPay 待支付 OrderStatus = 100
                    OrderStatus_Cancel 订单关闭 OrderStatus = 101
                    OrderStatus_PaySuccess 支付成功 OrderStatus = 200
                    OrderStatus_Available 待使用 OrderStatus = 201
                    OrderStatus_Finish 交易成功 OrderStatus = 1
                    OrderStatus_PartPay 部分支付  OrderStatus = 150）
                     */
                    if ($status == 1) {
                        $order->order_status = 2;
                        $order->save();
                        $this->output($output,$order->sn . ' 更新成功， 订单已核销'. PHP_EOL);
                    }
                    elseif ($status == 101) {
                        $order->order_status = 4;
                        $order->is_refunded = 1;
                        $order->save();
                        $this->output($output,$order->sn . ' 更新成功， 订单已退款' . PHP_EOL);
                    } else {
                        $this->output($output,$order->sn . ' 不需要更新 ' . PHP_EOL);
                    }
                } else {
                    $this->output($output,$order->sn . ' 订单信息查询失败' . PHP_EOL);
                }
                sleep(1);
            }
        } catch (\Exception $e) {
            $this->output($output, sprintf('同步订单状态异常：%s, 失败原因：%s', json_encode($order), $e->getMessage().$e->getFile().$e->getLine())  );
        }
    }

    /**
     * @return OrdersModel[]|array|\think\Collection|\think\model\Collection
     */
    private function orders() {
        $orders = Orders::where('os', 5)
            ->where('is_refunded', 0)
            ->where('order_status', '!=', 2)
            ->field(['id', 'sn'])
            ->select();

        return $orders;
    }
}
