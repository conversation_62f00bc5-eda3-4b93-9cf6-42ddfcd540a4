<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div slot="header" class="clearfix">
            <span>今日分配总数</span>
          </div>
          <div class="stats-number">{{ statistics.total_count || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div slot="header" class="clearfix">
            <span>分配客服数</span>
          </div>
          <div class="stats-number">{{ (statistics.admin_stats && statistics.admin_stats.length) || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div slot="header" class="clearfix">
            <span>涉及门店数</span>
          </div>
          <div class="stats-number">{{ (statistics.shop_stats && statistics.shop_stats.length) || 0 }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div slot="header" class="clearfix">
            <span>平均每小时</span>
          </div>
          <div class="stats-number">{{ avgPerHour }}</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <el-card class="filter-container">
      <el-form :model="listQuery" ref="filterForm" :inline="true" class="demo-form-inline">
        <el-form-item label="订单号">
          <el-input
            v-model="listQuery.order_sn"
            placeholder="请输入订单号"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="客服">
          <el-select
            v-model="listQuery.admin_id"
            placeholder="请选择客服"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="admin in adminOptions"
              :key="admin.id"
              :label="admin.username"
              :value="admin.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="门店">
          <el-select
            v-model="listQuery.shop_id"
            placeholder="请选择门店"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="shop in shopOptions"
              :key="shop.id"
              :label="shop.name"
              :value="shop.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="分配时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleFilter">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" @click="resetFilter">
            重置
          </el-button>
          <el-button type="success" icon="el-icon-download" @click="handleExport">
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card>
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中"
        border
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column align="center" label="ID" width="80">
          <template slot-scope="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>

        <el-table-column label="订单号" width="180" align="center">
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.order_sn }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="客服信息" width="150" align="center">
          <template slot-scope="scope">
            <div>
              <div>{{ (scope.row.admin && scope.row.admin.name) || '--' }}</div>
              <div class="text-muted">{{ (scope.row.admin && scope.row.admin.username) || '--' }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="门店" width="120" align="center">
          <template slot-scope="scope">
            {{ (scope.row.shop && scope.row.shop.name) || '--' }}
          </template>
        </el-table-column>

        <el-table-column label="分配时间" width="160" align="center">
          <template slot-scope="scope">
            {{ parseTime(scope.row.allocate_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>

        <el-table-column label="分配方式" width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="getMethodTagType((scope.row.extra_data && scope.row.extra_data.allocate_method))">
              {{ (scope.row.extra_data && scope.row.extra_data.allocate_method) || '未知' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="手机号" width="120" align="center">
          <template slot-scope="scope">
            {{ (scope.row.extra_data && scope.row.extra_data.mobile) || '--' }}
          </template>
        </el-table-column>

        <el-table-column label="线路权限" min-width="200">
          <template slot-scope="scope">
            <route-permission-display
              :route-permission="scope.row.route_permission"
              :product-cache="productCache"
              @view-more="handleViewMoreRoute"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" width="100" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="handleDetail(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog title="分配记录详情" :visible.sync="detailDialogVisible" width="60%">
      <div v-if="currentRecord">
        <el-card class="detail-card">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">记录ID：</span>
                <span class="detail-value">{{ currentRecord.id }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">订单号：</span>
                <span class="detail-value">{{ currentRecord.order_sn }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">客服姓名：</span>
                <span class="detail-value">{{ (currentRecord.admin && currentRecord.admin.name) || '--' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">客服用户名：</span>
                <span class="detail-value">{{ (currentRecord.admin && currentRecord.admin.username) || '--' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">门店名称：</span>
                <span class="detail-value">{{ (currentRecord.shop && currentRecord.shop.name) || '--' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">分配时间：</span>
                <span class="detail-value">
                  {{ parseTime(currentRecord.allocate_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}
                </span>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div class="detail-item">
                <span class="detail-label">线路权限：</span>
                <span class="detail-value">{{ currentRecord.route_permission || '--' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="detail-card" style="margin-top: 15px">
          <div slot="header" class="clearfix">
            <span>额外信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">分配方式：</span>
                <span class="detail-value">{{ (currentRecord.extra_data && currentRecord.extra_data.allocate_method) || '--' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">手机号：</span>
                <span class="detail-value">{{ (currentRecord.extra_data && currentRecord.extra_data.mobile) || '--' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">商品ID：</span>
                <span class="detail-value">{{ (currentRecord.extra_data && currentRecord.extra_data.product_id) || '--' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">商品名称：</span>
                <span class="detail-value">{{ (currentRecord.extra_data && currentRecord.extra_data.product_name) || '--' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">订单类型：</span>
                <span class="detail-value">{{ (currentRecord.extra_data && currentRecord.extra_data.order_type) || 'normal' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">数据来源：</span>
                <span class="detail-value">{{ (currentRecord.extra_data && currentRecord.extra_data.source) || '--' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <div v-if="currentRecord.extra_data && currentRecord.extra_data.old_order_sn" style="margin-top: 15px">
          <el-alert
            title="历史分配信息"
            :description="`基于历史订单 ${(currentRecord.extra_data && currentRecord.extra_data.old_order_sn)} 进行分配`"
            type="info"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 线路权限详情对话框 -->
    <el-dialog title="线路权限详情" :visible.sync="routePermissionDialog" width="60%">
      <div v-if="currentRouteProducts.length > 0">
        <div class="route-products-grid">
          <el-card
            v-for="product in currentRouteProducts"
            :key="product.id"
            class="product-card"
            shadow="hover"
          >
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-id">ID: {{ product.id }}</div>
              <div v-if="product.third_product_id" class="product-third-id">
                第三方ID: {{ product.third_product_id }}
              </div>
            </div>
          </el-card>
        </div>
      </div>
      <div v-else class="no-products">
        <el-empty description="暂无线路权限" />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="routePermissionDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchOrderAllocateList, fetchOrderAllocateStatistics, fetchOrderAllocateDetail, fetchProductsByIds } from '@/api/order-allocate'
import { fetchAdminList } from '@/api/admin'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'OrderAllocateLog',
  components: {
    Pagination,
    RoutePermissionDisplay: {
      props: {
        routePermission: {
          type: String,
          default: ''
        },
        productCache: {
          type: Object,
          default: () => ({})
        }
      },
      data() {
        return {
          products: [],
          loading: false
        }
      },
      computed: {
        displayProducts() {
          return this.products.slice(0, 3)
        },
        hasMore() {
          return this.products.length > 3
        },
        displayText() {
          if (this.products.length === 0) {
            return '--'
          }
          const names = this.displayProducts.map(p => p.name)
          return names.join('，') + (this.hasMore ? '...' : '')
        }
      },
             watch: {
         routePermission: {
           immediate: true,
           handler(newVal) {
             this.loadProducts(newVal)
           }
         },
         productCache: {
           deep: true,
           handler() {
             this.loadProducts(this.routePermission)
           }
         }
       },
      methods: {
        parseRoutePermission(routePermission) {
          if (!routePermission || routePermission === '--') {
            return []
          }
          const ids = routePermission.split(',').filter(id => id.trim())
          return ids.map(id => parseInt(id.trim())).filter(id => !isNaN(id))
        },

        loadProducts(routePermission) {
          const productIds = this.parseRoutePermission(routePermission)
          if (productIds.length === 0) {
            this.products = []
            return
          }

          this.products = productIds.map(id =>
            this.productCache[id] || { id, name: `产品${id}` }
          )
        },

        handleViewMore() {
          this.$emit('view-more', this.routePermission)
        }
      },
             render(h) {
         if (this.products.length === 0) {
           return h('span', '--')
         }

         const tags = this.displayProducts.map(product =>
           h('el-tag', {
             props: { size: 'small' },
             class: 'route-tag',
             key: product.id
           }, product.name)
         )

         if (this.hasMore) {
           tags.push(
             h('el-button', {
               props: { type: 'text', size: 'mini' },
               class: 'view-more-btn',
               on: { click: this.handleViewMore }
             }, `查看更多(${this.products.length})`)
           )
         }

         return h('div', { class: 'route-permission-display' }, tags)
       }
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      statistics: {},
      adminOptions: [],
      shopOptions: [],
      dateRange: [],
      listQuery: {
        page: 1,
        limit: 20,
        order_sn: '',
        admin_id: '',
        shop_id: '',
        start_time: '',
        end_time: ''
      },
      detailDialogVisible: false,
      currentRecord: null,
      productCache: {}, // 产品信息缓存
      routePermissionDialog: false, // 查看更多对话框
      currentRouteProducts: [] // 当前查看的线路产品
    }
  },
  computed: {
    avgPerHour() {
      if (!this.statistics.hourly_stats || this.statistics.hourly_stats.length === 0) {
        return 0
      }
      const total = this.statistics.hourly_stats.reduce((sum, item) => sum + item.count, 0)
      return Math.round(total / 24)
    }
  },
  created() {
    this.getList()
    this.getStatistics()
    this.getAdminOptions()
  },
  methods: {
    parseTime,

    // 解析线路权限
    parseRoutePermission(routePermission) {
      if (!routePermission || routePermission === '--') {
        return []
      }

      const ids = routePermission.split(',').filter(id => id.trim())
      return ids.map(id => parseInt(id.trim())).filter(id => !isNaN(id))
    },

    // 获取产品名称
    async getProductNames(productIds) {
      if (!productIds || productIds.length === 0) {
        return []
      }

      const uncachedIds = productIds.filter(id => !this.productCache[id])

      if (uncachedIds.length > 0) {
        try {
          const response = await fetchProductsByIds(uncachedIds.join(','))
          Object.assign(this.productCache, response.data || {})
        } catch (error) {
          console.error('获取产品信息失败:', error)
        }
      }

      return productIds.map(id => this.productCache[id] || { id, name: `产品${id}` })
    },

    // 格式化路线权限显示
    async formatRoutePermission(routePermission, showAll = false) {
      const productIds = this.parseRoutePermission(routePermission)
      if (productIds.length === 0) {
        return { text: '--', products: [], hasMore: false }
      }

      const products = await this.getProductNames(productIds)
      const displayProducts = showAll ? products : products.slice(0, 3)
      const hasMore = products.length > 3

      const text = displayProducts.map(p => p.name).join('，') + (hasMore && !showAll ? '...' : '')

      return { text, products, hasMore, displayProducts }
    },

    // 查看更多线路权限
    async handleViewMoreRoute(routePermission) {
      const result = await this.formatRoutePermission(routePermission, true)
      this.currentRouteProducts = result.products
      this.routePermissionDialog = true
    },

    getList() {
      this.listLoading = true

      // 处理日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        this.listQuery.start_time = this.dateRange[0]
        this.listQuery.end_time = this.dateRange[1]
      } else {
        this.listQuery.start_time = ''
        this.listQuery.end_time = ''
      }

      fetchOrderAllocateList(this.listQuery).then(async response => {
        this.list = response.data.data
        this.total = response.data.total

        // 提取门店选项
        const shops = response.data.data.map(item => item.shop).filter(shop => shop)
        this.shopOptions = [...new Map(shops.map(shop => [shop.id, shop])).values()]

        // 预加载产品信息
        await this.preloadProductInfo(this.list)

        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },

    // 预加载产品信息
    async preloadProductInfo(list) {
      const allProductIds = new Set()

      list.forEach(item => {
        const productIds = this.parseRoutePermission(item.route_permission)
        productIds.forEach(id => allProductIds.add(id))
      })

      if (allProductIds.size > 0) {
        try {
          const response = await fetchProductsByIds(Array.from(allProductIds).join(','))
          Object.assign(this.productCache, response.data || {})
        } catch (error) {
          console.error('预加载产品信息失败:', error)
        }
      }
    },

    getStatistics() {
      const params = {
        start_time: (this.dateRange && this.dateRange[0]) || new Date().toISOString().split('T')[0],
        end_time: (this.dateRange && this.dateRange[1]) || new Date().toISOString().split('T')[0]
      }

      fetchOrderAllocateStatistics(params).then(response => {
        this.statistics = response.data || {}
      })
    },

    getAdminOptions() {
      fetchAdminList({ limit: 1000 }).then(response => {
        // 根据后端返回的数据结构调整
        if (response.data && response.data.data) {
          this.adminOptions = response.data.data || []
        } else if (response.data && Array.isArray(response.data)) {
          this.adminOptions = response.data
        } else {
          this.adminOptions = []
        }
      }).catch(error => {
        console.error('获取客服列表失败:', error)
        this.adminOptions = []
      })
    },

    handleFilter() {
      this.listQuery.page = 1
      this.getList()
      this.getStatistics()
    },

    resetFilter() {
      this.listQuery = {
        page: 1,
        limit: 20,
        order_sn: '',
        admin_id: '',
        shop_id: '',
        start_time: '',
        end_time: ''
      }
      this.dateRange = []
      this.getList()
      this.getStatistics()
    },

    handleDetail(row) {
      fetchOrderAllocateDetail({ id: row.id }).then(response => {
        this.currentRecord = response.data
        this.detailDialogVisible = true
      }).catch(error => {
        console.error('获取详情失败:', error)
        this.$message.error('获取记录详情失败')
      })
    },

    handleExport() {
      const params = { ...this.listQuery }
      delete params.page
      delete params.limit

      // 处理日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        params.start_time = this.dateRange[0]
        params.end_time = this.dateRange[1]
      }

      const queryString = Object.keys(params)
        .filter(key => params[key] !== '' && params[key] !== null)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')

      const url = `${process.env.VUE_APP_BASE_API}/admin/order-allocate-log/export?${queryString}`
      window.open(url)
    },

    getMethodTagType(method) {
      const typeMap = {
        '在线客服分配': 'primary',
        '历史客服分配': 'success',
        '过滤手机号分配': 'warning',
        '公海重新分配': 'info',
        '分配失败': 'danger'
      }
      return typeMap[method] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .stats-row {
    margin-bottom: 20px;
  }

  .stats-card {
    text-align: center;
    .stats-number {
      font-size: 28px;
      font-weight: bold;
      color: #409EFF;
      margin-top: 10px;
    }
  }

  .filter-container {
    margin-bottom: 20px;
    .demo-form-inline {
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }

  .text-muted {
    color: #999;
    font-size: 12px;
  }

  .el-table {
    .cell {
      padding: 8px 0;
    }
  }

  // 线路权限展示组件样式
  .route-permission-display {
    .route-tag {
      margin-right: 5px;
      margin-bottom: 3px;
    }

    .view-more-btn {
      margin-left: 5px;
      padding: 0;
      font-size: 12px;
      color: #409EFF;
    }
  }

  // 线路权限详情对话框样式
  .route-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;

    .product-card {
      cursor: default;

      .product-info {
        .product-name {
          font-weight: bold;
          font-size: 14px;
          color: #303133;
          margin-bottom: 8px;
          line-height: 1.4;
        }

        .product-id {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
        }

        .product-third-id {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .no-products {
    text-align: center;
    padding: 20px 0;
  }

  // 详情对话框样式
  .detail-card {
    .el-card__header {
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
      
      .clearfix span {
        font-weight: bold;
        color: #303133;
      }
    }

    .detail-item {
      margin-bottom: 15px;
      padding: 8px 0;

      .detail-label {
        font-weight: bold;
        color: #606266;
        display: inline-block;
        min-width: 80px;
      }

      .detail-value {
        color: #303133;
        word-break: break-all;
      }
    }

    .el-row:last-child .detail-item {
      margin-bottom: 0;
    }
  }
}
</style>
