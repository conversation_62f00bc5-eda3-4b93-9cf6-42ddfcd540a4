<?php

declare(strict_types=1);

namespace base\auth;


/**
 * 获取控制器的权限注解
 */
#[\Attribute(\Attribute::TARGET_ALL | \Attribute::IS_REPEATABLE)]
class Auth
{
    /**
     * 操作标识符
     *
     * @var string
     */
    private string $action;
    public string $description;

    /**
     * @param string $action 操作标识符
     * @param string $description 描述,无实际作用
     */
    public function __construct(
        string       $action,
        string       $description = '',
    )
    {
        $this->action = $action;
        $this->description = $description;
    }


    /**
     * 获取注解数据 function
     *
     * @return array
     */
    public function get(): array
    {
        return [
            'action' => $this->action,
            'description' => $this->description,
        ];
    }

    public function getAction(): string
    {
        return $this->action;
    }
}
