<template>
  <div class="app-container">

    <div class="filter-container">
      <el-input v-model="listQuery.sn" placeholder="订单号" style="width: 300px;" class="filter-item" />

      <el-select v-model="listQuery.status" filterable placeholder="状态" class="filter-item" style="width: 120px;">
        <el-option label="未处理" value="0">未处理</el-option>
        <el-option label="已同意" value="1">已同意</el-option>
        <el-option label="拒绝" value="2">拒绝</el-option>
        <el-option label="取消" value="3">取消</el-option>
      </el-select>

      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getList">
        搜索
      </el-button>

      <el-button class="filter-item" type="primary" icon="el-icon-edit-outline" @click="dialogVisible=true">
        申请转入
      </el-button>
    </div>

    <el-table v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">

      <el-table-column align="center" fixed width="220" label="操作">
        <template slot-scope="scope">
          <el-button v-if="scope.row.self ==0 && scope.row.status == 0" type="primary" size="small" icon="el-icon-check" @click="onPass(scope.row)">
            同意
          </el-button>
          <el-button v-if="scope.row.self ==0 && scope.row.status == 0" type="success" size="small" icon="el-icon-close" @click="onRefuse(scope.row)">
            拒绝
          </el-button>
          <el-button v-if="scope.row.self ==3 && scope.row.status == 0" type="info" size="small" icon="el-icon-close" @click="onCancel(scope.row)">
            取消流转
          </el-button>
        </template>
      </el-table-column>

      <el-table-column align="center" label="转出" width="80" prop="outto.username" />

      <el-table-column align="center" label="转入" width="80" prop="into.username" />
      <el-table-column align="center" label="申请者" width="80" prop="apply.username" />
      <el-table-column align="center" label="订单号" width="220" prop="orders.sn" />
      <el-table-column align="center" label="产品" prop="orders.product_name" />

      <el-table-column align="center" label="总金额" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.orders && scope.row.orders.total_price/100 }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="状态" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.status| statusFilter }}</span>
        </template>
      </el-table-column>

      <el-table-column width="180px" align="center" label="申请时间">
        <template slot-scope="scope">
          <span>{{ scope.row.create_time | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>

      <el-table-column width="180px" align="center" label="修改时间">
        <template slot-scope="scope">
          <span>{{ scope.row.update_time | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-dialog title="申请转入订单" :visible.sync="dialogVisible">
      <el-form label-width="160px" :model="item">
        <el-form-item label="订单号">
          <el-input v-model="item.sn" name="check_sn" placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="平台">
          <el-radio v-for="(value,index) in oss" v-model="item.os" :key="item.os" :label="index">{{ value }}</el-radio>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onBack()">确 认</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
// import Pagination from '@/Wangeditor/Pagination'
import Pagination from '@/components/PaginationFixed'

export default {
  name: 'Orderlist',
  components: { Pagination },
  filters: {
    statusFilter(status) {
      const statusMap = {
        1: '同意',
        0: '申请中',
        2: '拒绝',
        3: '取消'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10
      },
      oss: {},
      item: {},
      dialogVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$axios.get('/admin/order/backlist', { params: this.listQuery }).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.oss = response.ext
        this.listLoading = false
      })
    },
    onBack() {
      this.$axios.post('/admin/order/back', this.item).then(res => {
        this.dialogVisible = false
        this.item = {}
        this.getList()
      }).catch(err => {

      })
    },
    onPass(item) {
      this.$axios.post('/admin/order/backpass', { id: item.id }).then(res => {
        this.dialogVisible = false
        this.item = {}
        this.getList()
      }).catch(err => {

      })
    },
    onRefuse(item) {
      this.$axios.post('/admin/order/backrefuse', { id: item.id }).then(res => {
        this.dialogVisible = false
        this.item = {}
        this.getList()
      }).catch(err => {

      })
    },
    onCancel(item) {
      this.$axios.post('/admin/order/backcancel', { id: item.id }).then(res => {
        this.dialogVisible = false
        this.item = {}
        this.getList()
      }).catch(err => {

      })
    }
  }
}
</script>
<style scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 52px; /* 分页条的高度，以避免内容重叠 */
}
</style>
