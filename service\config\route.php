<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use Webman\Route;

// 飞猪回调接口路由
Route::group('/api/fliggy', function () {
    Route::post('/pre-create-order', [\app\api\controller\FliggyController::class, 'preCreateOrder']);
    Route::post('/cancel-order', [\app\api\controller\FliggyController::class, 'cancelOrder']);
    Route::post('/create-order', [\app\api\controller\FliggyController::class, 'createOrder']);
    Route::post('/query-order', [\app\api\controller\FliggyController::class, 'queryOrder']);
    Route::post('/refund-order', [\app\api\controller\FliggyController::class, 'refundOrder']);
    Route::post('/update-order', [\app\api\controller\FliggyController::class, 'updateOrder']);
});

// 飞猪测试接口路由
Route::group('/api/fliggy-test', function () {
    Route::get('/signature', [\app\api\controller\FliggyTestController::class, 'testSignature']);
    Route::get('/create-order', [\app\api\controller\FliggyTestController::class, 'testCreateOrder']);
    Route::get('/query-order', [\app\api\controller\FliggyTestController::class, 'testQueryOrder']);
    Route::get('/refund', [\app\api\controller\FliggyTestController::class, 'testRefund']);
    Route::get('/update-order', [\app\api\controller\FliggyTestController::class, 'testUpdateOrder']);
});

// 管理员操作日志路由
Route::group('/admin/admin-operation-log', function () {
    Route::get('/index', [\app\admin\controller\AdminOperationLogController::class, 'index']);
    Route::get('/show', [\app\admin\controller\AdminOperationLogController::class, 'show']);
    Route::get('/statistics', [\app\admin\controller\AdminOperationLogController::class, 'statistics']);
    Route::get('/export', [\app\admin\controller\AdminOperationLogController::class, 'export']);
})->middleware([\app\middleware\adminAuth::class]);

Route::group('/admin/ota-route-group', function () {
    Route::get('/index', [\app\admin\controller\OtaRouteGroupController::class, 'index']);
    Route::get('/show', [\app\admin\controller\OtaRouteGroupController::class, 'show']);
    Route::post('/save', [\app\admin\controller\OtaRouteGroupController::class, 'save']);
    Route::post('/delete', [\app\admin\controller\OtaRouteGroupController::class, 'delete']);
    Route::post('/updateStatus', [\app\admin\controller\OtaRouteGroupController::class, 'updateStatus']);
    Route::get('/getActiveGroups', [\app\admin\controller\OtaRouteGroupController::class, 'getActiveGroups']);
    Route::get('/getProducts', [\app\admin\controller\OtaRouteGroupController::class, 'getProducts']);
    Route::post('/updateSort', [\app\admin\controller\OtaRouteGroupController::class, 'updateSort']);
    Route::get('/logs', [\app\admin\controller\OtaRouteGroupController::class, 'logs']);
})->middleware([\app\middleware\adminAuth::class]);

// 行程管理路由
Route::group('/admin/itinerary', function () {
    Route::get('/list', [\app\admin\controller\ItineraryController::class, 'list']);
    Route::get('/detail/{id}', [\app\admin\controller\ItineraryController::class, 'detail']);
    Route::post('/create', [\app\admin\controller\ItineraryController::class, 'create']);
    Route::post('/update/{id}', [\app\admin\controller\ItineraryController::class, 'update']);
    Route::post('/delete/{id}', [\app\admin\controller\ItineraryController::class, 'delete']);
    Route::post('/online/{id}', [\app\admin\controller\ItineraryController::class, 'online']);
    Route::post('/offline/{id}', [\app\admin\controller\ItineraryController::class, 'offline']);
    Route::get('/tree', [\app\admin\controller\ItineraryController::class, 'tree']);
})->middleware([\app\middleware\adminAuth::class]);

// 行程分类路由
Route::group('/admin/itinerary-category', function () {
    Route::get('/list', [\app\admin\controller\ItineraryCategoryController::class, 'list']);
    Route::get('/tree', [\app\admin\controller\ItineraryCategoryController::class, 'tree']);
    Route::get('/detail/{id}', [\app\admin\controller\ItineraryCategoryController::class, 'detail']);
    Route::post('/create', [\app\admin\controller\ItineraryCategoryController::class, 'create']);
    Route::post('/update/{id}', [\app\admin\controller\ItineraryCategoryController::class, 'update']);
    Route::post('/delete/{id}', [\app\admin\controller\ItineraryCategoryController::class, 'delete']);
})->middleware([\app\middleware\adminAuth::class]);

// 城市信息路由
Route::group('/admin/city-info', function () {
    Route::get('/json', [\app\admin\controller\CityInfoController::class, 'json']);
})->middleware([\app\middleware\adminAuth::class]);

// 库存管理路由
Route::group('/admin/itinerary', function () {
    Route::get('/inventory/month', [\app\admin\controller\Inventory::class, 'month']);
    Route::post('/inventory/batch-update', [\app\admin\controller\Inventory::class, 'batchUpdate']);
    Route::get('/inventory/date', [\app\admin\controller\Inventory::class, 'date']);
    Route::post('/inventory/update', [\app\admin\controller\Inventory::class, 'update']);
})->middleware([\app\middleware\adminAuth::class]);
