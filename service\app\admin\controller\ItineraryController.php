<?php
namespace app\admin\controller;

use app\model\ItineraryDayModel;
use app\model\ItineraryModel;
use app\model\ItineraryScheduleModel;
use app\model\ProductCategoryModel;
use support\Log;
use support\Request;
use support\Response;
use think\facade\Db;

class ItineraryController extends base {
    /**
     * 获取行程列表
     * @param Request $request
     * @return Response
     */
    public function list(Request $request): Response
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $status = $request->input('status');
        $keyword = $request->input('keyword');
        $destination = $request->input('destination');
        $departureCity = $request->input('departureCity');
        $categoryId = $request->input('categoryId');
        $productType = $request->input('productType');

        // 构建查询条件
        $query = ItineraryModel::mk()
            ->field([
                'id', 'title', 'price', 'original_price', 'days', 'nights', 'destination', 'departure_city',
                'product_type', 'cover_image', 'status', 'travel_mode', 'group_type', 'min_group_size',
                'max_group_size', 'collect_type', 'deposit', 'promotion_price', 'category_id', 'category_path',
                'valid_from', 'valid_to', 'is_long_term', 'single_room_surcharge', 'room_upgrade_surcharge',
                'create_time', 'update_time',
                // 主题相关字段
                'themes_ids',
                // 交易规则相关字段
                'consumable_date_type', 'consumable_days', 'consumable_dates',
                // 限购规则
                'purchase_limit_single', 'purchase_limit_per_order', 'purchase_limit_rules', 'purchase_limit_details',
                // 预约规则
                'earliest_booking_days', 'advance_booking_required', 'advance_booking_range',
                'advance_booking_time', 'advance_booking_time_unit',
                // 取消规则
                'has_no_cancellation_after_appointment', 'refund_before_appointment', 'auto_refund_after_expiration',
                'has_breach_of_contract_terms', 'breach_of_contract_terms', 'breach_of_contract_base'
            ])
            ->where('delete_time', 0);

        // 应用筛选条件
        if (!empty($status)) {
            $query = $query->where('status', $status);
        }
        if (!empty($keyword)) {
            $query = $query->where('title', 'like', "%$keyword%");
        }
        if (!empty($destination)) {
            $query = $query->where('destination', $destination);
        }
        if (!empty($departureCity)) {
            $query = $query->whereFindInSet('departure_city', $departureCity);
        }
        if (!empty($categoryId)) {
            $query = $query->where('category_id', $categoryId);
        }
        if (!empty($productType)) {
            $query = $query->where('product_type', $productType);
        }

        // 执行查询
        $total = $query->count();
        $list = $query->page($page, $pageSize)
        ->order('id', 'desc')->append(['id'])
            ->withAttr('themes_ids', function ($value) {
                return str2Arr($value);
            })
        ->select();

        // 转换为前端格式
        $items = [];
        foreach ($list as $item) {
            $items[] = $this->convertToFrontend($item->toArray());
        }

        return $this->success([
            'list' => $items,
            'total' => $total,
            'page' => (int)$page,
            'pageSize' => (int)$pageSize,
        ]);
    }

    /**
     * 获取行程详情
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function detail(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 查询行程基本信息
        $detail = ItineraryModel::mk()
            ->with(['scheduleDays'=> ['schedules']])
            ->field([
                'id', 'title', 'price', 'original_price', 'days', 'nights', 'destination', 'departure_city',
                'product_type', 'cover_image', 'images', 'image_description', 'description', 'features', 'status',
                'travel_mode', 'group_type', 'min_group_size', 'max_group_size', 'collect_type', 'deposit',
                'promotion_price', 'category_id', 'category_path', 'valid_from', 'valid_to', 'is_long_term',
                'single_room_surcharge', 'room_upgrade_surcharge', 'create_time', 'update_time',
                // 主题相关字段
                'themes_ids',
                // 交易规则相关字段
                'consumable_date_type', 'consumable_days', 'consumable_dates',
                // 限购规则
                'purchase_limit_single', 'purchase_limit_per_order', 'purchase_limit_rules', 'purchase_limit_details',
                // 预约规则
                'earliest_booking_days', 'advance_booking_required', 'advance_booking_range',
                'advance_booking_time', 'advance_booking_time_unit',
                // 取消规则
                'has_no_cancellation_after_appointment', 'refund_before_appointment', 'auto_refund_after_expiration',
                'has_breach_of_contract_terms', 'breach_of_contract_terms', 'breach_of_contract_base'
            ])
            ->where('id', $id)
            ->append(['id'])
            ->withAttr('themes_ids', function ($value) {
                return str2Arr($value);
            })
            ->find();

        if (!$detail) {
            return error(400, '行程不存在');
        }

        $result = $detail->toArray();
        return $this->success($result);
    }

    /**
     * 创建行程
     * @param Request $request
     * @return Response
     */
    public function create(Request $request): Response
    {
        $data = $request->post();

        // 开启事务
        Db::startTrans();
        try {
            // 处理行程基本信息
            $itineraryData = [
                'title' => $data['title'],
                'price' => $data['price'],
                'original_price' => $data['original_price'] ?? 0,
                'days' => $data['days'],
                'nights' => $data['nights'],
                'destination' => $data['destination'],
                'departure_city' => $data['departureCity'],
                'product_type' => $data['product_type'],
                'cover_image' => $data['cover_image'],
                'images' => $data['images'] ?? [],
                'image_description' => $data['image_description'] ?? [],
                'description' => $data['description'],
                'features' => isset($data['features']) ? json_encode($data['features']) : null,
                'status' => $data['status'] ?? 'offline',
                'travel_mode' => $data['travel_mode'],
                'group_type' => $data['group_type'],
                'min_group_size' => $data['min_group_size'],
                'max_group_size' => $data['max_group_size'],
                // 'collect_type' => $data['collectType'] === 'capitation' ? 'capitation' : 'collectAll',
                'deposit' => 0,
                'promotion_price' => $data['promotion_price'] ?? 0,
                'category_id' => end($data['categoryId']),
                'category_path' => $data['categoryId'],
                'is_long_term' => $data['is_long_term'] ? 1 : 0,
                'valid_from' => !empty($data['valid_from']) ? date('Y-m-d', strtotime($data['valid_from']))  : null,
                'valid_to' =>    !empty($data['valid_to']) ? date('Y-m-d', strtotime($data['valid_to']))  : null,
                'single_room_surcharge' => $data['single_room_surcharge'] ?? 0,
                'room_upgrade_surcharge' => $data['room_upgrade_surcharge'] ?? 0,

                // 交易规则相关字段
                'consumable_date_type' => $data['consumable_date_type'] ?? null,
                'consumable_days' => $data['consumable_days'] ?? null,
                'consumable_dates' => isset($data['consumable_dates']) ? json_encode($data['consumable_dates']) : null,

                // 限购规则
                'purchase_limit_single' => isset($data['purchase_limit_single']) ? ($data['purchase_limit_single'] ? 1 : 0) : null,
                'purchase_limit_per_order' => $data['purchase_limit_per_order'] ?? null,
                'purchase_limit_rules' => isset($data['purchase_limit_rules']) ? ($data['purchase_limit_rules'] ? 1 : 0) : null,
                'purchase_limit_details' => $data['purchase_limit_details'] ?? null,

                // 预约规则
                'earliest_booking_days' => $data['earliest_booking_days'] ?? null,
                'advance_booking_required' => isset($data['advance_booking_required']) ? ($data['advance_booking_required'] ? 1 : 0) : null,
                'advance_booking_range' => $data['advance_booking_range'] ?? null,
                'advance_booking_time' => $data['advance_booking_time'] ?? null,
                'advance_booking_time_unit' => $data['advance_booking_time_unit'] ?? null,

                // 取消规则
                'has_no_cancellation_after_appointment' => isset($data['has_no_cancellation_after_appointment']) ? ($data['has_no_cancellation_after_appointment'] ? 1 : 0) : null,
                'refund_before_appointment' => isset($data['refund_before_appointment']) ? ($data['refund_before_appointment'] ? 1 : 0) : null,
                'auto_refund_after_expiration' => isset($data['auto_refund_after_expiration']) ? ($data['auto_refund_after_expiration'] ? 1 : 0) : null,
                'has_breach_of_contract_terms' => isset($data['has_breach_of_contract_terms']) ? ($data['has_breach_of_contract_terms'] ? 1 : 0) : null,
                'breach_of_contract_terms' => isset($data['breach_of_contract_terms']) ? json_encode($data['breach_of_contract_terms']) : null,
                'breach_of_contract_base' => $data['breach_of_contract_base'] ?? null,
            ];

            // 创建行程
            $itinerary = ItineraryModel::mk();
            $itinerary->save($itineraryData);
            $itineraryId = $itinerary->id;

            if (!empty($data['scheduleDays'])) {
                foreach ($data['scheduleDays'] as $k => $dayInfo) {
                    $scheduleDay = [
                        'day' => $dayInfo['day'] ?? ($k+1),
                        'itinerary_id' => $itineraryId,
                        'title' => $dayInfo['title'],
                        'description' => $dayInfo['description'] ?? '',
                        'meeting_point' => $dayInfo['meetingPoint'] ?? null,
                        'meeting_time' => isset($dayInfo['meetingTime']) ? date('H:i:s', strtotime($dayInfo['meetingTime'])) : null,
                    ];
                    $itineraryDay = ItineraryDayModel::mk();
                    $itineraryDay->id = $itineraryDay->insertGetId($scheduleDay);
                    $itineraryDayId = $itineraryDay->id;
                    // 处理行程安排
                    if (!empty($dayInfo['schedules'])) {
                        foreach ($dayInfo['schedules'] as $schedule) {
                            $scheduleData = [
                                'itinerary_id' => $itineraryId,
                                'itinerary_day_id' => $itineraryDayId,
                                'activities' => $schedule['activities'],
                                'images' => $schedule['images'] ?? [],
                                'type' => $schedule['type'],
                                'location' => $schedule['location'],
                                'start_time' => isset($schedule['start_time']) ? date('H:i:s', strtotime($schedule['start_time'])) : null,
                                'duration' => $schedule['duration'] ?? 0,
                            ];

                            ItineraryScheduleModel::mk()->save($scheduleData);
                        }
                    }
                }
            }
            Db::commit();
            return $this->success(['id' => $itineraryId]);
        } catch (\Exception $e) {
            Db::rollback();
            throw  $e;
        }
    }

    /**
     * 更新行程
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function update(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        $data = $request->post();

        // 验证行程是否存在
        $detail = ItineraryModel::mk()->where('id', $id)->append(['id'])->find();;
        if (!$detail) {
            return error(400, '行程不存在');
        }
        $itineraryId = $detail['id'];
        // 开启事务
        Db::startTrans();
        try {
            // 处理行程基本信息
            $itineraryData = [
                'id' => $id,
                'title' => $data['title'],
                'price' => $data['price'],
                'original_price' => $data['original_price'],
                'days' => $data['days'],
                'nights' => $data['nights'],
                'destination' => $data['destination'],
                'departure_city' => $data['departureCity'],
                'product_type' => $data['product_type'],
                'cover_image' => $data['cover_image'],
                'images' => $data['images'] ?? [],
                'image_description' => $data['image_description'] ?? [],
                'description' => $data['description'],
                'features' => isset($data['features']) ? json_encode($data['features']) : null,
                'status' => $data['status'],
                'travel_mode' => $data['travel_mode'],
                'group_type' => $data['group_type'],
                'min_group_size' => $data['min_group_size'],
                'max_group_size' => $data['max_group_size'],
               // 'collect_type' => $data['collectType'] === 'capitation' ? 'capitation' : 'collectAll',
                'deposit' => $data['deposit'],
               // 'promotion_price' => $data['promotionPrice'],
                'category_id' => end($data['categoryId']),
                'category_path' => $data['categoryId'],
                'is_long_term' => $data['is_long_term'] ? 1 : 0,
                'valid_from' => !empty($data['valid_from']) ? date('Y-m-d', strtotime($data['valid_from']))  : null,
                'valid_to' =>    !empty($data['valid_to']) ? date('Y-m-d', strtotime($data['valid_to']))  : null,
                'single_room_surcharge' => $data['single_room_surcharge'] ?? 0,
                'room_upgrade_surcharge' => $data['room_upgrade_surcharge'] ?? 0,

                // 交易规则相关字段
                'consumable_date_type' => $data['consumable_date_type'] ?? null,
                'consumable_days' => $data['consumable_days'] ?? null,
                'consumable_dates' => isset($data['consumable_dates']) ? json_encode($data['consumable_dates']) : null,

                // 限购规则
                'purchase_limit_single' => isset($data['purchase_limit_single']) ? ($data['purchase_limit_single'] ? 1 : 0) : null,
                'purchase_limit_per_order' => $data['purchase_limit_per_order'] ?? null,
                'purchase_limit_rules' => isset($data['purchase_limit_rules']) ? ($data['purchase_limit_rules'] ? 1 : 0) : null,
                'purchase_limit_details' => $data['purchase_limit_details'] ?? null,

                // 预约规则
                'earliest_booking_days' => $data['earliest_booking_days'] ?? null,
                'advance_booking_required' => isset($data['advance_booking_required']) ? ($data['advance_booking_required'] ? 1 : 0) : null,
                'advance_booking_range' => $data['advance_booking_range'] ?? null,
                'advance_booking_time' => $data['advance_booking_time'] ?? null,
                'advance_booking_time_unit' => $data['advance_booking_time_unit'] ?? null,

                // 取消规则
                'has_no_cancellation_after_appointment' => isset($data['has_no_cancellation_after_appointment']) ? ($data['has_no_cancellation_after_appointment'] ? 1 : 0) : null,
                'refund_before_appointment' => isset($data['refund_before_appointment']) ? ($data['refund_before_appointment'] ? 1 : 0) : null,
                'auto_refund_after_expiration' => isset($data['auto_refund_after_expiration']) ? ($data['auto_refund_after_expiration'] ? 1 : 0) : null,
                'has_breach_of_contract_terms' => isset($data['has_breach_of_contract_terms']) ? ($data['has_breach_of_contract_terms'] ? 1 : 0) : null,
                'breach_of_contract_terms' => isset($data['breach_of_contract_terms']) ? json_encode($data['breach_of_contract_terms']) : null,
                'breach_of_contract_base' => $data['breach_of_contract_base'] ?? null,
            ];

            // 更新行程
            $detail->save($itineraryData);

            // 处理行程安排
            if (!empty($data['scheduleDays'])) {
                // 先删除原有行程安排
                $scheduleDayModel = new ItineraryDayModel();
                $scheduleDayModel->where('itinerary_id', $id)->delete();
                $scheduleModel = new ItineraryScheduleModel();
                $scheduleModel->where('itinerary_id', $id)->delete();

                foreach ($data['scheduleDays'] as $k => $dayInfo) {
                    $scheduleDay = [
                        'day' => $dayInfo['day'] ?? ($k+1),
                        'itinerary_id' => $itineraryId,
                        'title' => $dayInfo['title'] ?? '',
                        'description' => $dayInfo['description'] ?? '',
                        'meeting_point' => $dayInfo['meetingPoint'] ?? null,
                        'meeting_time' => isset($dayInfo['meetingTime']) ? date('H:i:s', strtotime($dayInfo['meetingTime'])) : null,
                    ];
                    $itineraryDay = ItineraryDayModel::mk();
                    $itineraryDay->id = $itineraryDay->insertGetId($scheduleDay);
                    $itineraryDayId = $itineraryDay->id;
                    // 处理行程安排
                    if (!empty($dayInfo['schedules'])) {
                        foreach ($dayInfo['schedules'] as $schedule) {
                            $scheduleData = [
                                'itinerary_id' => $itineraryId,
                                'itinerary_day_id' => $itineraryDayId,
                                'activities' => $schedule['activities'],
                                'images' => $schedule['images'] ?? [],
                                'type' => $schedule['type'],
                                'location' => $schedule['location'],
                                'start_time' => isset($schedule['start_time']) ? date('H:i:s', strtotime($schedule['start_time'])) : null,
                                'duration' => $schedule['duration'] ?? 0,
                            ];

                            ItineraryScheduleModel::mk()->save($scheduleData);
                        }
                    }
                }
            }

            Db::commit();

            return $this->success([]);
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error([
                'code' => 500,
                'msg' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除行程
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function delete(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        // 验证行程是否存在
        $model = new ItineraryModel();
        $detail = $model->getDetail($id);
        if (!$detail) {
            return json([
                'code' => 404,
                'msg' => '行程不存在'
            ]);
        }

        // 删除行程
        $result = $model->where('id', $id)->delete();

        // 删除关联的行程安排
        $scheduleModel = new ItineraryScheduleModel();
        $scheduleModel->where('itinerary_id', $id)->delete();

        return $this->success([
            'code' => 200,
            'msg' => '删除成功'
        ]);
    }

    /**
     * 获取行程安排列表
     * @param Request $request
     * @return Response
     */
    public function scheduleList(Request $request): Response
    {
        $itineraryId = $request->input('itineraryId');

        if (!$itineraryId) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        // 验证行程是否存在
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->getDetail($itineraryId);
        if (!$itinerary) {
            return json([
                'code' => 404,
                'msg' => '行程不存在'
            ]);
        }

        $model = new ItineraryScheduleModel();
        $list = $model->getSchedulesByItineraryId($itineraryId);

        return $this->success([
            'code' => 0,
            'msg' => '获取成功',
            'data' => [
                'list' => $list,
                'itinerary' => $itinerary
            ]
        ]);
    }

    /**
     * 获取行程安排详情
     * @param Request $request
     * @return Response
     */
    public function scheduleDetail(Request $request): Response
    {
        $id = $request->input('id');

        if (!$id) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        $model = new ItineraryScheduleModel();
        $detail = $model->getDetail($id);

        if (!$detail) {
            return json([
                'code' => 404,
                'msg' => '行程安排不存在'
            ]);
        }

        // 获取所属行程信息
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->getDetail($detail['itinerary_id']);
        $detail['itinerary'] = $itinerary;

        return $this->success([
            'code' => 0,
            'msg' => '获取成功',
            'data' => $detail
        ]);
    }

    /**
     * 保存行程安排
     * @param int $itineraryId 行程ID
     * @param array $schedules 行程安排数据
     * @return bool
     */
    private function saveSchedules(int $itineraryId, array $schedules): bool
    {
        if (empty($schedules)) {
            return false;
        }

        // 验证行程是否存在
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->getDetail($itineraryId);
        if (!$itinerary) {
            return false;
        }

        $scheduleModel = new ItineraryScheduleModel();

        // 先删除原有行程安排
        $scheduleModel->where('itinerary_id', $itineraryId)->delete();

        // 添加时间戳和行程ID
        $now = time();
        foreach ($schedules as &$schedule) {
            // 转换字段为下划线格式
            $schedule = $this->convertScheduleFieldsToDB($schedule);

            // 验证天数是否合法
            if (isset($schedule['day']) && ($schedule['day'] < 1 || $schedule['day'] > $itinerary['days'])) {
                continue; // 跳过无效的行程安排
            }

            $schedule['itinerary_id'] = $itineraryId;
            $schedule['create_time'] = $schedule['update_time'] = $now;
        }

        // 保存行程安排
        return $scheduleModel->batchSave($schedules);
    }

    /**
     * 更新单个行程安排
     * @param Request $request
     * @return Response
     */
    public function updateSchedule(Request $request): Response
    {
        $id = $request->input('id');
        $itineraryId = $request->input('itineraryId');

        if (!$id || !$itineraryId) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        $data = $request->post();
        unset($data['id'], $data['itineraryId']);

        // 转换字段为下划线格式
        $data = $this->convertScheduleFieldsToDB($data);

        // 验证行程是否存在
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->getDetail($itineraryId);
        if (!$itinerary) {
            return json([
                'code' => 404,
                'msg' => '行程不存在'
            ]);
        }

        // 验证行程安排是否存在
        $scheduleModel = new ItineraryScheduleModel();
        $schedule = $scheduleModel->getDetail($id);
        if (!$schedule) {
            return json([
                'code' => 404,
                'msg' => '行程安排不存在'
            ]);
        }

        // 验证行程安排是否属于该行程
        if ($schedule['itinerary_id'] != $itineraryId) {
            return json([
                'code' => 403,
                'msg' => '无权限修改此行程安排'
            ]);
        }

        // 验证天数是否合法
        if (isset($data['day']) && ($data['day'] < 1 || $data['day'] > $itinerary['days'])) {
            return json([
                'code' => 400,
                'msg' => '天数超出行程范围'
            ]);
        }

        // 添加更新时间
        $data['update_time'] = time();

        // 更新行程安排
        $result = $scheduleModel->where('id', $id)->update($data);

        return json([
            'code' => 0,
            'msg' => '更新成功'
        ]);
    }

    /**
     * 删除行程安排
     * @param Request $request
     * @return Response
     */
    public function deleteSchedule(Request $request): Response
    {
        $id = $request->input('id');
        $itineraryId = $request->input('itineraryId');

        if (!$id || !$itineraryId) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        // 验证行程是否存在
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->getDetail($itineraryId);
        if (!$itinerary) {
            return json([
                'code' => 404,
                'msg' => '行程不存在'
            ]);
        }

        // 验证行程安排是否存在
        $scheduleModel = new ItineraryScheduleModel();
        $schedule = $scheduleModel->getDetail($id);
        if (!$schedule) {
            return json([
                'code' => 404,
                'msg' => '行程安排不存在'
            ]);
        }

        // 验证行程安排是否属于该行程
        if ($schedule['itinerary_id'] != $itineraryId) {
            return json([
                'code' => 403,
                'msg' => '无权限删除此行程安排'
            ]);
        }

        // 删除行程安排
        $result = $scheduleModel->where('id', $id)->delete();

        return json([
            'code' => 0,
            'msg' => '删除成功'
        ]);
    }


    /**
     * 将小驼峰转换为下划线格式（行程安排字段）
     * @param array $data
     * @return array
     */
    private function convertScheduleFieldsToDB(array $data): array
    {
        $result = [];
        $map = [
            'itineraryId' => 'itinerary_id',
            'meetingPoint' => 'meeting_point',
            'meetingTime' => 'meeting_time',
            'startTime' => 'start_time',
            'mealType' => 'meal_type',
            'adultMealIncluded' => 'adult_meal_included',
            'childMealIncluded' => 'child_meal_included',
            'mealPrice' => 'meal_price',
            'transportType' => 'transport_type',
            'flightNumber' => 'flight_number',
            'departureCity' => 'departure_city',
            'arrivalCity' => 'arrival_city',
            'stayType' => 'stay_type',
            'hotelName' => 'hotel_name',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'deleteTime' => 'delete_time'
        ];

        foreach ($data as $key => $value) {
            if (isset($map[$key])) {
                $result[$map[$key]] = $value;
            } else {
                $result[$key] = $value;
            }
        }

        return $result;
    }

    /**
     * 上架行程
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function online(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 验证行程是否存在
        $detail = ItineraryModel::mk()->where('id', $id)->find();
        if (!$detail) {
            return error(400, '行程不存在');
        }

        // 更新状态
        $detail->save([
            'status' => 'online',
        ]);

        return success([
            'id' => $detail['id'],
            'status' => 'online',
        ], '上架成功');
    }

    /**
     * 下架行程
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function offline(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 验证行程是否存在
        // 验证行程是否存在
        $detail = ItineraryModel::mk()->where('id', $id)->find();
        if (!$detail) {
            return error(400, '行程不存在');
        }
        // 更新状态
        $detail->save([
            'status' => 'offline',
        ]);

        return success([], '上架成功');
    }

    /**
     * 批量删除行程
     * @param Request $request
     * @return Response
     */
    public function batchDelete(Request $request): Response
    {
        $ids = $request->post('ids', []);

        if (empty($ids) || !is_array($ids)) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        $model = new ItineraryModel();
        $scheduleModel = new ItineraryScheduleModel();

        // 删除行程
        $result = $model->whereIn('id', $ids)->delete();

        // 删除关联的行程安排
        $scheduleModel->whereIn('itinerary_id', $ids)->delete();

        return json([
            'code' => 0,
            'msg' => '批量删除成功'
        ]);
    }

    /**
     * 批量上架行程
     * @param Request $request
     * @return Response
     */
    public function batchOnline(Request $request): Response
    {
        $ids = $request->post('ids', []);

        if (empty($ids) || !is_array($ids)) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        $model = new ItineraryModel();

        // 更新状态
       $model->whereIn('id', $ids)->update([
            'status' => 'online'
        ]);

        return success();
    }

    /**
     * 批量下架行程
     * @param Request $request
     * @return Response
     */
    public function batchOffline(Request $request): Response
    {
        $ids = $request->post('ids', []);

        if (empty($ids) || !is_array($ids)) {
            return json([
                'code' => 400,
                'msg' => '参数错误'
            ]);
        }

        $model = new ItineraryModel();

        // 更新状态
         $model->whereIn('id', $ids)->update([
            'status' => 'offline',
            'update_time' => time()
        ]);

        return success();
    }

    /**
     * 将数据库格式转换为前端格式
     * @param array $data
     * @return array
     */
    private function convertToFrontend(array $data): array
    {
        $result = [];
        $map = [
            'original_price' => 'originalPrice',
            'departure_city' => 'departureCity',
            'product_type' => 'productType',
            'cover_image' => 'coverImage',
            'travel_mode' => 'travelMode',
            'group_type' => 'groupType',
            'min_group_size' => 'minGroupSize',
            'max_group_size' => 'maxGroupSize',
            'collect_type' => 'collectType',
            'promotion_price' => 'promotionPrice',
            'category_id' => 'categoryId',
            'category_path' => 'categoryPath',
            'valid_from' => 'validFrom',
            'valid_to' => 'validTo',
            'is_long_term' => 'isLongTerm',
            'create_time' => 'createdTime',
            'update_time' => 'updatedTime',
            'delete_time' => 'deleteTime',
            'single_room_surcharge' => 'singleRoomSurcharge',
            'room_upgrade_surcharge' => 'roomUpgradeSurcharge',

            // 交易规则相关字段
            'consumable_date_type' => 'consumableDateType',
            'consumable_days' => 'consumableDays',
            'consumable_dates' => 'consumableDates',

            // 限购规则
            'purchase_limit_single' => 'purchaseLimitSingle',
            'purchase_limit_per_order' => 'purchaseLimitPerOrder',
            'purchase_limit_rules' => 'purchaseLimitRules',
            'purchase_limit_details' => 'purchaseLimitDetails',

            // 预约规则
            'earliest_booking_days' => 'earliestBookingDays',
            'advance_booking_required' => 'advanceBookingRequired',
            'advance_booking_range' => 'advanceBookingRange',
            'advance_booking_time' => 'advanceBookingTime',
            'advance_booking_time_unit' => 'advanceBookingTimeUnit',

            // 取消规则
            'has_no_cancellation_after_appointment' => 'hasNoCancellationAfterAppointment',
            'refund_before_appointment' => 'refundBeforeAppointment',
            'auto_refund_after_expiration' => 'autoRefundAfterExpiration',
            'has_breach_of_contract_terms' => 'hasBreachOfContractTerms',
            'breach_of_contract_terms' => 'breachOfContractTerms',
            'breach_of_contract_base' => 'breachOfContractBase'
        ];

        foreach ($data as $key => $value) {
            if (isset($map[$key])) {
                $result[$map[$key]] = $value;
            } else {
                $result[$key] = $value;
            }
        }

        // 处理时间戳转为ISO格式
        if (isset($result['createdTime']) && is_numeric($result['createdTime'])) {
            $result['createdTime'] = date('c', $result['createdTime']);
        }
        if (isset($result['updatedTime']) && is_numeric($result['updatedTime'])) {
            $result['updatedTime'] = date('c', $result['updatedTime']);
        }

        // 处理布尔值字段
        $booleanFields = [
            'isLongTerm', 'purchaseLimitSingle', 'purchaseLimitRules', 'advanceBookingRequired',
            'hasNoCancellationAfterAppointment', 'refundBeforeAppointment',
            'autoRefundAfterExpiration', 'hasBreachOfContractTerms'
        ];

        foreach ($booleanFields as $field) {
            if (isset($result[$field])) {
                $result[$field] = (bool)$result[$field];
            }
        }

        // 处理JSON字段
        if (isset($result['categoryPath']) && is_string($result['categoryPath'])) {
            $result['categoryPath'] = json_decode($result['categoryPath'], true);
        }

        if (isset($result['features']) && is_string($result['features'])) {
            $result['features'] = json_decode($result['features'], true);
        }

        if (isset($result['images']) && is_string($result['images'])) {
            $result['images'] = json_decode($result['images'], true);
        }

        if (isset($result['imageDescription']) && is_string($result['imageDescription'])) {
            $result['imageDescription'] = json_decode($result['imageDescription'], true);
        }

        if (isset($result['consumableDates']) && is_string($result['consumableDates'])) {
            $result['consumableDates'] = json_decode($result['consumableDates'], true);
        }

        if (isset($result['breachOfContractTerms']) && is_string($result['breachOfContractTerms'])) {
            $result['breachOfContractTerms'] = json_decode($result['breachOfContractTerms'], true);
        }

        return $result;
    }

    /**
     * 将行程安排从数据库格式转换为前端格式
     * @param array $data
     * @return array
     */
    private function convertScheduleToFrontend(array $data): array
    {
        $result = [];
        $map = [
            'itinerary_id' => 'itineraryId',
            'meeting_point' => 'meetingPoint',
            'meeting_time' => 'meetingTime',
            'start_time' => 'startTime',
            'meal_type' => 'mealType',
            'adult_meal_included' => 'adultMealIncluded',
            'child_meal_included' => 'childMealIncluded',
            'meal_price' => 'mealPrice',
            'transport_type' => 'transportType',
            'flight_number' => 'flightNumber',
            'departure_city' => 'departureCity',
            'arrival_city' => 'arrivalCity',
            'stay_type' => 'stayType',
            'hotel_name' => 'hotelName',
            'create_time' => 'createTime',
            'update_time' => 'updateTime',
            'delete_time' => 'deleteTime'
        ];

        foreach ($data as $key => $value) {
            if (isset($map[$key])) {
                $result[$map[$key]] = $value;
            } else {
                $result[$key] = $value;
            }
        }

        return $result;
    }

    /**
     * 获取分类树结构
     * @param Request $request
     */
    public function tree(Request $request)
    {
        try {
            $model = new ProductCategoryModel();

            // 获取所有分类，然后构建树形结构
            $categories = $model->where([
                'delete_time' => 0
            ])->select();

            // 组织分类范围信息
            $categoryScopeInfo = [];
            foreach ($categories as $category) {
                if (!empty($category['allow_access_scope']) || !empty($category['no_access_scope']) || !empty($category['detail_url'])) {
                    $categoryScopeInfo[$category['category_id']] = [
                        'allow_access_product_scope' => $category['allow_access_scope'] ?? '',
                        'no_access_product_scope' => $category['no_access_scope'] ?? '',
                        'detail_url' => $category['detail_url'] ?? '暂无'
                    ];
                }
            }

            // 构建树形结构
            $categoryTreeList = $this->buildCategoryTree($categories, 0);

            // 构建响应数据
            $responseData = [
                'BaseResp' => [
                    'StatusCode' => 0,
                    'StatusMessage' => ''
                ],
                'category_scope_info' => $categoryScopeInfo,
                'category_tree_list' => $categoryTreeList,
                'status_code' => 0,
                'status_msg' => '',
                'now' => time() * 1000,
                'hit_grey' => true,
                'is_travel_history_good' => false
            ];

            return success($responseData);
        } catch (\Exception $e) {
            return json([
                'BaseResp' => [
                    'StatusCode' => 1,
                    'StatusMessage' => $e->getMessage()
                ],
                'status_code' => 1,
                'status_msg' => '获取失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 构建分类树结构
     * @param   $categories
     * @param int $parentId 父级ID
     * @return array
     */
    private function buildCategoryTree(  $categories, int $parentId): array
    {
        $tree = [];

        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $item = [
                    'ParentId' => $parentId,
                    'category_id' => $category['category_id'],
                    'category_name' => $category['category_name'],
                    'access_status' => $category['access_status'],
                    'category_discard' => (bool)$category['category_discard']
                ];

                // 添加可选字段
                if (!empty($category['price_limit'])) {
                    $item['price_limit'] = $category['price_limit'];
                }

                // 如果状态是受限，添加拒绝原因
                if ($category['access_status'] == 2) {
                    $item['reject_reason'] = [
                        'reject_title' => 'category_need_merchant_category',
                        'reject_chinese_title' => '当前类目未开通',
                        'reject_content' => [
                            '如需创建商品,请新增辅营类目或修改主营类目',
                            '建议新增经营类目：' . $category['category_name']
                        ]
                    ];
                }

                // 递归添加子分类
                $children = $this->buildCategoryTree($categories, $category['category_id']);
                $item['sub_category_list'] = $children;

                $tree[] = $item;
            }
        }

        return $tree;
    }

    /**
     * 更新线路主题
     * @param Request $request
     * @param string $id
     * @return Response
     */
    public function updateThemes(Request $request, string $id = ''): Response
    {
        // 尝试从路径获取ID，如果没有则从请求参数中获取
        if (empty($id)) {
            $id = $request->input('id');
        }

        if (!$id) {
            return error(400, '参数错误');
        }

        // 获取主题ID数组
        $themeIds = $request->post('themeIds', []);

        // 验证线路是否存在
        $itinerary = ItineraryModel::mk()->where('id', $id)->find();
        if (!$itinerary) {
            return error(404, '线路不存在');
        }

        // 将主题ID数组转换为逗号分隔的字符串
        $themesIdStr = '';
        if (!empty($themeIds)) {
            if (is_array($themeIds)) {
                $themesIdStr = implode(',', $themeIds);
            } else {
                $themesIdStr = (string)$themeIds;
            }
        }

        // 更新线路的主题
        $result = $itinerary->save([
            'themes_ids' => $themesIdStr,
        ]);

        if ($result) {
            return success(['id' => $id], '更新主题成功');
        } else {
            return error(500, '更新主题失败');
        }
    }

    /**
     * 获取测试行程数据
     */
    private function getTestItineraryData()
    {
        return [
            'id' => 1,
            'title' => '北京3天2晚经典游',
            'price' => 1299.00,
            'original_price' => 1599.00,
            'days' => 3,
            'nights' => 2,
            'destination' => ['110000', '110100'], // 北京市
            'departure_city' => ['310000', '310100'], // 上海市
            'product_type' => 'group_tour',
            'categoryId' => [32000000, 32006000, 32006002], // 度假旅游服务 > 境内行程游 > 境内跟团游
            'cover_image' => '/static/images/beijing-cover.jpg',
            'images' => ['/static/images/beijing1.jpg', '/static/images/beijing2.jpg'],
            'image_description' => ['天安门广场', '故宫博物院'],
            'description' => '探索北京的历史文化，游览天安门、故宫、长城等著名景点',
            'features' => '专业导游、豪华大巴、精选酒店',
            'status' => 'draft',
            'travel_mode' => 'flight',
            'group_type' => 'regular',
            'min_group_size' => 10,
            'max_group_size' => 30,
            'collect_type' => 'immediate',
            'deposit' => 300.00,
            'cost_includes' => '住宿、交通、门票、导游服务',
            'cost_excludes' => '个人消费、保险',
            'refund_policy' => '出发前7天可免费取消',
            'scheduleDays' => [
                [
                    'day' => 1,
                    'title' => '第1天',
                    'description' => '抵达北京，入住酒店',
                    'schedules' => [
                        [
                            'type' => 'transport',
                            'time' => '09:00',
                            'location' => '首都国际机场',
                            'duration' => 60,
                            'activities' => '接机服务，前往酒店办理入住',
                            'images' => ['/static/images/airport.jpg']
                        ],
                        [
                            'type' => 'accommodation',
                            'time' => '14:00',
                            'location' => '北京王府井大酒店',
                            'duration' => 0,
                            'activities' => '办理入住手续，休息调整',
                            'images' => ['/static/images/hotel.jpg']
                        ],
                        [
                            'type' => 'attraction',
                            'time' => '16:00',
                            'location' => '天安门广场',
                            'duration' => 120,
                            'activities' => '游览天安门广场，观看升旗仪式',
                            'images' => ['/static/images/tiananmen.jpg']
                        ]
                    ]
                ],
                [
                    'day' => 2,
                    'title' => '第2天',
                    'description' => '故宫深度游',
                    'schedules' => [
                        [
                            'type' => 'meal',
                            'time' => '08:00',
                            'location' => '酒店餐厅',
                            'duration' => 60,
                            'activities' => '享用丰盛早餐',
                            'images' => ['/static/images/breakfast.jpg']
                        ],
                        [
                            'type' => 'attraction',
                            'time' => '09:30',
                            'location' => '故宫博物院',
                            'duration' => 240,
                            'activities' => '深度游览故宫，了解明清历史文化',
                            'images' => ['/static/images/forbidden-city.jpg']
                        ]
                    ]
                ]
            ]
        ];
    }

    /**
     * 获取测试的具体安排数据
     */
    private function getTestScheduleData($day)
    {
        $scheduleData = [
            1 => [
                [
                    'type' => 'transport',
                    'time' => '09:00',
                    'location' => '滁州高铁站',
                    'duration' => 60,
                    'activities' => '接站服务，前往酒店',
                    'images' => []
                ],
                [
                    'type' => 'accommodation',
                    'time' => '11:00',
                    'location' => '滁州国际大酒店',
                    'duration' => 0,
                    'activities' => '办理入住手续，休息调整',
                    'images' => []
                ],
                [
                    'type' => 'attraction',
                    'time' => '14:00',
                    'location' => '琅琊山风景区',
                    'duration' => 180,
                    'activities' => '游览琅琊山，欣赏自然风光',
                    'images' => []
                ],
                [
                    'type' => 'meal',
                    'time' => '18:00',
                    'location' => '滁州特色餐厅',
                    'duration' => 90,
                    'activities' => '品尝滁州特色美食',
                    'images' => []
                ]
            ],
            2 => [
                [
                    'type' => 'meal',
                    'time' => '08:00',
                    'location' => '酒店餐厅',
                    'duration' => 60,
                    'activities' => '享用丰盛早餐',
                    'images' => []
                ],
                [
                    'type' => 'attraction',
                    'time' => '09:30',
                    'location' => '醉翁亭',
                    'duration' => 120,
                    'activities' => '参观醉翁亭，了解欧阳修文化',
                    'images' => []
                ],
                [
                    'type' => 'freeActivity',
                    'time' => '14:00',
                    'location' => '滁州古城',
                    'duration' => 180,
                    'activities' => '自由活动，购买特产',
                    'images' => []
                ]
            ],
            3 => [
                [
                    'type' => 'meal',
                    'time' => '08:00',
                    'location' => '酒店餐厅',
                    'duration' => 60,
                    'activities' => '享用早餐',
                    'images' => []
                ],
                [
                    'type' => 'transport',
                    'time' => '10:00',
                    'location' => '滁州高铁站',
                    'duration' => 60,
                    'activities' => '送站服务，结束愉快旅程',
                    'images' => []
                ]
            ]
        ];

        return $scheduleData[$day] ?? [];
    }
}
