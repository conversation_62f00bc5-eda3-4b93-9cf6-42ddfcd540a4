<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder()">
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="shopping" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            <span>订单</span>
          </div>
          <count-to :start-val="0" :end-val="all" :duration="1" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder()">
        <div class="card-panel-icon-wrapper icon-money3">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            订单总额
          </div>
          <count-to :start-val="0" :end-val="total" :duration="10" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({status:0})">
        <div class="card-panel-icon-wrapper icon-message2">
          <svg-icon icon-class="message" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            <span to="/order/index?">待跟进</span>
          </div>
          <count-to :start-val="0" :end-val="wait" :duration="1" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({status:1})">
        <div class="card-panel-icon-wrapper icon-message">
          <svg-icon icon-class="message" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            <span to="/order/index?status=1">跟进中</span>
          </div>
          <count-to :start-val="0" :end-val="doing" :duration="1" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({os_status:[4,2]})">
        <div class="card-panel-icon-wrapper icon-skill">
          <svg-icon icon-class="skill" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            核销订单数
          </div>
          <count-to :start-val="0" :end-val="asset" :duration="1" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({os_status:[4,2]})">
        <div class="card-panel-icon-wrapper icon-money2">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            核销订单金额
          </div>
          <count-to :start-val="0" :end-val="asset_price" :duration="10" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({os_status:[4,3]})">
        <div class="card-panel-icon-wrapper icon-documentation">
          <svg-icon icon-class="documentation" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            退款订单数
          </div>
          <count-to :start-val="0" :end-val="refund" :duration="10" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({os_status:[4,3]})">
        <div class="card-panel-icon-wrapper icon-money">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            退款订单金额
          </div>
          <count-to :start-val="0" :end-val="refund_price" :duration="10" class="card-panel-num" />
        </div>
      </div>
    </el-col>
<!--    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({os_status:[4,3],times:[handleGetStartTime(30),handleEndTime()]})">
        <div class="card-panel-icon-wrapper icon-camellia">
          <svg-icon icon-class="documentation" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            待使用订单数30天
          </div>
          <count-to :start-val="0" :end-val="tobeused_30" :duration="10" class="card-panel-num" />
        </div>
      </div>
    </el-col>-->
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({os_status:[4,1],times:[handleGetStartTime(30),handleEndTime()]})">
        <div class="card-panel-icon-wrapper icon-camellia">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            待使用订单数30天 <count-to :start-val="0" :end-val="tobeused_30" :duration="10" class="card-panel-num" style="color: #ea517f" />
          </div>
          <div class="card-panel-text">
            待使用订单金额30天 <count-to :start-val="0" :end-val="tobeused_price_30" :duration="10" class="card-panel-num" style="color: #ea517f" />
          </div>
        </div>
      </div>
    </el-col>
<!--    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({os_status:[4,3],times:[handleGetStartTime(60),handleEndTime()]})">
        <div class="card-panel-icon-wrapper icon-camellia-sixty">
          <svg-icon icon-class="documentation" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            待使用订单数60天
          </div>
          <count-to :start-val="0" :end-val="tobeused_60" :duration="10" class="card-panel-num" />
        </div>
      </div>
    </el-col>-->
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({os_status:[4,1],times:[handleGetStartTime(60),handleEndTime()]})">
        <div class="card-panel-icon-wrapper icon-camellia-sixty">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            待使用订单数60天 <count-to :start-val="0" :end-val="tobeused_60" :duration="10" class="card-panel-num" style="color: #ea517f" />
          </div>
          <div class="card-panel-text">
            待使用订单金额60天 <count-to :start-val="0" :end-val="tobeused_price_60" :duration="10" class="card-panel-num" style="color: #ea517f" />
          </div>
        </div>
      </div>
    </el-col>
<!--    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({os_status:[4,3],times:[handleGetStartTime(80),handleEndTime()]})">
        <div class="card-panel-icon-wrapper icon-camellia-fourscore">
          <svg-icon icon-class="documentation" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            待使用订单数80天
          </div>
          <count-to :start-val="0" :end-val="tobeused_80" :duration="10" class="card-panel-num" />
        </div>
      </div>
    </el-col>-->
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleOrder({os_status:[4,1],times:[handleGetStartTime(80),handleEndTime()]})">
        <div class="card-panel-icon-wrapper icon-camellia-fourscore">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            待使用订单数80天 <count-to :start-val="0" :end-val="tobeused_80" :duration="10" class="card-panel-num" style="color: #ea517f" />
          </div>
          <div class="card-panel-text">
            待使用订单金额80天 <count-to :start-val="0" :end-val="tobeused_price_80" :duration="10" class="card-panel-num" style="color: #ea517f" />
          </div>

        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CountTo from 'vue-count-to'

export default {
  components: {
    CountTo
  },
  data(){
    return {
      wait:0,
      doing:0,
      all:0,
      total:0,
      asset:0,
      asset_price:0,
      refund:0,
      refund_price:0,
      tobeused_30: 0,
      tobeused_60: 0,
      tobeused_80: 0,
      tobeused_price_30: 0,
      tobeused_price_60: 0,
      tobeused_price_80: 0
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$axios.get('/admin/index/orders').then(res=>{
        this.wait = parseFloat(res.data.wait)
        this.doing = parseFloat(res.data.doing)
        this.all = parseFloat(res.data.all)
        this.total = parseFloat(res.data.total)/100
        this.asset = parseFloat(res.data.asset)
        this.asset_price = parseFloat(res.data.asset_price)/100
        this.refund = parseFloat(res.data.refund)
        this.refund_price = parseFloat(res.data.refund_price)/100
        this.tobeused_30 = parseFloat(res.data.tobeused_30)
        this.tobeused_price_30 = parseFloat(res.data.tobeused_price_30)/100
        this.tobeused_60 = parseFloat(res.data.tobeused_60)
        this.tobeused_price_60 = parseFloat(res.data.tobeused_price_60)/100
        this.tobeused_80 = parseFloat(res.data.tobeused_80)
        this.tobeused_price_80 = parseFloat(res.data.tobeused_price_80)/100
      }).catch(err=>{
        console.log(err)
      })
    })
  },
  methods: {
    //获取n天的时间戳
    handleGetStartTime(i){
      return new Date(new Date().setHours(0, 0, 0)  - 1000 * 3600 * 24 *i).getTime()
    },
    //获取当天结束时间的时间戳
    handleEndTime(){
      return new Date(new Date().setHours(23,59,59,999)).getTime()
    },
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    },
    handleOrder(val){
      this.$router.push({
        path: "/order/index",
        query: val
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }
      .icon-camellia{
        background: #c45a65;
      }
      .icon-camellia-sixty{
        background: #ea517f;
      }
      .icon-camellia-fourscore{
        background: #d2357d;
      }
      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-message2 {
        background: #656768;
      }

      .icon-skill {
        background: #7fd554;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-money2 {
        background: #7fd554;
      }

      .icon-money3 {
        background: #34bfa3;
      }

      .icon-shopping {
        background: #34bfa3
      }
      .icon-documentation {
        background: #f4516c
      }

      .icon-documentation2 {
        background: #d972e4
      }
    }

    .icon-people {
      color: #40c9c6;
    }
    .icon-camellia-sixty{
        color: #ea517f;
      }
      .icon-camellia-fourscore{
        color: #d2357d;
      }
    .icon-camellia{
        color: #c45a65;
      }
    .icon-message {
      color: #36a3f7;
    }

    .icon-money2 {
      color: #7fd554;
    }

    .icon-money3 {
      color: #34bfa3;
    }

    .icon-skill {
      color: #7fd554;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }
    .icon-documentation {
      color: #f4516c
    }

    .icon-documentation2 {
      background: #d972e4
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
