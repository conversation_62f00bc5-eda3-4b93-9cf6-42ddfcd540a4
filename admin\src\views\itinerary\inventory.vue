<template>
  <div class="app-container">
    <div class="inventory-header">
      <h2>行程库存管理</h2>
      <div class="header-info">
        <span class="itinerary-title">{{ itineraryInfo.title }}</span>
        <el-button @click="handleBack">返回列表</el-button>
      </div>
    </div>

    <!-- 操作工具栏 - 只保留批量设置功能 -->
    <div class="toolbar">
      <div class="date-picker-container">
        <el-date-picker
          v-model="currentMonth"
          type="month"
          placeholder="选择月份"
          @change="handleMonthChange"
          style="width: 200px; margin-right: 10px;"
        />
      </div>
      <div class="batch-operations">
        <el-button type="primary" @click="showBatchDialog = true">
          批量设置
        </el-button>
      </div>
    </div>

    <!-- 日历视图 -->
    <div class="calendar-container">
      <div class="calendar-header">
        <div class="month-navigation">
          <el-button icon="el-icon-arrow-left" circle @click="previousMonth"></el-button>
          <span class="month-title">{{ formatMonthTitle() }}</span>
          <el-button icon="el-icon-arrow-right" circle @click="nextMonth"></el-button>
        </div>
      </div>
      
      <div class="week-header">
        <div v-for="day in weekDays" :key="day" class="week-day">{{ day }}</div>
      </div>

      <div class="calendar-grid">
        <!-- 日历网格 -->
        <div v-for="week in calendarData" :key="week[0].dateStr" class="calendar-week">
          <template v-for="dayItem in week">
            <!-- 有数据的日期单元格 -->
            <div 
              v-if="dayItem.hasData" 
              :key="dayItem.dateStr"
              class="calendar-day" 
              :class="getDayClass(dayItem.date)" 
              @click="handleDateClick(dayItem.date)"
            >
              <div class="day-number">{{ dayItem.day }}</div>
              <div class="day-content">
                <!-- 预约状态 -->
                <div class="booking-status" :class="getStatusClass(dayItem.date)">
                  {{ getStatusText(dayItem.date) }}
                </div>

                <!-- 预约数量信息 -->
                <div class="booking-info">
                  <div class="booked-info">已约 {{ getInventoryData(dayItem.date).booked || 0 }}</div>
                  <div class="available-info">剩 {{ getAvailableCount(dayItem.date) }}</div>
                </div>

                <!-- 加价信息 -->
                <div class="surcharge-info" v-if="hasSurcharge(dayItem.date)">
                  <!-- 热门日期加价 -->
                  <div class="hot-date-surcharge" v-if="getInventoryData(dayItem.date).hotDateSurcharge > 0">
                    热门日期: +{{ getInventoryData(dayItem.date).hotDateSurcharge }}元
                  </div>

                  <!-- 成人加价 -->
                  <div class="adult-surcharge" v-if="getInventoryData(dayItem.date).adultSurcharge > 0">
                    成人携带成人: +{{ getInventoryData(dayItem.date).adultSurcharge }}元/人
                  </div>

                  <!-- 儿童加价 -->
                  <div class="child-surcharge" v-if="getInventoryData(dayItem.date).childSurcharge > 0">
                    成人携带儿童: +{{ getInventoryData(dayItem.date).childSurcharge }}元/人
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 无数据日期的空占位符 -->
            <div 
              v-else 
              :key="dayItem.dateStr"
              class="calendar-day empty-day"
            >
              <div class="day-number light">{{ dayItem.day }}</div>
              <div class="no-data-info">无排期</div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 批量设置对话框 -->
    <el-dialog
      title="批量修改预约库存"
      :visible.sync="showBatchDialog"
      width="800px"
      class="batch-dialog"
    >
      <div class="batch-form">
        <!-- 商品信息 -->
        <div class="product-info">
          <div class="info-item">
            <span class="label">商品名称：</span>
            <span class="value">{{ itineraryInfo.title }}</span>
          </div>
          <div class="info-item">
            <span class="label">商品ID：</span>
            <span class="value">{{ itineraryId }}</span>
          </div>
        </div>

        <el-form :model="batchForm" label-width="100px">
          <!-- 时间范围 -->
          <div class="form-section">
            <h4>时间范围</h4>
            <el-form-item>
              <div class="date-range-container">
                <el-date-picker
                  v-model="batchForm.dateRanges.startDate"
                  type="date"
                  placeholder="开始日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 140px; margin-right: 10px;"
                />
                <span style="margin: 0 10px;">—</span>
                <el-date-picker
                  v-model="batchForm.dateRanges.endDate"
                  type="date"
                  placeholder="结束日期"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  style="width: 140px;"
                />
              </div>
            </el-form-item>
          </div>

          <!-- 适用时间 -->
          <div class="form-section">
            <h4>适用时间</h4>
            <el-form-item>
              <el-checkbox v-model="batchForm.allDays" @change="handleAllDaysChange">全选</el-checkbox>
              <div class="days-selection">
                <el-checkbox-group v-model="batchForm.selectedDays">
                  <el-checkbox label="MON">周一</el-checkbox>
                  <el-checkbox label="TUE">周二</el-checkbox>
                  <el-checkbox label="WED">周三</el-checkbox>
                  <el-checkbox label="THU">周四</el-checkbox>
                  <el-checkbox label="FRI">周五</el-checkbox>
                  <el-checkbox label="SAT">周六</el-checkbox>
                  <el-checkbox label="SUN">周日</el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
          </div>

          <!-- 库存状态和数量 -->
          <div class="form-section">
            <h4>库存状态和数量</h4>
            <el-form-item label="库存状态：">
              <el-radio-group v-model="batchForm.status">
                <el-radio :label="1">可预约</el-radio>
                <el-radio :label="0">不可预约</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="库存数量：">
              <el-radio-group v-model="batchForm.isUnlimited">
                <el-radio :label="0">不限制</el-radio>
                <el-radio :label="1">限制</el-radio>
              </el-radio-group>
              <el-input-number
                v-if="batchForm.isUnlimited === 1"
                v-model="batchForm.stock"
                :min="0"
                :max="9999"
                controls-position="right"
                style="width: 30%"
              />
            </el-form-item>
          </div>

          <!-- 热门日期加价 -->
          <div class="form-section">
            <h4>热门日期加价</h4>
            <el-form-item label="是否需要加价：">
              <el-radio-group v-model="batchForm.needSurcharge">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="加价金额：" v-if="batchForm.needSurcharge">
              <el-input-number
                v-model="batchForm.surcharges.adult"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 120px; margin-right: 10px;"
              />
              <span>元</span>
            </el-form-item>
          </div>

          <!-- 增加出行人加价 -->
          <div class="form-section">
            <h4>增加出行人加价</h4>
            <el-form-item label="是否需要加价：">
              <el-radio-group v-model="batchForm.needPersonSurcharge">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="batchForm.needPersonSurcharge">
              <el-form-item label="成人加价：">
                <el-input-number
                  v-model="batchForm.surcharges.adult"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  style="width: 120px; margin-right: 10px;"
                />
                <span>元/人</span>
              </el-form-item>
              <el-form-item label="儿童加价：">
                <el-input-number
                  v-model="batchForm.surcharges.child"
                  :min="0"
                  :precision="2"
                  controls-position="right"
                  style="width: 120px; margin-right: 10px;"
                />
                <span>元/人</span>
              </el-form-item>
            </div>
            <div class="note">
              设置不同类型出行人的额外加收费用
            </div>
          </div>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showBatchDialog = false">取消</el-button>
        <el-button type="primary" @click="handleBatchSet">确定</el-button>
      </div>
    </el-dialog>

    <!-- 日期编辑对话框 -->
    <el-dialog
      title="编辑库存信息"
      :visible.sync="showEditDialog"
      width="500px"
    >
      <el-form :model="editForm" label-width="120px" v-if="editForm">
        <el-form-item label="日期">
          <span>{{ editForm.date }}</span>
        </el-form-item>

        <el-form-item label="预约状态">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="0">不可预约</el-radio>
            <el-radio :label="1">可预约</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="总库存">
          <el-radio-group v-model="editForm.isUnlimited">
            <el-radio :label="1">限制</el-radio>
            <el-radio :label="0">不限制</el-radio>
          </el-radio-group>
          <el-input-number
            v-if="editForm.isUnlimited === 1"
            v-model="editForm.stock"
            :min="0"
            :max="9999"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="已预约数量">
          <el-input-number
            v-model="editForm.booked"
            :min="0"
            :max="editForm.stock"
            controls-position="right"
            style="width: 100%"
            disabled
          />
        </el-form-item>

        <!-- 修改热门日期加价部分，添加开关 -->
        <el-form-item label="热门日期加价">
          <el-switch
            v-model="editForm.enableHotDateSurcharge"
            active-text="开启"
            inactive-text="关闭"
            @change="handleHotDateSurchargeChange"
          ></el-switch>
          <div v-if="editForm.enableHotDateSurcharge" style="margin-top: 10px;">
            <el-input-number
              v-model="editForm.hotDateSurcharge"
              :min="1"
              :precision="0"
              controls-position="right"
              style="width: 80%"
            />
            <span style="margin-left: 10px;">元</span>
          </div>
        </el-form-item>

        <!-- 修改增加出行人加价部分，添加开关 -->
        <el-form-item label="增加出行人加价">
          <el-switch
            v-model="editForm.enablePersonSurcharge"
            active-text="开启"
            inactive-text="关闭"
            @change="handlePersonSurchargeChange"
          ></el-switch>
        </el-form-item>
        
        <!-- 只有当启用出行人加价时才显示这两个选项 -->
        <div v-if="editForm.enablePersonSurcharge">
          <el-form-item label="成人加价">
            <el-input-number
              v-model="editForm.adultSurcharge"
              :min="0"
              :precision="0"
              controls-position="right"
              style="width: 80%"
            />
            <span style="margin-left: 10px;">元/人</span>
          </el-form-item>

          <el-form-item label="儿童加价">
            <el-input-number
              v-model="editForm.childSurcharge"
              :min="0"
              :precision="0"
              controls-position="right"
              style="width: 80%"
            />
            <span style="margin-left: 10px;">元/人</span>
          </el-form-item>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" :loading="saving" @click="handleEditSave">保存</el-button>
      </div>
    </el-dialog>

    <!-- 图例说明 -->
    <div class="legend">
      <div class="legend-item">
        <div class="legend-color available"></div>
        <span>可预约</span>
      </div>
      <div class="legend-item">
        <div class="legend-color unavailable"></div>
        <span>不可预约</span>
      </div>
      <div class="legend-item">
        <div class="legend-color past"></div>
        <span>过期日期</span>
      </div>
      <div class="legend-item">
        <div class="legend-color hot-date"></div>
        <span>热门日期加价</span>
      </div>
      <div class="legend-item">
        <div class="legend-color adult-surcharge"></div>
        <span>成人加价</span>
      </div>
      <div class="legend-item">
        <div class="legend-color child-surcharge"></div>
        <span>成人携带儿童</span>
      </div>
      <div class="legend-item">
        <div class="legend-color empty"></div>
        <span>无排期</span>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getItineraryDetailApi,
  getItineraryInventoryMonthApi,
  batchUpdateItineraryInventoryApi,
  updateItineraryInventorySingleApi 
} from "@/api/admin";
import request from "@/utils/request"; // 导入request函数以确保正确处理认证和错误

export default {
  name: "ItineraryInventory",
  data() {
    return {
      itineraryId: null,
      itineraryInfo: {},
      currentDate: new Date(),
      currentMonth: new Date(),
      inventoryData: {},
      originalData: {},
      saving: false,
      showBatchDialog: false,
      showEditDialog: false,
      editForm: {
        date: '',
        status: 1,
        stock: 0,
        isUnlimited: 1,
        booked: 0,
        hotDateSurcharge: 0,
        adultSurcharge: 0,
        childSurcharge: 0,
        enableHotDateSurcharge: false,
        enablePersonSurcharge: false
      },
      weekDays: ['一', '二', '三', '四', '五', '六', '日'],
      batchForm: {
        dateRanges: {
          startDate: '',
          endDate: ''
        },
        selectedDays: ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'],
        allDays: true,
        status: 1,
        stockType: 'unlimited',
        stockAmount: 0,
        needSurcharge: false,
        needPersonSurcharge: false,
        surcharges: {
          adult: 0,
          child: 0
        }
      }
    };
  },
  computed: {
    calendarData() {
      const month = this.currentMonth.getMonth();
      const year = this.currentMonth.getFullYear();
      
      // 获取当月第一天
      const firstDay = new Date(year, month, 1);
      // 获取当月最后一天
      const lastDay = new Date(year, month + 1, 0);
      
      // 计算第一天是周几（0是周日，1是周一...）
      let firstDayOfWeek = firstDay.getDay();
      if (firstDayOfWeek === 0) firstDayOfWeek = 7; // 将周日从0改为7
      
      const daysInMonth = lastDay.getDate();
      
      // 创建日历网格
      const weeks = [];
      let week = [];
      
      // 填充第一周前的空白
      for (let i = 1; i < firstDayOfWeek; i++) {
        week.push({
          day: '',
          date: null,
          dateStr: '',
          hasData: false
        });
      }
      
      // 填充日期
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month, day);
        const dateStr = this.formatDate(date);
        
        // 检查API是否返回了此日期的数据
        const hasData = this.inventoryData[dateStr] !== undefined;
        
        week.push({
          day,
          date,
          dateStr,
          hasData
        });
        
        // 如果周日或月底，开始新一周
        if (week.length === 7 || day === daysInMonth) {
          // 填充最后一周的空白
          while (week.length < 7) {
            week.push({
              day: '',
              date: null,
              dateStr: '',
              hasData: false
            });
          }
          
          weeks.push(week);
          week = [];
        }
      }
      
      return weeks;
    }
  },
  created() {
    this.itineraryId = this.$route.params.id;

    if (!this.itineraryId) {
      this.$message.error('未获取到行程ID');
      this.$router.push('/itinerary');
      return;
    }

    this.initData();
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.loadItineraryInfo();
      await this.loadInventoryData();
    },

    // 加载行程信息
    async loadItineraryInfo() {
      try {
        const response = await getItineraryDetailApi(this.itineraryId);
        this.itineraryInfo = response.data;
      } catch (error) {
        console.error('获取行程信息失败:', error);
      }
    },

    // 加载库存数据
    async loadInventoryData() {
      try {
        const params = {
          itineraryId: this.itineraryId,
          month: `${this.currentMonth.getFullYear()}-${String(this.currentMonth.getMonth() + 1).padStart(2, '0')}`
        };

        const response = await getItineraryInventoryMonthApi(params);

        // 处理API返回的数据，转换为前端需要的格式
        const apiData = response.data?.items || response.data || {};
        this.inventoryData = this.processApiData(apiData);

        this.originalData = JSON.parse(JSON.stringify(this.inventoryData));
      } catch (error) {
        console.error('获取库存数据失败:', error);

        // 如果API失败，初始化空数据
        this.inventoryData = {};
        this.$message.error('获取库存数据失败，请检查网络连接或联系管理员');
      }
    },

    // 处理API数据，转换为前端显示格式
    processApiData(apiData) {
      const processedData = {};

      // 检查apiData是否是数组格式（后端返回的格式）
      if (Array.isArray(apiData)) {
        apiData.forEach(item => {
          const dateStr = item.date;
          const isUnlimited = item.total === '不限';
          processedData[dateStr] = {
            status: item.status !== undefined ? item.status : 1, // 默认可预约
            stock: isUnlimited ? '不限' : (parseInt(item.total) || 50),
            isUnlimited: isUnlimited,
            booked: parseInt(item.booked) || 0,
            hotDateSurcharge: parseInt(item.hotDateSurcharge) || 0,
            adultSurcharge: parseInt(item.adultSurcharge) || 0,
            childSurcharge: parseInt(item.childSurcharge) || 0,
            price: item.price || this.itineraryInfo.price || 0
          };
        });
      } else if (typeof apiData === 'object' && apiData !== null) {
        // 如果是对象格式，按原来的逻辑处理
        Object.keys(apiData).forEach(dateStr => {
          const item = apiData[dateStr];
          const isUnlimited = item.totalAmount === null || item.total === '不限' || item.stock === null;
          processedData[dateStr] = {
            status: item.status || 1, // 默认可预约
            stock: isUnlimited ? '不限' : (parseInt(item.totalAmount) || parseInt(item.total) || parseInt(item.stock) || 50),
            isUnlimited: isUnlimited,
            booked: parseInt(item.bookedAmount) || parseInt(item.booked) || 0,
            hotDateSurcharge: parseInt(item.surcharges?.hotDate) || parseInt(item.hotDateSurcharge) || 0,
            adultSurcharge: parseInt(item.surcharges?.adult) || parseInt(item.adultSurcharge) || 0,
            childSurcharge: parseInt(item.surcharges?.child) || parseInt(item.childSurcharge) || 0,
            price: item.price || this.itineraryInfo.price || 0
          };
        });
      }

      return processedData;
    },

    // 月份变化处理
    handleMonthChange() {
      this.loadInventoryData();
    },

    // 上一个月
    previousMonth() {
      const newDate = new Date(this.currentMonth);
      newDate.setMonth(newDate.getMonth() - 1);
      this.currentMonth = newDate;
      this.loadInventoryData();
    },

    // 下一个月
    nextMonth() {
      const newDate = new Date(this.currentMonth);
      newDate.setMonth(newDate.getMonth() + 1);
      this.currentMonth = newDate;
      this.loadInventoryData();
    },

    // 格式化月份标题
    formatMonthTitle() {
      const year = this.currentMonth.getFullYear();
      const month = this.currentMonth.getMonth() + 1;
      return `${year}年${month}月`;
    },

    // 获取日期的库存数据
    getInventoryData(date) {
      if (!date) return this.createDefaultInventoryData();
      
      const dateStr = this.formatDate(date);
      if (!this.inventoryData[dateStr]) {
        // 不再为未返回的日期创建默认数据
        return this.createDefaultInventoryData();
      }
      return this.inventoryData[dateStr];
    },
    
    // 创建默认库存数据
    createDefaultInventoryData() {
      return {
        status: 1,
        stock: 0,
        isUnlimited: 0,
        booked: 0,
        hotDateSurcharge: 0,
        adultSurcharge: 0,
        childSurcharge: 0,
        price: this.itineraryInfo.price || 0
      };
    },

    // 获取预约状态文本
    getStatusText(date) {
      const inventory = this.getInventoryData(date);
      return inventory.status === 0 ? '不可预约' : '可预约';
    },

    // 获取状态样式类
    getStatusClass(date) {
      const inventory = this.getInventoryData(date);
      return inventory.status === 0 ? 'status-unavailable' : 'status-available';
    },

    // 获取可预约数量
    getAvailableCount(date) {
      const inventory = this.getInventoryData(date);
      return inventory.isUnlimited ? '不限' : Math.max(0, inventory.stock - inventory.booked);
    },

    // 是否有加价信息
    hasSurcharge(date) {
      const inventory = this.getInventoryData(date);
      return inventory.hotDateSurcharge > 0 ||
             inventory.adultSurcharge > 0 ||
             inventory.childSurcharge > 0;
    },
    
    // 处理日期点击
    handleDateClick(date) {
      if (!date) return;
      
      const dateStr = this.formatDate(date);
      const inventory = this.getInventoryData(date);

      // 检查是否是过去的日期
      const today = new Date();
      const currentDate = new Date(date);
      if (currentDate < today) {
        this.$message.warning('不能编辑过去的日期');
        return;
      }

      // 设置编辑表单数据
      this.editForm = {
        date: dateStr,
        status: inventory.status,
        stock: inventory.stock,
        isUnlimited: inventory.isUnlimited,
        booked: inventory.booked,
        hotDateSurcharge: inventory.hotDateSurcharge,
        adultSurcharge: inventory.adultSurcharge,
        childSurcharge: inventory.childSurcharge,
        // 根据价格值设置开关状态
        enableHotDateSurcharge: inventory.hotDateSurcharge > 0,
        enablePersonSurcharge: inventory.adultSurcharge > 0 || inventory.childSurcharge > 0
      };

      this.showEditDialog = true;
    },
    
    // 获取日期样式类
    getDayClass(date) {
      if (!date) return '';
      
      const today = new Date();
      const currentDate = new Date(date);
      const isPast = currentDate < today;
      const inventory = this.getInventoryData(date);

      if (isPast) {
        return 'past-date';
      }

      return inventory.status === 0 ? 'unavailable-date' : 'available-date';
    },

    // 处理热门日期加价开关变化
    handleHotDateSurchargeChange(value) {
      if (value) {
        // 如果启用热门日期加价，设置一个默认值
        this.editForm.hotDateSurcharge = this.editForm.hotDateSurcharge > 0 ? this.editForm.hotDateSurcharge : 1;
      } else {
        // 如果禁用热门日期加价，设置为0
        this.editForm.hotDateSurcharge = 0;
      }
    },
    
    // 处理出行人加价开关变化
    handlePersonSurchargeChange(value) {
      if (!value) {
        // 如果禁用出行人加价，将成人加价和儿童加价都设置为0
        this.editForm.adultSurcharge = 0;
        this.editForm.childSurcharge = 0;
      } else {
        // 如果启用出行人加价，可以设置一个默认值
        if (this.editForm.adultSurcharge === 0 && this.editForm.childSurcharge === 0) {
          this.editForm.adultSurcharge = 0;
          this.editForm.childSurcharge = 0;
        }
      }
    },

    // 修改后的保存编辑方法 - 使用新的API接口
    async handleEditSave() {
      if (!this.editForm) return;
      
      this.saving = true;
      
      try {
        const dateStr = this.editForm.date;
        
        // 准备API需要的数据格式
        const apiData = {
          itineraryId: this.itineraryId,
          date: dateStr,
          status: this.editForm.status,
          limit: this.editForm.isUnlimited === 0 ? null : this.editForm.stock,
          surcharges: {
            hotDate: this.editForm.enableHotDateSurcharge ? this.editForm.hotDateSurcharge : 0,
            adult: this.editForm.enablePersonSurcharge ? this.editForm.adultSurcharge : 0,
            child: this.editForm.enablePersonSurcharge ? this.editForm.childSurcharge : 0
          }
        };
        
        // 调用API保存单个日期数据
        await updateItineraryInventorySingleApi(apiData);
        
        // 更新本地数据
        this.inventoryData[dateStr] = {
          status: this.editForm.status,
          stock: this.editForm.stock,
          isUnlimited: this.editForm.isUnlimited,
          booked: this.editForm.booked,
          hotDateSurcharge: this.editForm.enableHotDateSurcharge ? this.editForm.hotDateSurcharge : 0,
          adultSurcharge: this.editForm.enablePersonSurcharge ? this.editForm.adultSurcharge : 0,
          childSurcharge: this.editForm.enablePersonSurcharge ? this.editForm.childSurcharge : 0,
          price: this.inventoryData[dateStr]?.price || this.itineraryInfo.price || 0
        };
        
        // 关闭编辑对话框
        this.showEditDialog = false;
        
        console.log('库存信息已保存', this.inventoryData);
        this.$message.success('库存信息已保存');
      } catch (error) {
        // 错误处理优化，防止登出
        console.error('保存库存信息失败:', error);
        
        // 检查是否是401错误（未授权，可能导致登出）
        if (error.response && error.response.status === 401) {
          this.$message.error('登录状态已过期，请重新登录');
          // 此处可以添加重定向到登录页面的逻辑
        } else {
          // 其他错误不应导致登出
          this.$message.error('保存库存信息失败: ' + (error.message || '请检查接口参数'));
        }
      } finally {
        this.saving = false;
      }
    },

    // 批量设置方法保持不变
    async handleBatchSet() {
      // 验证表单
      if (!this.batchForm.dateRanges.startDate || !this.batchForm.dateRanges.endDate) {
        this.$message.warning('请选择日期范围');
        return;
      }

      if (this.batchForm.selectedDays.length === 0) {
        this.$message.warning('请选择适用时间');
        return;
      }

      try {
        // 准备API参数
        const apiData = {
          itineraryId: this.itineraryId,
          dateRanges: {
            startDate: this.batchForm.dateRanges.startDate,
            endDate: this.batchForm.dateRanges.endDate
          },
          status: this.batchForm.status,
          limit: this.batchForm.isUnlimited === 0 ? null : this.batchForm.stock,
          selectedDays: this.batchForm.selectedDays,
          surcharges: {
            hotDate: this.batchForm.needSurcharge ? this.batchForm.surcharges.hotDate : 0,
            adult: this.batchForm.needPersonSurcharge ? this.batchForm.surcharges.adult : 0,
            child: this.batchForm.needPersonSurcharge ? this.batchForm.surcharges.child : 0
          }
        };

        // 调用批量更新API
        await this.handleBatchUpdate(apiData);

        this.showBatchDialog = false;
        this.$message.success('批量设置成功');
      } catch (error) {
        console.error('批量设置失败:', error);
        this.$message.error('批量设置失败');
      }
    },

    // 批量更新库存（使用API）
    async handleBatchUpdate(updateData) {
      try {
        const data = {
          itineraryId: this.itineraryId,
          ...updateData
        };

        await batchUpdateItineraryInventoryApi(data);
        this.$message.success('批量更新成功');
        
        // 重新加载当前月份的数据
        await this.loadInventoryData();
      } catch (error) {
        console.error('批量更新失败:', error);
        this.$message.error('批量更新失败');
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 返回列表
    handleBack() {
      this.$router.push('/itinerary');
    },

    // 全选天数变化处理
    handleAllDaysChange(value) {
      if (value) {
        this.batchForm.selectedDays = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];
      } else {
        this.batchForm.selectedDays = [];
      }
    }
  }
};
</script>

<style scoped>
/* CSS 样式保持不变 */
.app-container {
  padding: 20px;
}

.inventory-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.inventory-header h2 {
  margin: 0;
  color: #303133;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.itinerary-title {
  font-size: 16px;
  color: #606266;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.batch-operations {
  display: flex;
  gap: 10px;
}

.calendar-container {
  margin-bottom: 20px;
}

/* 新增自定义日历样式 */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.month-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
}

.month-title {
  font-size: 18px;
  font-weight: 500;
}

.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background-color: #f5f7fa;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}

.week-day {
  padding: 12px;
  text-align: center;
  font-weight: 500;
}

.calendar-grid {
  border-left: 1px solid #ebeef5;
  border-top: 1px solid #ebeef5;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.empty-day {
  background-color: #f9f9f9;
  color: #c0c4cc;
}

.light {
  color: #c0c4cc;
}

.no-data-info {
  margin-top: 10px;
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.calendar-day {
  height: 140px;
  padding: 8px;
  border-right: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  font-size: 11px;
  line-height: 1.2;
  transition: all 0.2s ease;
}

.calendar-day:not(.empty-day) {
  cursor: pointer;
}

.calendar-day:not(.empty-day):hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.calendar-day.past-date {
  background-color: #f5f5f5;
  color: #c0c4cc;
}

.calendar-day.available-date {
  background-color: #f0f9ff;
  border: 1px solid #409eff;
}

.calendar-day.unavailable-date {
  background-color: #fef0f0;
  border: 1px solid #f56c6c;
}

.calendar-day.full-date {
  background-color: #fff7e6;
  border: 1px solid #faad14;
}

.day-number {
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 14px;
}

.day-content {
  font-size: 11px;
}

/* 预约状态 */
.booking-status {
  font-weight: 500;
  margin-bottom: 3px;
  padding: 1px 4px;
  border-radius: 2px;
  text-align: center;
  font-size: 10px;
}

.booking-status.status-available {
  background-color: #52c41a;
  color: white;
}

.booking-status.status-unavailable {
  background-color: #ff4d4f;
  color: white;
}

.booking-status.status-full {
  background-color: #faad14;
  color: white;
}

/* 预约数量信息 */
.booking-info {
  margin-bottom: 4px;
}

.booked-info,
.available-info {
  font-size: 10px;
  margin-bottom: 1px;
}

.booked-info {
  color: #666;
}

.available-info {
  color: #52c41a;
  font-weight: 500;
}

/* 加价信息 */
.surcharge-info {
  font-size: 9px;
  line-height: 1.1;
}

.hot-date-surcharge {
  color: #ff4d4f;
  margin-bottom: 1px;
}

.adult-surcharge {
  color: #1890ff;
  margin-bottom: 1px;
}

.child-surcharge {
  color: #52c41a;
  margin-bottom: 1px;
}

.legend {
  display: flex;
  gap: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.legend-color.available {
  background-color: #409eff;
}

.legend-color.unavailable {
  background-color: #f56c6c;
}

.legend-color.past {
  background-color: #c0c4cc;
}

.legend-color.full {
  background-color: #faad14;
}

.legend-color.hot-date {
  background-color: #ff4d4f;
}

.legend-color.adult-surcharge {
  background-color: #1890ff;
}

.legend-color.child-surcharge {
  background-color: #52c41a;
}

/* 添加无排期图例样式 */
.legend-color.empty {
  background-color: #f9f9f9;
  border: 1px solid #e0e0e0;
}

/* 深度选择器修改日历样式 */
::v-deep .el-calendar-table .el-calendar-day {
  height: 160px;
  padding: 0;
}

::v-deep .el-calendar__body {
  padding: 12px;
}

::v-deep .el-calendar-table td {
  border: 1px solid #ebeef5;
}

::v-deep .el-calendar-table .el-calendar-day:hover {
  background-color: #f5f7fa;
}

/* 批量设置对话框样式 */
::v-deep .batch-dialog .el-dialog__body {
  padding: 20px;
}

.batch-form {
  max-height: 600px;
  overflow-y: auto;
}

.product-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.product-info .info-item {
  margin-bottom: 8px;
}

.product-info .label {
  color: #666;
  font-weight: normal;
}

.product-info .value {
  color: #333;
  font-weight: 500;
}

.form-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.date-range-container {
  display: flex;
  align-items: center;
}

.days-selection {
  margin-top: 10px;
}

.days-selection .el-checkbox {
  margin-right: 15px;
  margin-bottom: 8px;
}

.advance-booking {
  color: #666;
  font-size: 12px;
}

.note {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}
</style>
