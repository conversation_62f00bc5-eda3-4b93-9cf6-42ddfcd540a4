<?php
namespace app\model;

/**
 * 七陌通话记录模型
 */
class QimoCallLogs extends base
{
    protected $table = 'qimo_call_logs';

    // 通话状态常量
    const CALL_STATUS_CALLING = 1;
    const CALL_STATUS_ANSWERED = 2;
    const CALL_STATUS_HANGUP = 3;
    const CALL_STATUS_FAILED = 4;

    const CALL_STATUS_MAP = [
        self::CALL_STATUS_CALLING => '呼叫中',
        self::CALL_STATUS_ANSWERED => '已接听',
        self::CALL_STATUS_HANGUP => '已挂断',
        self::CALL_STATUS_FAILED => '呼叫失败',
    ];

    // 呼叫方向
    const DIRECTION_OUTBOUND = 1; // 外呼
    const DIRECTION_INBOUND = 2;  // 内呼

    public function getCallStatusNameAttr($val)
    {
        return self::CALL_STATUS_MAP[$this->call_status] ?? '未知';
    }

    public function admin()
    {
        return $this->belongsTo(Admins::class, 'admin_id');
    }

    public function agent()
    {
        return $this->belongsTo(QimoAgents::class, 'agent_id');
    }

    /**
     * 创建通话记录
     * @param array $data
     * @return mixed
     */
    public static function createCallLog($data)
    {
        return self::create([
            'admin_id' => $data['admin_id'] ?? 0,
            'agent_id' => $data['agent_id'] ?? 0,
            'caller_number' => $data['caller_number'] ?? '',
            'callee_number' => $data['callee_number'] ?? '',
            'direction' => $data['direction'] ?? self::DIRECTION_OUTBOUND,
            'call_status' => $data['call_status'] ?? self::CALL_STATUS_CALLING,
            'call_id' => $data['call_id'] ?? '',
            'session_id' => $data['session_id'] ?? '',
            'start_time' => $data['start_time'] ?? time(),
            'answer_time' => $data['answer_time'] ?? null,
            'end_time' => $data['end_time'] ?? null,
            'duration' => $data['duration'] ?? 0,
            'record_url' => $data['record_url'] ?? '',
            'event_data' => $data['event_data'] ?? null,
        ]);
    }

    /**
     * 更新通话状态
     * @param string $callId
     * @param array $data
     * @return bool
     */
    public static function updateCallStatus($callId, $data)
    {
        return self::where('call_id', $callId)->update($data);
    }
} 