<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.sn"
        placeholder="订单号"
        style="width: 300px"
        class="filter-item"
      />

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            @click="handle(scope.row, 1)"
          >
            同意
          </el-button>
          <el-button
            type="primary"
            size="small"
            icon="el-icon-check"
            @click="handle(scope.row, 2)"
          >
            拒绝
          </el-button>
        </template>
      </el-table-column>

      <el-table-column align="center" label="订单号" prop="order_id" />
      <el-table-column align="center" label="售后类型" prop="after_sale_type_agg_name" />
      <el-table-column align="center" label="售后原因" prop="after_sale_reason" />
      <el-table-column align="center" label="退款金额" prop="refund_amount" />
      <el-table-column align="center" label="申请时间" :formatter="formatTime" prop="after_sale_apply_time_ms"/>
      <el-table-column align="center" label="到期时间" :formatter="formatTime" prop="audit_expire_time_ms" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
// import Pagination from '@/Wangeditor/Pagination'
import Pagination from "@/components/PaginationFixed";

export default {
  name: "Orderlist",
  components: { Pagination },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
      },
      oss: {},
      item: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.$axios
        .get("/admin/order-xs/dyAfterSaleOrder", { params: this.listQuery })
        .then((response) => {
          this.list = response.data.data;
          this.total = response.data.total;
          this.oss = response.ext;
          this.listLoading = false;
        });
    },
    handle(item, audit_result) {
      this.$axios
        .post("/admin/order-xs/dyAfterSaleOrderHandle", { id: item.id, audit_result:audit_result})
        .then((res) => {
          this.$message({
            message: "处理成功",
            type: "success",
          });
          this.item = {};
          this.getList();
        })
        .catch((err) => {});
    },
    formatTime(row, column, cellValue){
      const date = new Date(cellValue);
      return date.toLocaleString();
    },
  },
};
</script>
<style scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 52px; /* 分页条的高度，以避免内容重叠 */
}
::v-deep .el-image-viewer__close {
  background-color: #fff;
}
</style>
