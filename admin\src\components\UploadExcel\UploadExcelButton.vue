<template>
  <div class="upload-container">
    <el-upload
      :data="dataObj"
      :http-request="handlesSuccess"
      action=""
      class="image-uploader"
      accept=".xls,.xlsx"
      :show-file-list="false"
      >
      <el-button icon="el-icon-plus" type="success">{{ name }}</el-button>
    </el-upload>

    <el-dialog
      title="导入失败数据"
      :visible.sync="errorDialogVisible"
      width="60%">
      <el-table :data="failData">
        <el-table-column property="line" label="失败行"></el-table-column>
        <el-table-column property="productName" label="线路名称"></el-table-column>
        <el-table-column property="mobile" label="手机号"></el-table-column>
        <el-table-column property="wechat" label="微信号"></el-table-column>
        <el-table-column property="remark" label="备注"></el-table-column>
        <el-table-column property="error_msg" label="错误原因"></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="errorDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'

export default {
  name: 'UploadExcelButton',
  props: {
    name: {
      type: String,
      default: '导入线索'
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: '',
      tempUrls: [],
      dataObj: { token: '', key: '' },
      errorDialogVisible: false,
      failData: []
    }
  },
  watch: {

  },
  methods: {
    async handlesSuccess(file) {
      try {
        var formdata = new FormData()
        formdata.append("file", file.file)
        const res = await this.$axios.post("/admin/excel/import", formdata, {
          headers: {
            "Content-type": "multipart/form-data",
            "X-Token": getToken(),
          }
        })
        
        this.$message({
          showClose: true,
          message: res.msg,
        });

        if (res.data && res.data.data && res.data.data.fail_data && res.data.data.fail_data.length > 0) {
          this.failData = res.data.data.fail_data;
          this.errorDialogVisible = true;
        }

        this.$emit('refresh');
      } catch (error) {
        console.error('上传失败:', error)
        file.onError(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
    @import "~@/styles/mixin.scss";
    .upload-container {
        display: inline-block;
        margin-left: 5px;
        margin-bottom:-9px;
        width: 5%;
        position: relative;
        @include clearfix;
        .image-uploader {
            width: 60%;
            float: left;
        }
    }

</style>
