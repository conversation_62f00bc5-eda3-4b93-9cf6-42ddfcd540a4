<template>
  <div class="softphone-container" v-if="visible">
    <div class="softphone-header">
      <div class="status-info">
        <span class="status-text">{{ statusText }}</span>
        <span class="call-time" v-if="statusText === '通话中' && callDuration > 0">{{ formatTime(callDuration) }}</span>
      </div>
      <div class="header-actions">
        <!-- 挂断按钮 - 在通话状态时显示 -->
        <el-button 
          v-if="isInCall || statusText === '呼叫中' || statusText === '通话中'"
          type="danger" 
          size="mini" 
          icon="el-icon-phone-outline"
          @click="hangup"
          title="挂断"
        >
        </el-button>
        <!-- 拨号按钮 - 在非通话状态时显示 -->
        <el-button 
          v-else
          type="primary" 
          size="mini" 
          icon="el-icon-phone"
          @click="toggleCall"
          title="拨号"
        >
        </el-button>
        <el-button 
          size="mini" 
          icon="el-icon-close"
          @click="closePanel"
          title="关闭"
        >
        </el-button>
      </div>
    </div>
    
    <div class="softphone-body">
      <div class="settings-section">
        <div class="setting-title">通话设置</div>
        
        <div class="setting-item">
          <label>接听方式</label>
          <el-select 
            v-model="loginType" 
            size="small" 
            @change="onLoginTypeChange"
            :popper-append-to-body="false"
            popper-class="softphone-select-dropdown"
          >
            <el-option label="手机号" value="Local"></el-option>
            <el-option label="sip话机" value="sip"></el-option>
            <el-option label="webrtc" value="gateway"></el-option>
          </el-select>
        </div>
        
        <div class="setting-item">
          <label>手机号码</label>
          <div class="phone-display">{{ agentPhone }}</div>
        </div>
        
        <div class="setting-item" v-if="!softphoneInstance">
          <label>状态</label>
          <div style="color: #f56c6c; font-size: 12px;">未初始化</div>
        </div>
        
        <div class="setting-item" v-else-if="!softphoneInstance.callApi">
          <label>状态</label>
          <div style="color: #e6a23c; font-size: 12px;">初始化中...</div>
        </div>
        
        <div class="setting-item" v-else>
          <label>状态</label>
          <div style="color: #67c23a; font-size: 12px;">已就绪</div>
        </div>
      </div>
      
      <!-- 拨号盘区域 -->
      <div class="dialer-section" v-if="showDialer">
        <div class="dialer-input">
          <el-input 
            v-model="dialNumber" 
            placeholder="请输入电话号码"
            size="small"
          ></el-input>
        </div>
        <div class="dialer-buttons">
          <div class="number-pad">
            <el-button 
              v-for="num in ['1','2','3','4','5','6','7','8','9','*','0','#']"
              :key="num"
              size="mini"
              @click="addNumber(num)"
            >{{ num }}</el-button>
          </div>
        </div>
        <div class="call-actions">
          <el-button 
            type="success" 
            size="small" 
            icon="el-icon-phone"
            @click="makeCall()"
            :disabled="!dialNumber"
          >拨号</el-button>
          <el-button 
            size="small" 
            icon="el-icon-delete"
            @click="clearNumber"
          >清除</el-button>
        </div>
      </div>
      
      <!-- 通话控制区域 -->
      <div class="call-controls" v-if="isInCall">
        <el-button 
          type="danger" 
          size="small" 
          icon="el-icon-phone-outline"
          @click="hangup"
        >挂断</el-button>
        <el-button 
          type="warning" 
          size="small" 
          :icon="isHeld ? 'el-icon-video-play' : 'el-icon-video-pause'"
          @click="toggleHold"
        >{{ isHeld ? '取消保持' : '保持' }}</el-button>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button 
          size="small" 
          icon="el-icon-setting"
          @click="showDialer = !showDialer"
        >{{ showDialer ? '隐藏拨号盘' : '显示拨号盘' }}</el-button>
        <el-button 
          size="small" 
          type="warning"
          icon="el-icon-refresh"
          @click="forceCleanState"
          title="清理状态"
        >清理</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SoftPhone',
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    softphoneInstance: {
      type: Object,
      default: null
    },
    initialNumber: {
      type: String,
      default: ''
    },
    currentLoginType: {
      type: String,
      default: 'Local'
    }
  },
  data() {
    return {
      statusText: '空闲',
      callDuration: 0,
      isInCall: false,
      isHeld: false,
      loginType: this.currentLoginType,
      agentPhone: '8000@xglgj',
      showDialer: true,
      dialNumber: '',
      callTimer: null
    };
  },
  watch: {
    softphoneInstance: {
      handler(newInstance) {
        if (newInstance) {
          this.initSoftphoneEvents(newInstance);
        }
      },
      immediate: true
    },
    initialNumber: {
      handler(newNumber) {
        if (newNumber) {
          this.dialNumber = newNumber;
        }
      },
      immediate: true
    },
    
    currentLoginType: {
      handler(newVal, oldVal) {
        // 监听接听方式变化，自动重新初始化控件
        if (oldVal && newVal !== oldVal) {
          console.log('接听方式变化，从', oldVal, '到', newVal);
          this.loginType = newVal;
          if (this.softphoneInstance) {
            this.onLoginTypeChange(newVal);
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;
      
      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },
    
    initSoftphoneEvents(instance) {
      if (!instance) return;
      
      // 绑定通话事件
      instance.attachEvent({
        success: () => {
          console.log('软电话事件绑定成功');
          // 初始化成功后清理可能的残留状态
          setTimeout(() => {
            this.forceCleanState();
          }, 1000);
        },
        message: (res) => {
          console.log('收到软电话消息:', res);
          
          // 处理不同的消息格式
          if (res.event) {
            // 原有格式：res.event
            this.handleCallEvent(res.event);
          } else if (res.Event) {
            // WebSocket格式：直接是事件对象
            this.handleCallEvent(res);
          } else if (Array.isArray(res) && res.length > 0 && res[0].Event) {
            // WebSocket数组格式：事件数组
            res.forEach(event => {
              this.handleCallEvent(event);
            });
          } else {
            // 尝试直接处理
            this.handleCallEvent(res);
          }
        },
        error: (error) => {
          console.log('软电话事件绑定失败:', error);
        }
      });
    },
    
    handleCallEvent(event) {
      if (!event) return;
      
      console.log('通话事件:', event);
      
      // 处理WebSocket返回的ChannelStatus事件
      if (event.Event === 'ChannelStatus') {
        console.log('收到ChannelStatus事件:', event.ChannelStatus);
        
        switch (event.ChannelStatus) {
          case 'Hangup':
            console.log('通话已挂断，重置状态');
            this.endCall();
            break;
          case 'Ringing':
            this.statusText = '呼叫中';
            this.isInCall = false;
            this.callDuration = 0;
            break;
          case 'Up':
          case 'Answer':
            this.statusText = '通话中';
            this.isInCall = true;
            this.startCallTimer();
            break;
          default:
            console.log('其他ChannelStatus:', event.ChannelStatus);
        }
        return;
      }
      
      // 处理原有的事件格式
      if (event.type) {
        console.log('通话事件类型:', event.type);
        
        switch (event.type) {
          case 'peerstate':
            this.updateAgentStatus(event.typeValue);
            break;
          case 'dialing':
            this.statusText = '呼叫中';
            this.isInCall = false; // 呼叫中但还未接通
            this.callDuration = 0; // 重置计时但不启动计时器
            break;
          case 'dialTalking':
          case 'innerTalking':
            console.log('通话已接通，开始计时');
            this.statusText = '通话中';
            this.isInCall = true;
            this.startCallTimer(); // 只在真正接通时才开始计时
            break;
          case 'hangup':
            this.endCall();
            break;
          default:
            console.log('其他事件类型:', event.type);
        }
      }
    },
    
    updateAgentStatus(typeValue) {
      console.log('更新坐席状态:', typeValue);
      switch (typeValue) {
        case '0':
          console.log('坐席状态变为空闲，重置通话状态');
          this.endCall(); // 使用endCall来完全重置状态
          break;
        case '1':
          this.statusText = '忙碌';
          break;
        default:
          this.statusText = '未知状态';
      }
    },
    
    startCallTimer() {
      // 如果计时器已经在运行，先停止它
      if (this.callTimer) {
        clearInterval(this.callTimer);
      }
      
      this.callDuration = 0;
      this.callTimer = setInterval(() => {
        this.callDuration++;
      }, 1000);
    },
    
    stopCallTimer() {
      if (this.callTimer) {
        clearInterval(this.callTimer);
        this.callTimer = null;
      }
      // 确保计时器停止后，时间也重置为0
      this.callDuration = 0;
    },
    
    endCall() {
      console.log('结束通话，重置所有状态');
      
      // 立即停止计时器和重置时间
      this.stopCallTimer();
      
      // 重置所有状态
      this.statusText = '空闲';
      this.isInCall = false;
      this.isHeld = false;
      this.callDuration = 0;
      
      // 强制Vue更新响应式状态
      this.$nextTick(() => {
        this.callDuration = 0;
        console.log('通话状态已完全重置 - isInCall:', this.isInCall, 'callDuration:', this.callDuration, 'statusText:', this.statusText);
      });
      
      // 通知父组件通话已结束
      this.$emit('call-ended');
    },
    
    toggleCall() {
      if (this.isInCall) {
        this.hangup();
      } else {
        this.showDialer = true;
      }
    },
    
    makeCall(phoneNumber) {
      // 处理参数：如果是事件对象，则使用dialNumber
      let numberToCall;
      if (typeof phoneNumber === 'string' && phoneNumber.length > 0) {
        numberToCall = phoneNumber;
      } else {
        numberToCall = this.dialNumber;
      }
      
      if (!numberToCall || numberToCall.trim() === '') {
        this.$message.error('请输入电话号码');
        return;
      }
      
      if (!this.softphoneInstance) {
        this.$message.error('软电话未初始化，请先初始化软电话');
        return;
      }
      
      if (!this.softphoneInstance.callApi) {
        this.$message.error('软电话API未就绪，请等待初始化完成');
        return;
      }
      
      this.dialNumber = numberToCall; // 更新显示的号码
      
      console.log('发起拨号前状态检查 - isInCall:', this.isInCall, 'statusText:', this.statusText, 'callDuration:', this.callDuration);
      
      // 检查当前是否已经在通话中（添加对callDuration的检查）
      if (this.isInCall || this.statusText === '呼叫中' || this.statusText === '通话中' || this.callDuration > 0) {
        console.log('检测到通话状态异常，提示用户确认');
        this.$confirm('当前已有通话，是否先挂断现有通话再发起新呼叫？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 强制清理状态
          this.forceCleanState();
          // 等待一下再发起新呼叫
          setTimeout(() => {
            this.makeCall(numberToCall);
          }, 1000);
        });
        return;
      }
      
      try {
        console.log('发起外呼:', numberToCall);
        console.log('softphoneInstance:', this.softphoneInstance);
        console.log('callApi:', this.softphoneInstance.callApi);
        
        // 先更新状态，表示正在发起呼叫
        this.statusText = '呼叫中';
        
        this.softphoneInstance.callApi.dialout({
          calleeNumber: numberToCall,
          success: (result) => {
            console.log('外呼成功:', result);
            this.$message.success('呼叫发起成功');
          },
          fail: (error) => {
            console.log('外呼失败:', error);
            // 如果是用户正在通话的错误，给出特定的提示
            if (error.message && error.message.includes('calling')) {
              this.$message.error('坐席正在通话中，请先挂断现有通话再发起新呼叫');
            } else {
              this.$message.error(`呼叫失败: ${error.message || '未知错误'}`);
            }
            // 呼叫失败时恢复状态
            this.statusText = '空闲';
          }
        });
      } catch (error) {
        console.error('呼叫异常:', error);
        this.$message.error('呼叫异常: ' + error.message);
        // 异常时恢复状态
        this.statusText = '空闲';
      }
    },
    
    hangup() {
      if (!this.softphoneInstance) {
        this.$message.warning('软电话未初始化');
        return;
      }
      
      if (!this.softphoneInstance.callApi) {
        this.$message.warning('软电话API未就绪');
        return;
      }
      
      console.log('执行挂断操作，当前状态:', this.statusText);
      
      try {
        this.softphoneInstance.callApi.hangup({
          success: () => {
            console.log('挂断成功');
            this.$message.success('挂断成功');
            this.endCall();
          },
          fail: (error) => {
            console.log('挂断失败:', error);
            this.$message.error(`挂断失败: ${error.message || '未知错误'}`);
            // 即使挂断API失败，也要重置状态，避免界面卡死
            this.endCall();
          }
        });
      } catch (error) {
        console.error('挂断异常:', error);
        this.$message.error('挂断操作异常');
        // 发生异常时也要重置状态
        this.endCall();
      }
    },
    
    toggleHold() {
      if (!this.softphoneInstance) return;
      
      if (this.isHeld) {
        this.softphoneInstance.callApi.unhold({
          success: () => {
            this.isHeld = false;
            this.$message.success('已取消保持');
          },
          fail: (error) => {
            this.$message.error('取消保持失败');
          }
        });
      } else {
        this.softphoneInstance.callApi.hold({
          success: () => {
            this.isHeld = true;
            this.$message.success('已保持通话');
          },
          fail: (error) => {
            this.$message.error('保持通话失败');
          }
        });
      }
    },
    
    addNumber(num) {
      this.dialNumber += num;
    },
    
    clearNumber() {
      this.dialNumber = '';
    },
    
    onLoginTypeChange(value) {
      this.$emit('login-type-change', value);
      // 重新登录软电话
      this.reInitSoftphone(value);
    },
    
    // 重新初始化软电话
    async reInitSoftphone(newLoginType) {
      try {
        // 销毁当前实例
        if (this.softphoneInstance && this.softphoneInstance.destroy && typeof this.softphoneInstance.destroy === 'function') {
          this.softphoneInstance.destroy();
        }
        
        this.statusText = '重新登录中...';
        this.$message.info(`正在切换到${newLoginType === 'Local' ? '手机号' : newLoginType === 'sip' ? 'SIP话机' : 'WebRTC'}模式...`);
        
        // 通知父组件重新初始化
        this.$emit('reinit-softphone', newLoginType);
        
      } catch (error) {
        console.error('重新初始化失败:', error);
        this.$message.error('切换登录方式失败');
      }
    },
    
    closePanel() {
      this.$emit('close');
    },
    
    // 更新状态显示
    updateStatus(status) {
      this.statusText = status;
    },
    
    // 强制清理状态（用于清理可能的残留状态）
    forceCleanState() {
      console.log('强制清理软电话状态');
      
      // 立即停止计时器
      if (this.callTimer) {
        clearInterval(this.callTimer);
        this.callTimer = null;
      }
      
      // 立即重置所有状态
      this.statusText = '空闲';
      this.isInCall = false;
      this.isHeld = false;
      this.callDuration = 0;
      
      if (this.softphoneInstance && this.softphoneInstance.callApi) {
        try {
          // 尝试挂断可能存在的通话
          this.softphoneInstance.callApi.hangup({
            success: () => console.log('清理残留通话成功'),
            fail: () => console.log('无残留通话需要清理')
          });
        } catch (e) {
          console.log('清理状态时出错:', e);
        }
      }
      
      // 使用Vue.nextTick确保状态更新
      this.$nextTick(() => {
        this.callDuration = 0;
        this.isInCall = false;
        this.statusText = '空闲';
        console.log('强制清理完成 - isInCall:', this.isInCall, 'callDuration:', this.callDuration, 'statusText:', this.statusText);
      });
      
      this.$message.success('状态已清理');
    },
    
    // 外部调用：手动同步通话状态
    syncCallState(state) {
      console.log('手动同步通话状态:', state);
      if (state === 'idle' || state === 'hangup') {
        this.endCall();
      } else if (state === 'calling' || state === 'dialing') {
        this.statusText = '呼叫中';
        this.isInCall = false;
        this.callDuration = 0;
      } else if (state === 'talking' || state === 'connected') {
        this.statusText = '通话中';
        this.isInCall = true;
        this.startCallTimer();
      }
    }
  },
  
  beforeDestroy() {
    this.stopCallTimer();
  }
};
</script>

<style scoped>
.softphone-container {
  position: fixed;
  top: 10px;
  right: 10px;
  width: 300px;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 9999;
  font-size: 14px;
}

.softphone-header {
  background: #2c5aa0;
  color: white;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px 8px 0 0;
}

.status-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.status-text {
  font-weight: bold;
  font-size: 14px;
}

.call-time {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 2px;
}

.header-actions {
  display: flex;
  gap: 5px;
}

.header-actions .el-button {
  padding: 5px 8px;
  color: white;
  border-color: rgba(255,255,255,0.3);
  background-color: rgba(255,255,255,0.1);
}

.header-actions .el-button:hover {
  background-color: rgba(255,255,255,0.2);
  border-color: rgba(255,255,255,0.5);
}

/* 头部挂断按钮特殊样式 */
.header-actions .el-button--danger {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: white;
}

.header-actions .el-button--danger:hover {
  background-color: #f78989;
  border-color: #f78989;
}

.header-actions .el-button.calling {
  background: #f56c6c;
  border-color: #f56c6c;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.softphone-body {
  padding: 15px;
}

.settings-section {
  margin-bottom: 15px;
}

.setting-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
  font-size: 13px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.setting-item label {
  font-size: 12px;
  color: #606266;
  width: 60px;
}

.setting-item .el-select {
  width: 120px;
}

.phone-display {
  font-size: 12px;
  color: #409eff;
  font-weight: bold;
  background: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d4edda;
}

.dialer-section {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
  margin-top: 15px;
}

.dialer-input {
  margin-bottom: 10px;
}

.number-pad {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 5px;
  margin-bottom: 10px;
}

.number-pad .el-button {
  height: 35px;
  font-weight: bold;
}

.call-actions {
  display: flex;
  gap: 10px;
}

.call-actions .el-button {
  flex: 1;
}

.call-controls {
  display: flex;
  gap: 10px;
  margin: 15px 0;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.call-controls .el-button {
  flex: 1;
}

.action-buttons {
  margin-top: 15px;
  text-align: center;
}

/* 下拉框层级修复 */
::v-deep .softphone-select-dropdown {
  z-index: 10000 !important;
}

::v-deep .el-select-dropdown {
  z-index: 10000 !important;
}

/* 确保下拉框在软电话控件内部显示 */
.setting-item .el-select {
  position: relative;
}

::v-deep .el-select .el-select-dropdown {
  position: absolute !important;
  z-index: 10000 !important;
}

/* 响应式 */
@media (max-width: 768px) {
  .softphone-container {
    width: 280px;
    right: 5px;
    top: 5px;
  }
}
</style> 