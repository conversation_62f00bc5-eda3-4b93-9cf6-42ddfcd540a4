{"status":200,"message":"\u6210\u529f","data":{"sellerOrderResultDOS":[],"showVerify":false,"page":{"currentPage":2,"pageSize":10,"totalCount":0,"noMore":false},"canVerify":false,"canBatchDelivery":false,"canAsyncExportOrder":true},"success":true,"traceId":"EL2AgICAsoKsChjdAyDS_sLB8TEowpfGgQ8="}

ALTER TABLE `travel`.`orders`
MODIFY COLUMN `appointment_status` tinyint NOT NULL DEFAULT 0 COMMENT '是否已预约（0-未预约，1-已预约未处理，2-已预约已处理）' AFTER `next_remind_time`;

ALTER TABLE `admins`
ADD COLUMN `type` tinyint NOT NULL DEFAULT 0 COMMENT '类型（0-管理员，1-客服，2-主播，3-中控）' AFTER `route_type`;

ALTER TABLE `orders`
ADD COLUMN `live_room_works` int NOT NULL DEFAULT 0 COMMENT '排班表id' AFTER `appointment_status`;



2024/10/13
ALTER TABLE `orders`
ADD COLUMN `is_refunded` tinyint NOT NULL DEFAULT 0 COMMENT '是否已退款（0-否，1-是）' AFTER `update_time`;

update
  `orders`
  set is_refunded = 1
WHERE
  `os` IN ( '1', '7' )  and order_status = 5;

update
  `orders`
  set is_refunded = 1
WHERE
   (`os` IN ( '3', '5' )  and order_status = 4)




ALTER TABLE `orders`
ADD COLUMN `verification_date` datetime NULL COMMENT '核销日期';

ALTER TABLE `follows`
ADD INDEX `idx_order_id`(`order_id`) USING HASH;

update orders set verification_date =( SELECT FROM_UNIXTIME(create_time) FROM `follows` where status = 2 and order_id = orders.id  limit 1)

##### 2024-11-21 #####
ALTER TABLE `orders`
ADD COLUMN `is_direct_mode` tinyint NOT NULL DEFAULT 0 COMMENT '是否直连模式（0-否，1-是）' AFTER `verification_date`;


1个连接的 需求内存为 256k
16G  最大连接数 65536


// 加盟商
ALTER TABLE `admins`
ADD COLUMN `pid` int NOT NULL DEFAULT 0 COMMENT '上级ID';

##### 2025-02-28 #####
ALTER TABLE `test_travel`.`orders`
ADD COLUMN `is_public_pool` tinyint(1) NOT NULL DEFAULT 0 COMMENT '公共池（0-否，1-是）' AFTER `is_direct_mode`;

ALTER TABLE `test_travel`.`orders`
ADD COLUMN `last_pool_date` datetime NULL COMMENT '最后加入公共池时间' AFTER `is_public_pool`;

* * * * * root cd /home/<USER>/travel/service && php webman auto_fish_pub_order >> /home/<USER>/auto_fish_pub_order.log 2>&1


ALTER TABLE `orders_xs`
ADD COLUMN `create_admin_id` int NOT NULL DEFAULT 0 COMMENT '创建人id' AFTER `live_room_work_id`,
ADD INDEX `idx_create_admin_id`(`create_admin_id`);


ALTER TABLE `routes`
ADD COLUMN `qualifications` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '资质' AFTER `trip_info`,
ADD COLUMN `payment_code` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '收款码' AFTER `qualifications`;

