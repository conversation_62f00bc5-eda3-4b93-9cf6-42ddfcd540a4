<?php
declare(strict_types=1);

namespace app\model;

use support\Log;
use think\facade\Db;

/**
 * 飞猪订单模型
 */
final class FliggyOrders extends base
{
    protected $table = 'fliggy_orders';

    // 飞猪订单状态
    const STATUS_PENDING = 1; // 待确认
    const STATUS_CONFIRMED = 2; // 已确认
    const STATUS_CANCELLED = 3; // 已取消
    const STATUS_COMPLETED = 4; // 已完成
    const STATUS_REFUNDED = 5; // 已退款

    const STATUS_MAP = [
        self::STATUS_PENDING => '待确认',
        self::STATUS_CONFIRMED => '已确认',
        self::STATUS_CANCELLED => '已取消',
        self::STATUS_COMPLETED => '已完成',
        self::STATUS_REFUNDED => '已退款'
    ];

    // 退款状态
    const REFUND_STATUS_SUCCESS = 1; // 退款成功
    const REFUND_STATUS_FAILED = 2; // 退款失败
    const REFUND_STATUS_PROCESSING = 3; // 退款处理中

    protected $json = [
        'travellers', // 出行人信息
        'product_list', // 产品列表
        'contacts', // 联系人信息
        'vendor', // 系统商信息
        'order_info', // 订单详细信息
        'extend_map' // 扩展信息
    ];

    protected $type = [
        'create_at' => 'timestamp',
        'update_at' => 'timestamp',
        'confirm_time' => 'timestamp',
        'travel_date' => 'date'
    ];

    /**
     * 根据飞猪订单号查找订单
     */
    public static function findByFliggyOrderId(string $fliggyOrderId): ?self
    {
        return self::where('fliggy_order_id', $fliggyOrderId)->find();
    }

    /**
     * 根据系统商订单号查找订单
     */
    public static function findByOrderId(string $orderId): ?self
    {
        return self::where('order_id', $orderId)->find();
    }

    /**
     * 创建新订单
     */
    public static function createOrder(array $orderData): self
    {
        $order = new self();
        $order->fliggy_order_id = $orderData['fliggyOrderId'];
        $order->order_id = $orderData['orderId'] ?? '';
        $order->biz_type = $orderData['bizType'];
        $order->vendor = $orderData['vendor'];
        $order->contacts = $orderData['contacts'] ?? null;
        $order->travellers = $orderData['travellers'];
        $order->adult_number = $orderData['adultNumber'] ?? 0;
        $order->child_number = $orderData['childNumber'] ?? 0;
        $order->product_list = $orderData['productList'];
        $order->confirm_time = isset($orderData['confirmTime']) ? strtotime($orderData['confirmTime']) : null;
        $order->extend_map = $orderData['extendMap'] ?? null;
        $order->status = self::STATUS_PENDING;
        $order->create_at = time();
        $order->update_at = time();

        $order->save();
        return $order;
    }

    /**
     * 更新订单状态
     */
    public function updateStatus(int $status): bool
    {
        $this->status = $status;
        $this->update_at = time();
        return $this->save();
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUS_MAP[$this->status] ?? '未知状态';
    }
}
