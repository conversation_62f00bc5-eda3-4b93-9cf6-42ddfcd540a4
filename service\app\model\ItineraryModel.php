<?php

declare(strict_types=1);

namespace app\model;

use think\model\relation\HasMany;
use think\model\relation\HasOne;

class ItineraryModel extends ItineraryBase
{

    const ONLINE_STATUS = 'online';
    const OFFLINE_STATUS = 'offline';
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'itinerary';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'title',
        'price',
        'original_price',
        'days',
        'nights',
        'destination',
        'departure_city',
        'product_type',
        'cover_image',
        'images',
        'description',
        'features',
        'status',
        'travel_mode',
        'group_type',
        'min_group_size',
        'max_group_size',
        'collect_type',
        'deposit',
        'promotion_price',
        'category_id',
        'category_path',
        'valid_from',
        'valid_to',
        'is_long_term',
        'create_time',
        'update_time',
        'delete_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'price' => 'float',
        'original_price' => 'float',
        'days' => 'integer',
        'nights' => 'integer',
        'images' => 'json',
        'features' => 'json',
        'min_group_size' => 'integer',
        'max_group_size' => 'integer',
        'deposit' => 'float',
        'promotion_price' => 'float',
        'category_path' => 'json',
        'destination' => 'json',
        'departure_city' => 'json',
        'is_long_term' => 'boolean',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer'
    ];

    public function scheduleDays(): HasMany
    {
        return $this->hasMany(ItineraryDayModel::class ,'itinerary_id','id');
    }

    /**
     * 获取行程列表
     * @param array $where 查询条件
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public function getList(array $where = [], int $page = 1, int $pageSize = 10): array
    {
        $query = $this->where($where);

        $total = $query->count();
        $list = $query->page($page, $pageSize)->append(['id'])->select()->toArray();

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize
        ];
    }

    /**
     * 获取行程详情
     * @param int $id 行程ID
     * @return array|null
     */
    public function getDetail(int $id): ?array
    {
        $data = $this->where('id', $id)->append(['id'])->find();
        return $data ? $data->toArray() : null;
    }
}
