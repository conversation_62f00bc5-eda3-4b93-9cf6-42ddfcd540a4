<?php

namespace app\model;

use think\Model;

/**
 * contract 
 * @property integer $id (主键)
 * @property integer $product_id 
 * @property string $ye_license 营业执照
 * @property string $jy_license 经营许可证
 * @property string $pay_qr 收款二维码
 * @property string $hotel_pictures 酒店图片
 * @property string $restaurant_picture 餐厅图片
 * @property string $created_at 
 * @property string $updated_at
 */
class Contract extends base
{

    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'contract';

    public function product()
    {
        return $this->hasOne(Products::class, 'id', 'product_id');
    }

    
}
