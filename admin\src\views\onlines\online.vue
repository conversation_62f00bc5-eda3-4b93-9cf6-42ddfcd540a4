<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.username"
        placeholder="用户名"
        style="width: 200px; margin-right: 10px"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.mobile"
        placeholder="手机号"
        style="width: 200px; margin-right: 10px"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getOnlineList"
      >
        搜索
      </el-button>
    </div>
    <el-table v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">
      <el-table-column align="center" label="ID" width="60" prop="id" />
      <el-table-column align="center" label="姓名" width="80" prop="username" />
      <el-table-column align="center" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isOnline === 0" type="border-card">下线</el-tag>
          <el-tag v-if="scope.row.isOnline === 1" type="success">在线</el-tag>
          <el-tag v-if="scope.row.isOnline === 2" type="info">没上线</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否分单" width="100">
        <template #default="scope">
          <el-switch v-model="scope.row.isEndWork" :active-value="1" :inactive-value="0" @change="updateStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否私域分单" width="100">
        <template #default="scope">
          <el-switch v-model="scope.row.is_private" :active-value="1" :inactive-value="0" @change="updatePrivateStatus(scope.row)" />
        </template>
      </el-table-column>
<!--      <el-table-column align="center" label="剩余限制订单数量" width="190">
        <template #default="scope">
          <el-input-number v-model="scope.row.order_num" :max="9999999" :min="0" class="small-input-number" style="width: 160px; height: 36px;" @change="updateStatus(scope.row)" />
        </template>
      </el-table-column>-->
      <el-table-column align="center" label="在线时长" width="120">
        <template slot-scope="scope">
          {{ Math.floor((scope.row.data ? scope.row.data.onlineTime : scope.row.onlineTime) / 60) + '分钟' || '--' }} 
        </template>
      </el-table-column>
      <el-table-column width="138px" align="center" label="上线时间">
        <template slot-scope="scope">
          <span>{{ scope.row.start_work_time | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column width="138px" align="center" label="停止分单时间">
        <template slot-scope="scope">
          <span>{{ scope.row.end_work_time | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column width="138px" align="center" label="下线时间">
        <template slot-scope="scope">
          <span>{{ scope.row.last_work_time | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column width="138px" align="center" label="线路">
        <template slot-scope="scope">
          <el-tag type="border-card">{{ scope.row.routes }}</el-tag> 
        </template>
      </el-table-column> -->
    </el-table>
    
    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="listQuery.page"
        :page-sizes="[10,20,30,50]"
        :page-size="listQuery.limit"
        :total="total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>

export default {
  name: 'GetOnlineList',
  components: { },
  data() {
    return {
      statusArr: { 0: '禁用', 1: '启用' },
      list: [],
      total: 0,
      loading: false,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        status: null,
        content: '',
        mobile: ''
      },
      dialogCreate: false,
      dialogEdit: false,
      item: {},
      anchors: {}
    }
  },
  created() {
    this.listQuery.status = this.$route.query.status || null
    this.listQuery.content = this.$route.query.content || null
    this.listQuery.mobile = this.$route.query.mobile || null
    this.getOnlineList()
  },
  methods: {
    getOnlineList() {
      this.listLoading = true
      this.$axios.get('/admin/admin/getOnlineList', { params: this.listQuery }).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getOnlineList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getOnlineList()
    },
    updateStatus(item) {
      this.$axios.post('/admin/admin/editInfo', { id: item.id, order_num: item.order_num, is_order: item.isEndWork }).then(() => {
        this.getOnlineList()
      }).catch(() => {
      })
    },
    updatePrivateStatus(item) {
      this.$axios.post('/admin/admin/editInfo', { id: item.id, is_private: item.is_private }).then(() => {
        this.getOnlineList()
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 52px; /* 分页条的高度，以避免内容重叠 */
}

.search {
  margin-left: 10px;
}

.pagination-container {
  position: fixed;
  bottom: 0;
  left: 220px; /* 侧边栏宽度 */
  right: 0;
  background: #fff;
  padding: 10px 20px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  justify-content: center;
}
</style>
