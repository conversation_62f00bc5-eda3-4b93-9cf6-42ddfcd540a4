(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-674b82bd"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,a){var s=r(),l=t-s,o=20,u=0;e="undefined"===typeof e?500:e;var c=function(){u+=o;var t=Math.easeInOutQuad(u,s,l,e);n(t),u<e?i(c):a&&"function"===typeof a&&a()};c()}},3813:function(t,e,a){"use strict";a("3a6c")},"3a6c":function(t,e,a){},"67f2":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[a("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},n=[],r=(a("374d"),a("09f4")),s={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(r["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(r["a"])(0,800)}}},l=s,o=(a("7d30"),a("8a34")),u=Object(o["a"])(l,i,n,!1,null,"28fdfbeb",null);e["a"]=u.exports},7140:function(t,e,a){},"7d30":function(t,e,a){"use strict";a("7140")},a25c:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-date-picker",{staticClass:"filter-item",attrs:{type:"datetimerange","default-time":["00:00:00","23:59:59"],"range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.listQuery.times,callback:function(e){t.$set(t.listQuery,"times",e)},expression:"listQuery.times"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"产品名称"},model:{value:t.listQuery.product_name,callback:function(e){t.$set(t.listQuery,"product_name",e)},expression:"listQuery.product_name"}}),a("el-select",{staticClass:"filter-item",attrs:{placeholder:"平台状态"},model:{value:t.listQuery.os_status,callback:function(e){t.$set(t.listQuery,"os_status",e)},expression:"listQuery.os_status"}},t._l(t.oss,(function(t,e){return a("el-option",{key:e,attrs:{label:t,value:e}})})),1),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}}},[t._v(" 导出 ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"center",label:"产品名称",width:"260",prop:"product_name"}}),a("el-table-column",{attrs:{align:"center",label:"平台",width:"80",prop:"os_name"}}),a("el-table-column",{attrs:{align:"center",label:"订单数",width:"80",prop:"all"}}),a("el-table-column",{attrs:{align:"center",label:"订单金额",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.total?parseFloat(e.row.total)/100:0))])]}}])}),a("el-table-column",{attrs:{align:"center",width:"100px",label:"待跟进",prop:"wait"}}),a("el-table-column",{attrs:{width:"100px",align:"center",label:"跟进中",prop:"doing"}}),a("el-table-column",{attrs:{align:"center",width:"100px",label:"待使用数",prop:"tobeused"}}),a("el-table-column",{attrs:{width:"100px",align:"center",label:"待使用金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.tobeused_price?parseFloat(e.row.tobeused_price)/100:0))])]}}])}),a("el-table-column",{attrs:{align:"center",width:"100px",label:"核销数",prop:"asset"}}),a("el-table-column",{attrs:{width:"100px",align:"center",label:"核销金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.asset_price?parseFloat(e.row.asset_price)/100:0))])]}}])}),a("el-table-column",{attrs:{align:"center",width:"100px",label:"退款数",prop:"refund"}}),a("el-table-column",{attrs:{width:"100px",align:"center",label:"退款金额"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.refund_price?parseFloat(e.row.refund_price)/100:0))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"核销率",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.asset_rate)+"% ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"退款率",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.refund_rate)+"% ")]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}})],1)},n=[],r=a("d09a"),s=(a("e224"),a("4cc3"),a("5227"),a("67f2")),l={name:"productNameList",components:{Pagination:s["a"]},data:function(){return{active:"follow",types:{0:"",1:"",2:"",3:"primary",4:"success",5:"warning"},types2:{1:"primary",2:"success",3:"warning"},status_arr:["待跟进","跟进中","已核销","核销失败","放弃跟单","加入公海"],type_arr:["-","收益","支出"],timetype_arr:{},order_status:["#9e9f9c","#04bcd9","#fc9904","#1193f4","#48b14b","#eb1662","#9d1cb5"],follow_status:["#9e9f9c","#04bcd9","#fc9904","#1193f4","#48b14b","#eb1662"],options:[],list:[],total:0,listLoading:!0,listQuery:{page:1,limit:10,product_name:""},oss:{},form:{}}},mounted:function(){this.listQuery.status=this.$route.query.status||null,this.listQuery.zhubo=this.$route.query.zhubo||null,this.$route.query.start&&this.$route.query.end&&(this.listQuery.times=[this.$route.query.start,this.$route.query.end]),this.getList()},activated:function(){this.listQuery.status=this.$route.query.status||null,this.listQuery.zhubo=this.$route.query.zhubo||null,this.$route.query.start&&this.$route.query.end&&(this.listQuery.times=[this.$route.query.start,this.$route.query.end]),this.getList()},methods:{getList:function(t){var e=this;if(1!=t)this.listQuery.excel=0,this.$axios.get("/admin/index/productNameList",{params:this.listQuery}).then((function(t){e.listLoading=!1,console.log(e.listLoading),e.list=t.data.data,e.total=t.data.total,e.timetype_arr=t.ext.timetype,e.oss=t.ext.oss})).catch((function(){}));else{if(this.listQuery.excel=1,console.log("l:"+this.listQuery.times),!this.listQuery.times)return void this.$message({message:"请选择日期",type:"warning"});var a=this.listQuery.times[0]instanceof Date,i=Object(r["a"])(Object(r["a"])({},this.listQuery),{},{times:[a?this.listQuery.times[0].toISOString():"",a?this.listQuery.times[1].toISOString():""]});window.open("/admin/index/productNameList?"+this.objectToQuery(i))}},objectToQuery:function(t){return Object.keys(t).map((function(e){var a=t[e];return void 0==a||null==a?"":encodeURIComponent(e)+"="+encodeURIComponent(a)})).join("&")}}},o=l,u=(a("3813"),a("8a34")),c=Object(u["a"])(o,i,n,!1,null,"8eba745a",null);e["default"]=c.exports}}]);