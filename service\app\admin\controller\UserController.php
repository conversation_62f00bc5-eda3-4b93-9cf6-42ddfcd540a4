<?php
namespace app\admin\controller;

use app\model\Users;
use support\Request;

class UserController extends base
{
    public function Index(Request $request) {
        $list = Users::order('id', 'desc')->paginate($request->get('limit',10));

        return $this->success($list->hidden(['password']));
    }

    public function edit(Request $request) { 
        $id = $request->get('id', 0);

        $info = Users::find($id);

        return $this->success($info->hidden(['password','token']));
    }

    public function save(Request $request) {
        $id = $request->post('id',0);

        if($id) {
            $item = Users::find($id);
        }
        if(empty($item)) {
            return $this->error(2101);
        }
        
        $back = $item->save();
        if($back) 
            return $this->success($item->hidden(['password','remember_token']));
        return $this->error(2003);
    }
}