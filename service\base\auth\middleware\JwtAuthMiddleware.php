<?php

namespace base\auth\middleware;

use base\auth\AuthHttpClient;
use base\auth\UserInfo;
use base\exception\BaseException;
use DI\Attribute\Inject;
use Webman\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;

/**
 * JWT 鉴权中间件
 * 通过 iam-api 验证 JWT 令牌并获取用户信息
 */
class JwtAuthMiddleware implements MiddlewareInterface
{
    #[Inject]
    public AuthHttpClient $httpClient;

    /**
     * 处理请求
     *
     * @param Request $request
     * @param callable $handler
     * @return Response
     * @throws BaseException
     * @throws \JsonException
     */
    public function process(Request $request, callable $handler): Response
    {
        // 获取 JWT 令牌
        $authorization = $request->header('authorization');
        if (empty($authorization)) {
            throw new BaseException('Authorization header is required', 401);
        }

        // 提取令牌（移除 Bearer 前缀）
        $token = $authorization;
        if (str_starts_with($authorization, 'Bearer ')) {
            $token = substr($authorization, 7);
        }

        try {
            // 通过 iam-api 验证令牌并获取用户信息
            $userinfo = $this->httpClient->verifyTokenAndGetUserInfo($token);

            if (empty($userinfo)) {
                throw new BaseException('Failed to get user info from IAM API', 401);
            }

            // 初始化用户信息到全局上下文
            UserInfo::init($userinfo);

        } catch (BaseException $e) {
            // 如果是401错误，说明令牌无效或过期
            if ($e->getCode() === 401) {
                throw new BaseException('Invalid or expired token', 401);
            }
            // 其他错误重新抛出
            throw $e;
        }

        return $handler($request);
    }
}
