import {createRouter, createWebHistory} from "vue-router";

const routes  = [
    {
        path: '/:catchAll(.*)*', // 使用通配符捕获所有未定义的路由
        redirect: '/404' // 重定向到 404 页面
    },
    {
        path: '/401',
        name: '401',
        component: () => import('../views/error-page/401.vue'),
    },
    {
        path: '/404',
        name: '404',
        component: () => import('../views/error-page/404.vue'),
    },
    {
        path: '/',
        name: 'home',
        component: () => import('../views/home/<USER>'),
        meta: { title: '产品详情', icon: 'home' }
    }
];

const router = createRouter({
    history: createWebHistory(),
    routes,
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
    // 更改页面标题
    if (to.meta && to.meta.title) {
        document.title = to.meta.title;
    } else {
        document.title = '默认标题'; // 如果没有指定标题，则使用默认标题
    }
    next();
});

export default router;