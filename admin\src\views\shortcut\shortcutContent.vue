<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.content" placeholder="搜索快捷内容" style="width: 200px;" class="filter-item" />
      <el-select v-model="listQuery.status" filterable placeholder="状态" class="filter-item" style="width: 120px;">
        <el-option key="" label="请选择" value="" />
        <el-option v-for="(v, k) in statusArr" :key="k" :label="v" :value="k" />
      </el-select>
      <el-button class="filter-item search" type="primary" icon="el-icon-search" @click="getShortcutContent">搜索</el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-circle-plus" @click="onAdd">添加</el-button>
    </div>

    <el-table v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">
      <el-table-column align="center" fixed label="ID" width="80" prop="id" />
      <el-table-column align="center" fixed label="快捷内容" width="380" prop="content" />
      <el-table-column align="center" label="排序" width="140">
        <template #default="scope">
          <el-input-number v-model="scope.row.sort" :max="100" :min="0" class="small-input-number" style="width: 110px; height: 36px;" @change="updateSort(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" width="100">
        <template #default="scope">
          <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="updateStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column align="center" width="220" label="操作">
        <template #default="scope">
          <el-button type="primary" size="small" icon="el-icon-edit" @click="onEdit(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" icon="el-icon-delete" @click="onDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getShortcutContent" />

    <el-dialog title="添加快捷内容" :visible.sync="dialogCreate">
      <el-form label-width="120px" :model="anchors">
        <el-form-item label="快捷内容">
          <el-input v-model="anchors.content" type="textarea" placeholder="请输入快捷内容" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="anchors.sort" :max="100" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="anchors.status" :active-value="1" :inactive-value="0" active-color="#13ce66" inactive-color="#ff4949" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-loading="loading" type="primary" @click="onSave">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog title="编辑内容" :visible.sync="dialogEdit">
      <el-form label-width="120px" :model="anchors">
        <el-form-item label="快捷内容">
          <el-input v-model="anchors.content" type="textarea" placeholder="请输入快捷内容" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="anchors.sort" :max="100" :min="0" controls-position="right" />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="anchors.status" :active-value="1" :inactive-value="0" active-color="#13ce66" inactive-color="#ff4949" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-loading="loading" type="primary" @click="onSave">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/PaginationFixed'

export default {
  name: 'GetShortcutContent',
  components: { Pagination },
  data() {
    return {
      statusArr: { 0: '禁用', 1: '启用' },
      list: [],
      total: 0,
      loading: false,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        status: null,
        content: ''
      },
      dialogCreate: false,
      dialogEdit: false,
      item: {},
      anchors: {}
    }
  },
  created() {
    this.listQuery.status = this.$route.query.status || null
    this.listQuery.content = this.$route.query.content || null
    this.getShortcutContent()
  },
  methods: {
    getShortcutContent() {
      this.listLoading = true
      this.$axios.get('/admin/shortcutContent/list', { params: this.listQuery }).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    onAdd() {
      this.anchors = { sort: 0 } // 初始化时默认排序值为0
      this.dialogCreate = true
    },
    onEdit(item) {
      this.anchors = { ...item }
      this.dialogEdit = true
    },
    onSave() {
      if (this.loading) return
      this.loading = true
      const api = this.dialogCreate ? '/admin/shortcutContent/add' : '/admin/shortcutContent/edit'
      this.$axios.post(api, this.anchors).then(() => {
        this.dialogCreate = false
        this.dialogEdit = false
        this.loading = false
        this.getShortcutContent()
      }).catch(() => {
        this.loading = false
      })
    },
    onDel(item) {
      this.$axios.post('/admin/shortcutContent/del', { id: item.id }).then(() => {
        this.getShortcutContent()
      }).catch(() => {
      })
    },
    updateSort(item) {
      this.$axios.post('/admin/shortcutContent/edit', { id: item.id, sort: item.sort }).then(() => {
        this.getShortcutContent()
      }).catch(() => {
      })
    },
    updateStatus(item) {
      this.$axios.post('/admin/shortcutContent/edit', { id: item.id, status: item.status }).then(() => {
        this.getShortcutContent()
      }).catch(() => {
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 52px; /* 分页条的高度，以避免内容重叠 */
}

.search {
  margin-left: 10px;
}
</style>
