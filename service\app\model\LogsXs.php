<?php
namespace app\model;

class LogsXs extends base{

    const Actions = [1 => '查看电话', 2 => '核销订单', 3=> '核销订单', 4 => '分单', 5 => '加微信', 6 => '公海领取', 7 => '申请拉回订单', 8 => '确认拉单请求', 9 => '拒绝拉单请求', 10 => '取消拉单请求',11 => '核销失败',12 => '管理员操作转单'];

    public function getActionNameAttr($val) {
        return self::Actions[$this->action] ?? '未知';
    }

    public function admin(){
        return $this->belongsTo(Admins::class, 'admin_id')->visible(['name','username','avatar']);
    }

    public function orders(){
        return $this->belongsTo(OrdersXs::class, 'order_id');
    }

    //查看详情
    public static function see($id, $admin_id) {
        return self::todo($id, $admin_id, 1);
    }

    //录入核销码
    public static function pass($id, $admin_id, $sn) {
        return self::todo($id,$admin_id,2, $sn);
    }

    //直接核销的订单,订单还没有到系统,会直接抓取回来
    public static function pass2( $admin_id, $sn) {
        return self::todo(0,$admin_id,3, $sn);
    }

    //添加微信
    public static function wechat($id, $admin_id) {
        return self::todo($id,$admin_id, 5);
    }

    public static function todo($id, $admin_id, $action, $sn=null) {
        $self = new self();
        $self->order_id = $id;
        $self->admin_id = $admin_id;
        $self->action = $action;
        $self->sn = $sn;
        return $self->save();
    }
}