<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="30%"
    :before-close="handleClose"
  >
    <el-form :rules="rules" ref="form" :model="form" label-width="80px">
      <el-form-item label="直播间" prop="live_room_id">
        <el-select v-model="form.live_room_id" placeholder="请选择直播间">
          <el-option
            v-for="item in liveroomList"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="开始时间" prop="start">
        <el-date-picker
          @change="handleDate"
          v-model="form.start"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          placeholder="选择日期时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间" prop="end">
        <el-date-picker
          v-model="form.end"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          @change="handleDate"
          placeholder="选择日期时间"
        >
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="时间">
        <el-time-select
          placeholder="起始时间"
          v-model="startTime"
          :picker-options="{
            start: '08:00',
            step: '00:15',
            end: '24:00',
          }"
        >
        </el-time-select>
        <el-time-select
          placeholder="结束时间"
          @change="handleDate"
          v-model="endTime"
          :picker-options="{
            start: '08:00',
            step: '00:15',
            end: '24:00',
            minTime: startTime,
          }"
        >
        </el-time-select>
      </el-form-item>

      <el-form-item label="主播" prop="zhubo_id">
        <el-select v-model="form.zhubo_id" placeholder="请选择主播">
          <el-option
            v-for="item in zhuboList"
            :label="item.name"
            :value="item.id"
            :disabled="item.disabled"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="中控" prop="zhongkong_id">
        <el-select v-model="form.zhongkong_id" placeholder="请选择中控">
          <el-option
            v-for="item in zhongkongList"
            :label="item.name"
            :value="item.id"
            :disabled="item.disabled"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="路线">
        <el-input v-model="form.route"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleEdit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { availableZhubo, liveroom } from "@/api/scheduling";
export default {
  props: {
    title: {
      type: String,
      default: "添加排班",
    },
  },
  data() {
    return {
      dialogVisible: false,
      startTime: "",
      endTime: "",
      form: {
        live_room_id: "",
        start: "",
        end: "",
        zhubo_id: "",
        zhongkong_id: "",
        route: "",
      },
      zhuboList: [],
      zhongkongList: [],
      liveroomList: [],
      dayDate: "",
      rules: {
        live_room_id: [
          { required: true, message: "请选择直播间", trigger: "change" },
        ],
        zhongkong_id: [
          { required: true, message: "请选择中控", trigger: "change" },
        ],
        zhubo_id: [
          { required: true, message: "请选择主播", trigger: "change" },
        ],
        // date: [{ required: true, message: "请选择时间", trigger: "change" }],
      },
    };
  },
  watch: {
    form: function () {
      this.handleDate();
    },
  },
  methods: {
    extractTime(dateTimeStr) {
      // 定义正则表达式来匹配时间部分
      const regex = /^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/;
      const match = dateTimeStr.match(regex);

      if (!match) {
        throw new Error("不匹配");
      }

      // 提取时间部分
      return `${match[4]}:${match[5]}`;
    },
    init(live_room_id, form, date) {
      this.dialogVisible = true;
      this.$nextTick((res) => {
        this.$refs["form"].resetFields();
        this.dayDate = date;
        this.form = !!form.id
          ? form
          : {
              live_room_id: "",
              start: "",
              end: "",
              zhubo_id: "",
              zhongkong_id: "",
            };
        this.form.live_room_id = live_room_id;
        this.startTime = form.id ? this.extractTime(this.form.start) : "";
        this.endTime = form.id ? this.extractTime(this.form.end) : "";
        console.log(this.startTime);
        console.log(this.endTime);
      });
      this.liveroom();
    },
    // 直播间列表
    async liveroom() {
      try {
        let { data } = await liveroom();
        this.liveroomList = data;
      } catch (err) {
        console.log(err);
      }
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleDate() {
      this.form.end = `${this.dayDate} ${this.endTime}:00`;
      this.form.start = `${this.dayDate} ${this.startTime}:00`;
      if (this.form.end && this.form.start) {
        availableZhubo({ ...this.form, type: 2 }).then((res) => {
          this.zhuboList = res.data;
        });
        availableZhubo({ ...this.form, type: 3 }).then((res) => {
          this.zhongkongList = res.data;
        });
      }
    },
    handleEdit() {
      //   this.form.end = `${this.dayDate} ${this.startTime}:00`;
      //   this.form.start = `${this.dayDate} ${this.endTime}:00`;
      this.dialogVisible = false;
      this.$emit("update", this.form);
    },
  },
};
</script>

<style scoped lang="scss"></style>
