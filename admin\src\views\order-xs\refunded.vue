<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.sn"
        placeholder="订单号"
        style="width: 200px"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.product_name"
        placeholder="标题"
        style="width: 200px"
        class="filter-item"
      />

      <el-input
        v-model="listQuery.mobile"
        placeholder="手机号"
        style="width: 200px"
        class="filter-item"
      />

      <el-input
        v-model="listQuery.zhubo"
        placeholder="主播"
        style="width: 100px"
        class="filter-item"
      />

      <el-input
        v-model="listQuery.admin"
        placeholder="客服"
        style="width: 100px"
        class="filter-item"
      />

      <!-- <el-cascader
        v-model="listQuery.os_status"
        placeholder="平台状态"
        :options="oss"
        clearable
        :props="{ checkStrictly: true }"
        class="filter-item"
        @change="handleChange"
      /> -->

      <el-select
        v-model="listQuery.status"
        filterable
        placeholder="跟进状态"
        class="filter-item"
        style="width: 120px"
      >
        <el-option key="" label="请选择" value="" />
        <el-option
          v-for="(v, k) in status_arr"
          :key="k"
          :label="v"
          :value="k"
        />
      </el-select>

      <el-select
        v-model="listQuery.timetype"
        filterable
        placeholder="时间"
        class="filter-item"
        style="width: 120px"
      >
        <el-option key="" label="请选择" value="" />
        <el-option
          v-for="(v, k) in timetype_arr"
          :key="k"
          :label="v"
          :value="k"
        />
      </el-select>

      <el-date-picker
        v-model="listQuery.times"
        class="filter-item"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        :default-time="['00:00:00', '23:59:59']"
        end-placeholder="结束日期"
      />
      <el-select
        v-model="listQuery.appointment_status"
        filterable
        clearable
        placeholder="预约状态"
        class="filter-item"
        style="width: 120px"
      >
        <el-option key="0" label="未预约" value="0" />
        <el-option key="1" label="已预约(未处理)" value="1" />
        <el-option key="2" label="已预约(已处理)" value="2" />
      </el-select>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList"
      >
        搜索
      </el-button>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList(1)"
      >
        导出
      </el-button>

      <!-- <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="dialog2Visible = true"
      >
        核单
      </el-button> -->
      <el-button
        type="primary"
        :disabled="multipleSelection.length == 0"
        class="filter-item"
        icon="el-icon-refresh"
        @click="onCirculationAll()"
      >
        批量流转
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      :height="tableMaxHeight"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column align="center" fixed width="200" label="操作">
        <template slot-scope="scope">
          <el-button-group>
            <el-button
              :type="
                scope.row.backs && scope.row.backs.status == 0
                  ? types[7]
                  : types[4]
              "
              size="small"
              icon="el-icon-refresh"
              @click="onCirculation(scope.row)"
            >
              {{
                scope.row.backs && scope.row.backs.status == 0
                  ? "流转中"
                  : "流转出"
              }}
            </el-button>
            <el-button
              :type="types[scope.row.order_status]"
              size="small"
              icon="el-icon-edit"
              @click="onInfo(scope.row)"
            >
              跟进
            </el-button>
            <el-button
              size="small"
              icon="el-icon-thumb"
              @click="onOneClickRepair(scope.row)"
            >
              同步
            </el-button>

            <el-button
              v-if="scope.row.appointment_status == 1"
              size="small"
              icon="el-icon-edit"
              @click="onOneClickYyHandle(scope.row)"
            >
              预约处理
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed label="电话" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}</span>
          <span style="display: block; font-size: 12px"
            >{{ scope.row.mobileInfo.area }}-{{
              scope.row.mobileInfo.originalIsp
            }}</span
          >
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        fixed
        label="来源"
        width="80"
        prop="source_name"
      />

      <el-table-column align="center" fixed label="直播" width="60">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.is_zhibo">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        fixed
        label="客服"
        width="80"
        prop="admin.username"
      />

      <el-table-column align="center" label="订单号" width="180" prop="sn" />

      <el-table-column width="138px" align="center" label="下单时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.create_at | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column width="160px" align="center" label="派单时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.give_time | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="预约状态（抖音）" width="80">
        <template slot-scope="scope">
          <div
            style="padding: 1px 5px; border-radius: 3px"
            :style="{
              color: order_status[scope.row.appointment_status],
              border: `1px solid ${order_status[scope.row.appointment_status]}`,
            }"
            type="primary"
          >
            {{
              scope.row.appointment_status == 1
                ? "已预约(未处理)"
                : scope.row.appointment_status == 2
                ? "已预约(已处理)"
                : "未预约"
            }}
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="状态" width="80">
        <template slot-scope="scope">
          <div
            style="padding: 1px 5px; border-radius: 3px"
            :style="{
              color: order_status[scope.row.order_status],
              border: `1px solid ${order_status[scope.row.order_status]}`,
            }"
            type="primary"
          >
            {{ scope.row.order_status_name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column align="center" label="跟进状态" width="90">
        <template slot-scope="scope">
          <div
            style="padding: 1px 5px; border-radius: 3px"
            :style="{
              color: follow_status[scope.row.status],
              border: `1px solid ${follow_status[scope.row.status]}`,
            }"
            type="primary"
          >
            {{ scope.row.status_name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        width="500px"
        label="标题"
        prop="product_name"
      />
      <el-table-column
        width="500px"
        align="center"
        label="跟进备注"
        prop="remark"
      />

      <el-table-column
        align="center"
        label="联系人"
        width="120"
        prop="contact"
      />

      <!-- <el-table-column align="center" label="微信" width="80">
        <template slot-scope="scope">
          <i v-if="scope.row.is_wechat>0" class="el-icon-circle-check"></i>
        </template>
      </el-table-column> -->

      <el-table-column width="138px" align="center" label="出行时间">
        <template slot-scope="scope">
          <span>{{ scope.row.travel_date | parseTime("{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>

      <el-table-column width="138px" align="center" label="最后跟进时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.last_follow | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="核单" width="80">
        <template slot-scope="scope">
          <i v-if="scope.row.is_check == 1" class="el-icon-check" />
          <i v-if="scope.row.is_check == 2" class="el-icon-close" />
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="138px"
        label="分类"
        prop="category_desc"
      />

      <el-table-column align="center" label="总金额" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.total_price / 100 }}</span>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        width="80px"
        label="人数"
        prop="quantity"
      />
      <el-table-column
        align="center"
        label="主播"
        width="80"
        prop="anchor.username"
      />
      <el-table-column width="138px" align="center" label="修改时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.update_time | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="setMode"
    />

    <el-dialog title="订单跟进" :visible.sync="dialogVisible">
      <el-form label-width="130px" :model="item">
        <el-form-item label="产品名称">
          {{ item.product_name }}
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="产品状态">
              {{ item.order_status_name }}
            </el-form-item>
            <el-form-item label="数量">
              {{ item.quantity }}
            </el-form-item>
            <el-form-item label="联系人">
              {{ item.contact }}
            </el-form-item>
            <el-form-item label="手机">
              {{ item.mobile }}
            </el-form-item>
            <el-form-item label="下单时间">
              {{ item.create_at | parseTime("{y}-{m}-{d} {h}:{i}") }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="人员">
              <el-row>
                <el-col :span="3">大人</el-col>
                <el-col :span="5"
                  ><el-input
                    v-model="item.personnel.adult"
                    name="adult"
                    placeholder="大人"
                /></el-col>
                <el-col :span="3">老人</el-col>
                <el-col :span="5"
                  ><el-input
                    v-model="item.personnel.old"
                    name="old"
                    placeholder="老人"
                /></el-col>
                <el-col :span="3">小孩</el-col>
                <el-col :span="5"
                  ><el-input
                    v-model="item.personnel.child"
                    name="child"
                    placeholder="小孩"
                /></el-col>
              </el-row>
            </el-form-item>

            <el-form-item v-if="item.status !== 1" label="核销码">
              <el-input
                v-model="item.check_sn"
                name="check_sn"
                placeholder="请输入平台核销码"
              />
            </el-form-item>

            <el-form-item label="加微信" v-if="item.status !== 2">
              <el-checkbox
                v-model="item.is_wechat"
                :true-label="1"
                :false-label="0"
                >已加微信</el-checkbox
              >
            </el-form-item>

            <el-form-item required pros="travel_date" label="出游日期">
              <el-date-picker
                v-model="item.travel_date"
                type="date"
                placeholder="选择日期时间"
              />
            </el-form-item>

            <el-form-item v-if="item.status !== 1" label="返回日期">
              <el-date-picker
                v-model="item.travel_end"
                type="date"
                placeholder="选择日期时间"
              />
            </el-form-item>

            <el-form-item
              required
              pros="next_follow"
              label="下次跟进时间"
              v-if="item.status !== 2"
            >
              <el-date-picker
                v-model="next_follow"
                type="datetime"
                placeholder="选择日期时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="跟进状态">
          <template v-for="(v, k) in status_arr">
            <el-radio v-if="k > 0" v-model="item.status" :label="k" border>{{
              v
            }}</el-radio>
          </template>
        </el-form-item>
        <!--        <el-form-item label="快捷跟进" style="width: 600px;">
          <el-select v-model="value" placeholder="请选择" @change="onChange">
            <el-form-item style="display: inline-flex;text-align: left;width: 770px;">
              <el-option
                v-for="item in options"
                :key="item.value"
                style="width: 250px;display: inline-flex;word-break: break-all;"
                :label="item.label"
                :value="item.label"
              />
            </el-form-item>
          </el-select>
        </el-form-item>-->

        <el-form-item required pros="desc" label="跟进说明">
          <el-input v-model="item.desc" type="textarea" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onSave(item)">保 存</el-button>
      </div>

      <el-tabs v-model="active" type="border-card">
        <el-tab-pane name="follow" label="跟进记录">
          <el-table :data="item.follow" style="width: 100%">
            <el-table-column label="日期" width="138">
              <template slot-scope="scope">
                <span>{{
                  scope.row.create_time | parseTime("{y}-{m}-{d} {h}:{i}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="跟进人" width="110" prop="name" />
            <el-table-column label="状态" width="80">
              <template slot-scope="scope">
                <span>{{ status_arr[scope.row.status] }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="desc" label="跟进说明" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane name="finance" label="财务记录">
          <el-table :data="item.finance" style="width: 100%">
            <el-table-column label="日期">
              <template slot-scope="scope">
                <span>{{
                  scope.row.create_time | parseTime("{y}-{m}-{d} {h}:{i}")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型" width="110">
              <template slot-scope="scope">
                <span>{{ type_arr[scope.row.type] }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="120">
              <template slot-scope="scope">
                <span>{{ scope.row.total / 100 }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog title="纯核销" :visible.sync="dialog2Visible">
      <el-form label-width="160px" :model="form">
        <el-form-item label="平台">
          <el-radio v-model="form.os" label="1">美团</el-radio>
        </el-form-item>
        <el-form-item label="核销码">
          <el-input v-model="form.check_sn" placeholder="请输入平台核销码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onPass(form)">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog title="申请转出订单" :visible.sync="applyVisible">
      <el-form label-width="160px" :model="item3" :rules="rules" ref="ruleForm">
        <el-form-item label="标题:" v-if="!isAll">
          <el-input v-model="item3.product_name" disabled />
        </el-form-item>
        <el-form-item label="订单号:" v-if="!isAll">
          <el-input v-model="item3.sn" disabled />
        </el-form-item>
        <el-form-item label="流转对象:" style="width: 600px" prop="flowObj">
          <el-select
            v-model="item3.flowObj"
            placeholder="请选择"
            @change="onChange2"
          >
            <el-form-item
              style="display: inline-flex; text-align: left; width: 770px"
            >
              <el-option
                v-for="item in adminList"
                :key="item.value"
                style="
                  width: 250px;
                  display: inline-flex;
                  word-break: break-all;
                "
                :label="item.username"
                :value="item.id"
              />
            </el-form-item>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- scope.row.backs&&scope.row.backs.status==2? -->
        <el-button
          v-if="item3.backs && item3.backs.status == 0"
          type="primary"
          @click="onCancel(item3.flowObj)"
          >取 消</el-button
        >
        <el-button
          v-else
          type="primary"
          @click="onCirculationSave(item3.flowObj)"
          >确 认</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import Pagination from '@/Wangeditor/Pagination'
import Pagination from "@/components/PaginationFixed";
import { orderBack, orderbackBatch } from "@/api/order";
export default {
  name: "Orderlist",
  components: { Pagination },
  data() {
    return {
      active: "follow",
      types: {
        0: "",
        1: "",
        2: "",
        3: "primary",
        4: "success",
        5: "warning",
        6: "danger",
        7: "info",
      },
      types2: { 1: "primary", 2: "success", 3: "warning" },
      status_arr: ["待跟进", "跟进中", "已核销", "核销失败", "放弃跟单", "加入公海"],
      type_arr: ["-", "收益", "支出"],
      timetype_arr: {},
      order_status: [
        "#9e9f9c",
        "#04bcd9",
        "#fc9904",
        "#1193f4",
        "#48b14b",
        "#eb1662",
        "#9d1cb5",
      ],
      follow_status: [
        "#9e9f9c",
        "#04bcd9",
        "#fc9904",
        "#1193f4",
        "#48b14b",
        "#eb1662",
      ],
      options: [],
      value: null,
      next_follow: null,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        times: [],
        status: null,
        admin: null,
        zhubo: null,
        os_status: [],
        appointment_status: "",
      },
      item: { next_follow: "", personnel: { adult: "" } },
      follow: [],

      dialogVisible: false,
      dialog2Visible: false,
      applyVisible: false,
      oss: [],
      isSynchronization: false,
      item3: {
        sn: null,
        backs: null,
        flowObj: "",
        os: null, // 初始值，你可以根据需要设置为 1、2 或 3
      },
      multipleSelection: [],
      sn: [],
      adminList: [],
      form: {},
      isAll: false,
      rules: {
        flowObj: [
          { required: true, message: "请选择流转对象", trigger: "change" },
        ],
      },
    };
  },
  async created() {
    // this.listQuery.status = this.$route.query.status || null
    this.listQuery.zhubo = this.$route.query.zhubo || null;
    if (this.$route.query.start && this.$route.query.end) {
      this.listQuery.times = [this.$route.query.start, this.$route.query.end];
    }
    // console.log(typeof this.$route.params.id);
    this.setQuery("status");
    this.setQuery("os_status");
    this.setQuery("times");
    this.setQuery("appointment_status");
    // await this.getList();
    // if (!this.$route.query.id) {
    //   await this.setOneClickRepair();
    //   await this.getList();
    // }
    this.getShortcutContent();
    this.getAdminList();
  },
  mounted() {
    console.log(this.$route.query);
    this.setMode();
    if (this.$route.query.id) {
      this.onInfo({ id: this.$route.query.id });
    }
  },
  computed: {
    tableMaxHeight() {
      return window.innerHeight - 320 + "px";
    },
  },
  watch: {
    $route(to, from) {
      this.onInfo({ id: this.$route.query.id });
    },
  },
  methods: {
    handleSelectionChange(val) {
      // console.log(val);

      this.multipleSelection = val;
      const data = [];
      this.multipleSelection.map((item) => {
        data.push(item.sn);
      });
      this.sn = data;
    },
    setQuery(key) {
      if (this.$route.query.hasOwnProperty(key)) {
        this.listQuery[key] = this.$route.query[key];
      } else {
        this.listQuery[key] = "";
      }
    },
    async setMode() {
      await this.getList();
      // await this.setOneClickRepair();
      // await this.getList();
    },
    async setOneClickRepair() {
      let arr = this.list.map((res) => {
        return res.id;
      });
      await this.onOneClickRepair({ id: arr.join() });
    },
    async getList($is_excel) {
      this.listQuery.excel = null;
      this.listQuery.is_refunded = 1;
      if ($is_excel == 1) {
        this.listQuery.excel = 1;
        const isdate = this.listQuery.times[0] instanceof Date;
        const params = {
          ...this.listQuery,
          times: [
            isdate ? this.listQuery.times[0].toISOString() : "",
            isdate ? this.listQuery.times[1].toISOString() : "",
          ],
        };
        window.open("/admin/order-xs/index?" + this.objectToQuery(params));
        return;
      }

      await this.$axios
        .get("/admin/order-xs/index", { params: this.listQuery })
        .then((response) => {
          this.list = response.data.data;
          this.total = response.data.total;
          (this.timetype_arr = response.ext.timetype),
            (this.oss = response.ext.oss);
          this.listLoading = false;
          this.isSynchronization = true;
        });
    },
    objectToQuery(obj) {
      return Object.keys(obj)
        .map((key) => {
          const value = obj[key];
          if (value == undefined || value == null) return "";
          return encodeURIComponent(key) + "=" + encodeURIComponent(value);
        })
        .join("&");
    },
    onInfo(item) {
      this.value = null;
      this.next_follow = null;
      this.$set(item, "next_follow", null);
      // this.item = item;
      this.active = "follow";
      this.$axios
        .get("/admin/order-xs/info", { params: { id: item.id } })
        .then((res) => {
          this.item = res.data;
          this.dialogVisible = true;
        })
        .catch((err) => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    getAdminList(typeDesc = "") {
      this.$axios
        .get("/admin/admin/index", {
          params: { limit: 100, status: 1, is_order: 1, type_desc: typeDesc },
        })
        .then((response) => {
          this.adminList = response.data.data;
          this.listLoading = false;
        })
        .catch((err) => {});
    },
    onCirculation(item) {
      this.getAdminList(item.category_desc);
      this.applyVisible = true;
      this.isAll = false;
      this.item3 = { ...item, os: Number(item.os) };
      console.log(this.item3);
      if (this.item3.backs && this.item3.backs.admin_id) {
        this.item3.flowObj = this.item3.backs.admin_id;
      } else {
        this.resetForm("ruleForm");
      }
    },
    // 批量设置
    onCirculationAll() {
      this.applyVisible = true;
      this.isAll = true;
    },
    //确定
    onCirculationSave(to_admin_id) {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          let res;
          if (this.isAll) {
            this.$confirm("是否批量流转订单", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then(async () => {
              res = await orderbackBatch({
                sn: this.sn,
                to_admin_id: to_admin_id,
              });
              if (res.data) {
                this.$message({
                  message: "批量流转订单成功",
                  type: "success",
                });
                this.applyVisible = false;
                this.isAll = false;
                this.getList();
              }
            });
          } else {
            res = await orderBack({
              sn: this.item3.sn,
              os: this.item3.os,
              to_admin_id: to_admin_id,
            });
            if (res.data) {
              this.$message({
                message: "流转订单成功",
                type: "success",
              });
              this.applyVisible = false;
              this.isAll = false;
              this.getList();
            }
          }
        } else {
          return false;
        }
      });
    },
    // 取消
    onCancel() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$axios
            .post("/admin/order-xs/backcancel", { id: this.item3.id })
            .then((res) => {
              this.applyVisible = false;
              this.isAll = false;
              this.getList();
            })
            .catch((err) => {
              console.log(err);
            });
        } else {
          return false;
        }
      });
    },
    onBack() {
      this.$axios
        .post("/admin/order-xs/back", this.item)
        .then((res) => {
          this.dialogVisible = false;
          this.item = {};
          this.getList();
        })
        .catch((err) => {});
    },
    onSave(item) {
      this.$axios
        .post("/admin/order-xs/save", {
          id: item.id,
          check_sn: item.check_sn,
          is_wechat: item.is_wechat,
          travel_end: item.travel_end,
          travel_date: item.travel_date,
          desc: item.desc,
          status: item.status,
          next_follow: this.next_follow,
          personnel: this.item.personnel,
        })
        .then((res) => {
          this.dialogVisible = false;
          this.item = { next_follow: "", personnel: { adult: "" } };
          this.$router.push({
            path: "/order/refunded",
          });
        })
        .catch((err) => {});
    },
    onPass(form) {
      this.$axios
        .post("/admin/order-xs/pass", { check_sn: form.check_sn })
        .then((res) => {
          this.dialog2Visible = false;
          this.form = {};
        })
        .catch((err) => {});
    },
    onChange(from) {
      this.$set(
        this.item,
        "desc",
        from + (this.item.desc != undefined ? this.item.desc : "")
      );
    },
    onChange2(from) {
      this.$set(
        this.item,
        "to_admin_id",
        from + (this.item.admin_id != undefined ? this.item.admin_id : "")
      );
    },
    handleChange(os) {
      console.log(os);
    },
    getShortcutContent() {
      this.listLoading = true;
      this.$axios
        .get("/admin/shortcutContent/list", {
          params: { page: 1, limit: 50, status: 1 },
        })
        .then((response) => {
          for (const r of response.data.data) {
            this.options.push({ value: r.id, label: r.content });
          }
        })
        .catch(() => {});
    },
    onOneClickRepair(item) {
      // this.$axios
      //   .post("/admin/order/oneClickRepair", { id: item.id })
      //   .then((res) => {
      //     this.$notify({
      //       title: "成功",
      //       message: "同步完成",
      //       type: "success",
      //     });
      //     // this.getList();
      //   })
      //   .catch((err) => {
      //     this.$notify.error({
      //       title: "同步失败",
      //       message: err,
      //     });
      //   });
    },
    onOneClickYyHandle(item) {
      this.$axios
        .post("/admin/order-xs/changeAppointmentStatus", { id: item.id })
        .then((res) => {
          this.dialogVisible = false;
          this.$notify({
            title: "成功",
            message: "已处理",
            type: "success",
          });
          // this.getList();
        })
        .catch((err) => {
          this.$notify.error({
            title: "错误",
            message: err,
          });
        });
    },
  },
};
</script>

<style scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 5px; /* 分页条的高度，以避免内容重叠 */
}
</style>
