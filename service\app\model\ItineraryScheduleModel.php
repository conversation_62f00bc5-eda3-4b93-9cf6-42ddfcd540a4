<?php

declare(strict_types=1);

namespace app\model;


class ItineraryScheduleModel extends ItineraryBase
{
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'itinerary_schedule';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'id',
        'itinerary_id',
        'itinerary_day_id',
        'day',
        'activities',
        'images',
        'type',
        'start_time',
        'location',
        'duration',
        'create_time',
        'update_time',
        'delete_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'id' => 'integer',
        'day' => 'integer',
        'duration' => 'integer',
        'images' => 'json',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer'
    ];

    /**
     * 获取行程安排列表
     * @param string|int $itineraryId 行程ID
     * @return array
     */
    public function getSchedulesByItineraryId($itineraryId): array
    {
        return $this->where('itinerary_id', $itineraryId)
            ->order('day', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 获取行程安排详情
     * @param int $id 行程安排ID
     * @return array|null
     */
    public function getDetail(int $id): ?array
    {
        $data = $this->where('id', $id)->find();
        return $data ? $data->toArray() : null;
    }

    /**
     * 批量添加行程安排
     * @param array $schedules 行程安排数据
     * @return bool
     */
    public function batchSave(array $schedules): bool
    {
        return $this->saveAll($schedules) ? true : false;
    }
}
