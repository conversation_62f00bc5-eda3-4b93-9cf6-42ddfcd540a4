<?php

namespace app\command;

use app\api\controller\DyNotifyController;
use app\model\AdminOperationLogs;
use app\model\Admins;
use app\model\Blacks;
use app\model\CityInfoModel;
use app\model\ItineraryModel;
use app\model\Line;
use app\model\Orders;
use app\model\OrdersXs;
use app\model\ProductCategoryModel;
use app\model\Products;
use app\server\Douyin;
use app\server\DyApiService;
use app\server\SMS;
use app\utils\ExcelUtil;
use Carbon\Carbon;
use http\Exception\InvalidArgumentException;
use support\Redis;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;


class Test extends Command
{
    protected static $defaultName = 'test';
    protected static $defaultDescription = '测试用3';

    /**
     * @return void
     */
    protected function configure()
    {
        // $this->addArgument('name', InputArgument::OPTIONAL, 'Name description');
    }

    public function _curl($url, $params, $method = 'GET') {
        $ch = curl_init($url);

        $header = [
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Sec-Ch-Ua-Platform: "Windows"',
            'Host: life.douyin.com',
            'Sec-Ch-Ua: "Google Chrome";v="123", "Not:A-Brand";v="8", "Chromium";v="123"',
            'Accept-Language: zh-CN,zh;q=0.9',
            'Content-type: application/json'
        ];
        $cookie = "__live_version__=%221.1.2.6176%22; live_use_vvc=%22false%22; hevc_supported=true; is_hit_partitioned_cookie_canary=true; is_hit_partitioned_cookie_canary=true; is_hit_partitioned_cookie_canary_ss=true; is_hit_partitioned_cookie_canary_ss=true; s_v_web_id=verify_m9kz6b4e_WYB2hj7y_XBun_44uB_AmSm_JQVRSi92TT1A; gfkadpd=299467,22075; csrf_session_id=ed014f6f4d86c33439efc6ff6e13d545; passport_csrf_token=72d286637bcff7a9e3b97fb01a99b386; passport_csrf_token_default=72d286637bcff7a9e3b97fb01a99b386; passport_auth_status_ls=db2fe65f45e4b01e92b57a50503fd86f%2C; passport_auth_status_ss_ls=db2fe65f45e4b01e92b57a50503fd86f%2C; sid_guard_ls=8e6efeb57a21fe3c9f986199621131cf%7C1745484754%7C5184002%7CMon%2C+23-Jun-2025+08%3A52%3A36+GMT; sid_guard_ls=8e6efeb57a21fe3c9f986199621131cf%7C1745484754%7C5184002%7CMon%2C+23-Jun-2025+08%3A52%3A36+GMT; uid_tt_ls=4ed0e6c149629daa01e760a5b06bc67a; uid_tt_ls=4ed0e6c149629daa01e760a5b06bc67a; uid_tt_ss_ls=4ed0e6c149629daa01e760a5b06bc67a; uid_tt_ss_ls=4ed0e6c149629daa01e760a5b06bc67a; sid_tt_ls=8e6efeb57a21fe3c9f986199621131cf; sid_tt_ls=8e6efeb57a21fe3c9f986199621131cf; sessionid_ls=8e6efeb57a21fe3c9f986199621131cf; sessionid_ls=8e6efeb57a21fe3c9f986199621131cf; sessionid_ss_ls=8e6efeb57a21fe3c9f986199621131cf; sessionid_ss_ls=8e6efeb57a21fe3c9f986199621131cf; is_staff_user_ls=false; is_staff_user_ls=false; sid_ucp_v1_ls=1.0.0-KGZiYzBjODA4Y2RlYmRmY2M1Mzc0NjQ2ZjA0MGU5Yzg2ZDhjMjgwOTMKGgiuveDavoyyBRDS96fABhjRwRIgDDgCQPEHGgJobCIgOGU2ZWZlYjU3YTIxZmUzYzlmOTg2MTk5NjIxMTMxY2Y; sid_ucp_v1_ls=1.0.0-KGZiYzBjODA4Y2RlYmRmY2M1Mzc0NjQ2ZjA0MGU5Yzg2ZDhjMjgwOTMKGgiuveDavoyyBRDS96fABhjRwRIgDDgCQPEHGgJobCIgOGU2ZWZlYjU3YTIxZmUzYzlmOTg2MTk5NjIxMTMxY2Y; ssid_ucp_v1_ls=1.0.0-KGZiYzBjODA4Y2RlYmRmY2M1Mzc0NjQ2ZjA0MGU5Yzg2ZDhjMjgwOTMKGgiuveDavoyyBRDS96fABhjRwRIgDDgCQPEHGgJobCIgOGU2ZWZlYjU3YTIxZmUzYzlmOTg2MTk5NjIxMTMxY2Y; ssid_ucp_v1_ls=1.0.0-KGZiYzBjODA4Y2RlYmRmY2M1Mzc0NjQ2ZjA0MGU5Yzg2ZDhjMjgwOTMKGgiuveDavoyyBRDS96fABhjRwRIgDDgCQPEHGgJobCIgOGU2ZWZlYjU3YTIxZmUzYzlmOTg2MTk5NjIxMTMxY2Y; odin_tt=ffd37ba4c635b648f72e4d1287e0cb4677ded67b2a664694199a15eba9076e6b1124cb05878274d565ddb0018b6415ac; ttwid=1%7CLdSlokAyNxhJ1Sn2YkOYR0H6NmP-4GMwFcqUAcdqcdc%7C1745484757%7C18a0c693479d61db5550ac5d14cfaec7b09bd5f7d0f73aa02200b84693f0a808";
        if ($method == 'GET' || $method == 'POST') {
            $header[] = 'x-secsdk-csrf-token: 0001000000014b67c82ac53b3450c57f85825dcaaca38a152ba53c77c035ef79e73887025893183f61d6690629b2';
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        } else {
            curl_setopt($ch, CURLOPT_HEADER, 1);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $header[] = 'x-secsdk-csrf-request: 1';
        }
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_COOKIEFILE, './runtime5_dy_cookie.txt');
        if ($method == 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        }
        $body = curl_exec($ch);
        curl_close($ch);

        $res = json_decode($body);
        return $res;
    }

    public function test($order) {
        $order->a = 1;
    }

    /**
     * 判断工作时间是否在上午，且当前时间早于当日13:30
     * @param int $workStartTimestamp 精确到秒的时间戳
     * @return bool
     */
    private static function checkWorkTimeCondition(int  $workStartTimestamp): bool {
        $isLegal = false;
        try {
            date_default_timezone_set('Asia/Shanghai');
            if ($workStartTimestamp < 0) {
                throw new InvalidArgumentException("时间戳不能为负数");
            }
            $now = Carbon::now();
            $startToday = Carbon::now()->setTime(9, 10, 0);
            $deadlineToday = Carbon::now()->setTime(22, 0);
            if ($now > $startToday && $now < $deadlineToday) {
                $isLegal = true;
            }

            //            $workStart = (new \DateTime())->setTimestamp($workStartTimestamp);
            //            $isMorning = (int)$workStart->format('H') < 12; // 是否是早班
            //            $currentTime = new \DateTime('now');
            //            $startToday = (clone $currentTime)->setTime(9, 10, 0);
            //            $deadlineToday = (clone $currentTime)->setTime(13, 30, 0);
            //
            //            if ($isMorning && $currentTime > $startToday && ($currentTime < $deadlineToday)) {
            //                $isLegal = true;
            //            }
            //
            //            // 下午上线的用户，2.10开始分单
            //            $startAfternoon = (new $currentTime)->setTime(14, 10);
            //            if (!$isMorning && $currentTime > $startAfternoon) {
            //                $isLegal = true;
            //            }
        } catch (\Exception $e) {
            throw new InvalidArgumentException("时间处理错误: " . $e->getMessage());
        }

        return $isLegal;
    }
    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int {
        // 检查行程是否存在
        $itineraryModel = new ItineraryModel();
        $itinerary = $itineraryModel->where('id', 20)->where('delete_time', 0)->find();

        // 如果没有数据，返回默认分类数据
//        if (empty($tree)) {
//            $tree = $this->getDefaultCategoryTree();
//        }

        var_dump($itinerary);
        exit;

//        $action = 'notice';//模板名称
//        $mobile = '15711970154';//手机号
//        $params = ['title' => '港澳一日游', 'mob' => '15711970154'];
//        $areacode = "";//国际区号,腾讯云选传,其他不传
//        $sms = new \Hzdad\Wbsms\Wbsms('aliyun');//传入短信服务商名称, 腾讯云 qcloud , 阿里云 aliyun, 七牛 qiniu, 华为 huawei
//        $result = $sms->sendsms($action,$mobile,$params,$areacode);
//
//        if ($result['code'] == 200) {
//            echo '发送成功';
//        } else {
//            echo $result['msg'];
//        }
//        return 0;
        $order = Orders::where('sn', '1085054036934425372')->find();
        $admin_id = $order->admin_id;
        $user = Admins::cache(true)->where('id', $admin_id)->find();
//        if ((!config('app.debug', true) || config('app.debug', true) === 'false') && (time() * 1000 - $order->create_at) / 1000 < 2 * 24 * 3600) {
            $has = Blacks::where('mobile', $order->mobile)->find();
            if (empty($has) && !empty($order->mobile)) {
                $title = str_replace(['[', ']', '【', '】'], [''], $order->product_name);
                $res = SMS::juhe_sms_send('15711970154', 261607, ['title' => $title, 'mobile' => $user->mobile]);
                var_dump($res);
            } else {
                sleep(10);
            }

//        } else {
//            print_r([$order->mobile, 261607, ['title' => $order->product_name, 'mobile' => $user->mobile]]);
//        }
//        $encryptStr = (new DyApiService())->encrypt('这是一个测试', env('DY_APPSECRET'));
//        $output->write('加密字符串:' . $encryptStr);
//
//        $decryptStr = (new DyApiService())->decrypt($encryptStr, env('DY_APPSECRET'));
//        $output->write('解密字符串:' . $decryptStr);

        return 0;
    }
}


