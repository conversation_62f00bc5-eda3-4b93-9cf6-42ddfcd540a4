<template>
    <p class="desc_conten" ref="desc_conten">{{ content }}<el-button @click="open(content)" class="desc_btn" v-show="isTruncated" type="primary">查看</el-button></p>
</template>

<script>
export default {
  name: 'overflowConcealment',
  data() {
    return {
        isTruncated:false
    }
  },
  props:{
    content:{
      type:String,
      default:''
    }
  },
  mounted() {
    this.checkTruncation()
    window.addEventListener('resize',this.checkTruncation)
  },
  destroyed() {
    window.removeEventListener('resize',this.checkTruncation)
  },
  methods: {
    open(val){
      this.$alert(val, '内容', {
        confirmButtonText: '确定',
        // showConfirmButton:false
      });
    },
    // 检查是否溢出
        checkTruncation() {
            this.isTruncated =  this.$refs['desc_conten'].offsetWidth<this.$refs['desc_conten'].scrollWidth
        },
  }
}
</script>

<style scoped lang="scss">
.desc_conten{
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    .desc_btn{
        position: absolute;
        right: 0;
    }
    // text-overflow: ellipsis;
}
</style>