<template>
  <div class="app-container">
    <div class="mb10">
      <el-button
        size="small"
        class="filter-item"
        type="primary"
        @click="handleAdd('')"
        >添加</el-button
      >
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" fixed width="200" label="操作">
        <template slot-scope="scope">
          <el-button
            size="small"
            class="filter-item"
            type="primary"
            @click="handleAdd(scope.row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>

      <el-table-column align="center" fixed label="标题" prop="title" />

      <el-table-column align="center" fixed label="内容" prop="content" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
    <el-dialog :title="dialogTitle" :visible.sync="dialog2Visible">
      <el-form ref="addForm" label-width="160px" :model="form" :rules="rules">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input v-model="form.content" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onPass(form)">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/PaginationFixed";
export default {
  name: "Announlist",
  components: { Pagination },
  data() {
    return {
      active: "follow",
      order_status: [
        "#9e9f9c",
        "#04bcd9",
        "#fc9904",
        "#1193f4",
        "#48b14b",
        "#eb1662",
        "#9d1cb5",
      ],
      follow_status: [
        "#9e9f9c",
        "#04bcd9",
        "#fc9904",
        "#1193f4",
        "#48b14b",
        "#eb1662",
      ],
      options: [],
      value: null,
      next_follow: null,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        times: [],
        status: null,
        admin: null,
        zhubo: null,
        os_status: [],
      },
      item: { next_follow: "", personnel: {} },
      follow: [],
      dialog2Visible: false,
      oss: [],
      dialogTitle: "添加",
      adminList: [],
      form: {
        title: "",
        content: "",
      },
      rules: {
        title: [{ required: true, message: "请输入标题", trigger: "change" }],
      },
    };
  },
  created() {
    this.listQuery.zhubo = this.$route.query.zhubo || null;
    if (this.$route.query.start && this.$route.query.end) {
      this.listQuery.times = [this.$route.query.start, this.$route.query.end];
    }
    this.getList();
  },
  methods: {
    getList($is_excel) {
      this.$axios
        .get("/admin/announcements/index", { params: this.listQuery })
        .then((response) => {
          this.list = response.data.data;
          this.total = response.data.total;
          this.listLoading = false;
        });
    },
    handleAdd(val) {
      this.dialog2Visible = true;
      this.$nextTick(() => {
        this.$refs["addForm"].resetFields();
        if (val) {
          this.dialogTitle = "编辑";
          this.form = {
            title: val.title,
            content: val.content,
            id: val.id,
          };
        }
      });
    },
    onPass() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.$axios
            .post(
              this.dialogTitle == "编辑"
                ? "/admin/announcements/edit"
                : "/admin/announcements/add",
              this.form
            )
            .then((res) => {
              this.$message({
                message: this.dialogTitle == "编辑" ? "编辑成功" : "添加成功",
                type: "success",
              });
              this.dialog2Visible = false;
              this.getList();
            });
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
};
</script>

<style scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 52px; /* 分页条的高度，以避免内容重叠 */
}
.mb10 {
  margin-bottom: 10px;
}
</style>
