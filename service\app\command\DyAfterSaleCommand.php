<?php

namespace app\command;

use app\model\Admins;
use app\model\LiveRoomWorks;
use app\model\OrderAfterSales;
use app\model\Orders;
use app\model\Sales;
use app\model\Works;
use app\server\Douyin;
use Carbon\Carbon;
use support\Log;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputDefinition;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use think\facade\Db;

class DyAfterSaleCommand extends Command
{

    protected static $defaultName = 'DyAfterSaleCommand';
    protected static $defaultDescription = '抖音售后列表';

    protected function configure()
    {
        $this
            ->setName('DyAfterSaleCommand')
            ->setDescription('抖音售后列表')
            ->setDefinition(
                new InputDefinition(array())
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int {
        $output->writeln('DyAfterSaleCommand start');
        $douYinService = new Douyin(5);
        $res = $douYinService->bookSearch('', 6, 1, 50);
        $searchRes = json_decode(json_encode($res), true);
        Log::info('DyAfterSaleCommand searchRes:' . json_encode($searchRes));
        if (empty($searchRes) || $searchRes['status_code'] != 0) {
            return 1;
        }

        if (isset($searchRes['order_list']) && is_array($searchRes['order_list'])) {
            foreach ($searchRes['order_list'] as $order) {
                $orderInfo = $order['order_info'];
                $saleAfterInfo = $order['cancel_book_after_sale_info'];
                $afterSaleData = [
                    'order_id' => $orderInfo['order_id'],
                    'book_id' => $order['book_info']['book_id'],
                    'audit_order_id' => $saleAfterInfo['audit_order_id'],
                    'after_sale_type' => $saleAfterInfo['after_sale_type'],
                    'after_sale_type_agg_name' => $saleAfterInfo['after_sale_type_agg_name'],
                    'refund_amount' => bcdiv($saleAfterInfo['refund_amount'] ?: $orderInfo['pay_amount'], 100, 2),
                    'refund_type' => $saleAfterInfo['refund_type'],
                    'after_sale_reason' => implode(',', $saleAfterInfo['after_sale_reason']['reason_list']),
                    'after_sale_apply_time_ms' => $saleAfterInfo['after_sale_apply_time_ms'], // 申请时间
                    'audit_expire_time_ms' => $saleAfterInfo['audit_expire_time_ms'], // 售后时间
                ];

                $afterSale = OrderAfterSales::query()->where(['audit_order_id' => $afterSaleData['audit_order_id']])->find();
                if (!empty($afterSale)) {
                    OrderAfterSales::query()->where(['audit_order_id' => $afterSaleData['audit_order_id']])->update($afterSaleData);
                } else {
                    OrderAfterSales::query()->insert($afterSaleData);
                }
            }
        }

        $output->writeln('DyAfterSaleCommand end');
        return 1;
    }
}
