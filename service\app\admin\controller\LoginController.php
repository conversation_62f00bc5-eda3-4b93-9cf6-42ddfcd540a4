<?php
namespace app\admin\controller;

use app\model\AdminLogs;
use app\model\Admins;
use Firebase\JWT\JWT;
use support\Redis;
use Webman\Http\Request;

class LoginController extends base
{
    public function Index(Request $request) {
        $time = time();

        $username = $request->post('username');
        $password = $request->post('password');
        if(empty($username)) {
            return $this->error(1001);
        }
        if(empty($password)) {
            return $this->error(1002);
        }

        $admin = Admins::where('username', $username)->find();
        // $admin->password = 'Aa123456';
        // $admin->save();
        if(empty($admin)) {
            return $this->error(1002,'没有找到管理员');  //没有找到管理员
        }

        if(!$admin->checkPwd($password)) {
            return $this->error(1003,'密码校验错误');  //密码校验错误
        }
        $changePasswd = false;
        if (!preg_match('/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$/', $password)) {
            $changePasswd = true;
        }

        if($admin->status == 0) {
            return $this->error(1004,'管理员状态错误');  //管理员状态错误
        }

        // 记录当天第一次上线时间
        $remoteIp = $request->getRealIp();
        $remoteIpAddress = get_ip_address($remoteIp);
        if (date('Y-m-d') != date('Y-m-d',$admin->start_work_time)){
            $admin->start_work_time = $time;
        }
        $admin->ip = $remoteIp;
        $admin->ip_address = $remoteIpAddress;
        $admin->save();

        AdminLogs::create([
            'admin_id' => $admin->id,
            'ip' => $remoteIp,
            'ip_address' => $remoteIpAddress,
        ]);

        $data = [
            'id'=>  $admin->id,
            'username' => $admin->username,
            'name' => $admin->name,
            'avatar' => $admin->avatar,
            'is_super' => $admin->is_super,
            'is_franchisee' => $admin->is_franchisee,
            'shop_id' => $admin->shop_id,
            'is_private' => $admin->is_private,
        ];

        $payload = array(
            "iat" => $time,  // token 的创建时间
            "nbf" => $time,  // token 的生效时间
            "exp" => $time+3600*24,  // token 的过期时间
            "data"=> $data  // 携带数据
        );

        $keyId = "keyId";
        $token = JWT::encode($payload, config('app.jwt_key_admin'), 'HS256', $keyId);

        return $this->success(['token'=> $token, 'change_passwd' => $changePasswd]);
    }

    public function Logout(Request $request) {
        $md5 = md5($request->token);
        Redis::set('Admin:logout:'.$md5, time() , 'EX', 3600*24);
        setcookie('Admin-Token', '', time() - 3600);
        return $this->success(null);
    }
}
