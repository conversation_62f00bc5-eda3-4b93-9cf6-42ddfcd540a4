<template>
  <div class="app-container">

    <div class="filter-container">
      <el-date-picker
        class="filter-item"
        v-model="listQuery.times"
        type="datetimerange"
        :default-time="['00:00:00', '23:59:59']"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期">
      </el-date-picker>
      <el-select v-model="listQuery.os" filterable placeholder="请选择" class="filter-item" >
        <el-option
          key=""
          label="请选择"
          value="">
        </el-option>
        <el-option
          v-for="(v,k) in oss"
          :key="k"
          :label="v"
          :value="k">
        </el-option>
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getList">
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList(1)"
      >
        导出
      </el-button>
    </div>

    <el-table v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">

      <el-table-column align="center" fixed label="姓名" width="120" prop="admin.name" />

      <el-table-column align="center" label="订单数" width="120" prop="orders" />

      <el-table-column align="center" label="订单总金额">
        <template slot-scope="scope">
          {{ scope.row.total_price/100 }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="核销数" width="120" prop="assets" />

      <el-table-column align="center" label="核销金额">
        <template slot-scope="scope">
          {{ scope.row.asset_price/100 }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="退款订单" width="120" prop="nopays" />

      <el-table-column align="center" label="退款金额">
        <template slot-scope="scope">
          {{ scope.row.nopay_price/100 }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="核销率(按订单)" width="160">
        <template slot-scope="scope">
          {{ scope.row.write_rate }}%
        </template>
      </el-table-column>

      <el-table-column align="center" width="180" label="核销率(按销售额)">
        <template slot-scope="scope">
          {{ scope.row.write_rate_price }}%
        </template>
      </el-table-column>

      <el-table-column align="center" label="当月核销数量(按核销时间)" width="160">
        <template slot-scope="scope">
          {{ scope.row.month_write_num }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="当月核销率(按订单)" width="160">
        <template slot-scope="scope">
          {{ scope.row.month_write_rate }}%
        </template>
      </el-table-column>

      <el-table-column align="center" width="180" label="当月核销率(按销售额)">
        <template slot-scope="scope">
          {{ scope.row.month_write_rate_price }}%
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>

export default {
  name: 'Datalist',
  data() {
    return {
      oss: null,
      list: [],
      listLoading: true,
      listQuery:{}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList(is_excel) {
      if (is_excel == 1) {
        this.listQuery.excel = 1;
        if (!this.listQuery.times) {
          this.$message({
            message: "请选择日期",
            type: "warning",
          });
          return;
        }
        
        const isdate = this.listQuery.times[0] instanceof Date;
        const params = {
          ...this.listQuery,
          times: [
            isdate ? this.listQuery.times[0].toISOString() : "",
            isdate ? this.listQuery.times[1].toISOString() : "",
          ],
        };
        window.open("/admin/data/index?" + this.objectToQuery(params));
        return;
      }

      this.listQuery.excel = 0;
      this.$axios.get('/admin/data/index', { params: this.listQuery }).then(response => {
        this.list = response.data
        this.oss = response.ext.oss
        this.listLoading = false
      })
    },
      objectToQuery(obj) {
        return Object.keys(obj)
          .map((key) => {
            const value = obj[key];
            if (value == undefined || value == null) return "";
            return encodeURIComponent(key) + "=" + encodeURIComponent(value);
          })
          .join("&");
      },
  }
}
</script>
