import { asyncRoutes, constantRoutes } from '@/router'
import Vue from 'vue'

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  
  // 更新路由的dot值
  UPDATE_ROUTE_DOTS: (state, { routeName, dotValue }) => {
    // 更新路由dot值的辅助函数
    function updateRouteDot(routes, name, value) {
      if (!routes || !routes.length) return false
      
      for (let i = 0; i < routes.length; i++) {
        const route = routes[i]
        if (route.name === name) {
          if (!route.meta) {
            Vue.set(route, 'meta', {})
          }
          Vue.set(route.meta, 'dot', value)
          return true
        }
        
        // 递归检查子路由
        if (route.children && route.children.length) {
          const found = updateRouteDot(route.children, name, value)
          if (found) return true
        }
      }
      return false
    }
    
    // 更新各个路由集合中的dot值
    updateRouteDot(state.routes, routeName, dotValue)
    updateRouteDot(state.addRoutes, routeName, dotValue)
    // 更新原始路由配置
    updateRouteDot(constantRoutes, routeName, dotValue)
    updateRouteDot(asyncRoutes, routeName, dotValue)
  }
}

/**
 * 递归设置路由的dot值
 * @param {Array} routes 路由数组
 * @param {Object} dotsMap 路由名称与dot值的映射
 */
function setRoutesDotsRecursively(routes, dotsMap) {
  if (!routes || !routes.length) return

  routes.forEach(route => {
    if (route.name && dotsMap.hasOwnProperty(route.name)) {
      // 设置路由的dot值
      if (!route.meta) route.meta = {}
      route.meta.dot = dotsMap[route.name]
    }
    
    // 递归处理子路由
    if (route.children && route.children.length) {
      setRoutesDotsRecursively(route.children, dotsMap)
    }
  })
}

const actions = {
  generateRoutes({ commit }, roles) {
    return new Promise(resolve => {
      let accessedRoutes
      if (roles.includes('admin')) {
        accessedRoutes = asyncRoutes || []
      } else {
        accessedRoutes = filterAsyncRoutes(asyncRoutes, roles)
      }
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  },
  
  // 设置路由红点
  setRouteDots({ state, commit }, dotsMap) {
    // 设置constantRoutes的dot
    setRoutesDotsRecursively(constantRoutes, dotsMap)
    
    // 设置asyncRoutes的dot
    setRoutesDotsRecursively(asyncRoutes, dotsMap)
    
    // 更新已经生成的路由的dot
    if (state.routes.length) {
      setRoutesDotsRecursively(state.routes, dotsMap)
    }
    
    if (state.addRoutes.length) {
      setRoutesDotsRecursively(state.addRoutes, dotsMap)
    }
  },
  
  // 更新单个路由的dot值
  updateRouteDot({ commit }, { name, value }) {
    commit('UPDATE_ROUTE_DOTS', { routeName: name, dotValue: value })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
