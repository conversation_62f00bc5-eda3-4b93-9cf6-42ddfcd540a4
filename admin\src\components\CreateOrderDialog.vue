<template>
  <el-dialog title="私域线路" :visible.sync="dialogVisible" width="600px">
    <el-form :model="formData" label-width="100px" :rules="rules" ref="orderForm">
      <el-form-item label="私域线路" prop="product_id">
        <el-select
          v-model="formData.product_id"
          filterable
          placeholder="请输入线路名称搜索"
          style="width: 100%"
        >
          <el-option
            v-for="item in lineList"
            :key="item.id"
            :label="item.product_name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入客户手机号码"></el-input>
      </el-form-item>
      
      <el-form-item label="微信号" prop="wechat">
        <el-input v-model="formData.wechat" placeholder="请输入客户微信号"></el-input>
      </el-form-item>
      
      <el-form-item label="等级" prop="level">
        <el-select
          v-model="formData.level"
          placeholder="请选择等级"
          style="width: 100%"
        >
          <el-option
            v-for="(levelName, levelId) in levelList"
            :key="levelId"
            :label="levelName"
            :value="levelId"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="来源" prop="source">
        <el-select
          v-model="formData.source"
          placeholder="请选择来源"
          style="width: 100%"
        >
          <el-option
            v-for="(sourceName, sourceId) in sourceList"
            :key="sourceId"
            :label="sourceName"
            :value="sourceId"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input 
          v-model="formData.remark" 
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        ></el-input>
      </el-form-item>
      
      <el-form-item label="凭证图片">
        <el-upload
          action=""
          list-type="picture-card"
          :multiple="false"
          :show-file-list="false"
          :http-request="handleImageUpload"
        >
          <img 
            v-if="formData.evidence_image" 
            :src="formData.evidence_image" 
            style="width: 120px; height: 120px; margin-top: 14px;"
          />
          <i v-else class="el-icon-plus"></i>
        </el-upload>
      </el-form-item>
    </el-form>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";

export default {
  name: 'CreateOrderDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      formData: {
        product_id: '',
        mobile: '',
        wechat: '',
        level: '',
        source: '',
        remark: '',
        evidence_image: ''
      },
      rules: {
        product_id: [
          { required: true, message: '请选择线路', trigger: 'change' }
        ],
        // mobile: [
        //   { required: true, message: '请输入手机号码', trigger: 'blur' },
        //   { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        // ],
        wechat: [],
        level: [
          { required: true, message: '请选择等级', trigger: 'change' }
        ],
        source: [
          { required: true, message: '请选择来源', trigger: 'change' }
        ]
      },
      lineList: [],
      levelList: {},
      sourceList: {},
      loading: false,
      upLoading: false,
      submitLoading: false,
      searchTimeout: null
    }
  },
  mounted() {
    this.fetchLineList();
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.fetchLineList();
        this.fetchParameters();
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false);
        this.$refs.orderForm && this.$refs.orderForm.resetFields();
        this.formData.evidence_image = '';
      }
    }
  },
  methods: {
    fetchLineList() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }
      
      this.searchTimeout = setTimeout(() => {
        this.loading = true;
        this.$axios.get('/admin/line/list', {
          params: {
            limit: 500,
            page: 1,
          }
        }).then(response => {
            this.$emit('data-shared', response.data.data);
            this.lineList = response.data.data;
        }).catch(error => {
          this.$message.error('获取线路列表失败');
          console.error(error);
        }).finally(() => {
          this.loading = false;
        });
      }, 300);
    },
    
    fetchParameters() {
      this.$axios.get('/admin/line/parameter')
        .then(response => {
          if (response.error === 0 && response.data) {
            this.levelList = response.data.levels || {};
            this.sourceList = response.data.source || {};
          }
        })
        .catch(error => {
          this.$message.error('获取参数列表失败');
          console.error(error);
        });
    },
    
    async handleImageUpload(file) {
      try {
        var formdata = new FormData();
        formdata.append("file", file.file);

        this.upLoading = true;
        const res = await this.$axios.post("/admin/upload/index", formdata, {
          headers: {
            "Content-type": "multipart/form-data",
            "X-Token": getToken(),
          },
        });
        
        if (res.data) {
          this.formData.evidence_image = `${window.location.protocol}//${window.location.host}${res.data}`;
        }
        
        this.upLoading = false;
      } catch (error) {
        this.$message.error('图片上传失败');
        console.error('error:', error);
        this.upLoading = false;
      }
    },
    
    submitForm() {
      this.$refs.orderForm.validate(valid => {
        if (valid) {
          this.submitLoading = true;
          this.$axios.post('/admin/line/addOrder', this.formData)
            .then(response => {
              if (response.error === 0) {
                this.$message.success('订单创建成功');
                this.dialogVisible = false;
                this.$emit('refresh');
              } else {
                this.$message.error(response.msg || '订单创建失败');
              }
            })
            .catch(error => {
              this.$message.error('订单创建失败');
              console.error(error);
            })
            .finally(() => {
              this.submitLoading = false;
            });
        }
      });
    }
  }
}
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style> 