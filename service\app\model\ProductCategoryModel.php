<?php

declare(strict_types=1);

namespace app\model;

use base\base\BaseModel;

class ProductCategoryModel extends ItineraryBase
{
    /**
     * 设置表名
     * @var string
     */
    protected $name = 'product_category';

    /**
     * 设置主键
     * @var string
     */
    protected $pk = 'category_id';

    /**
     * 允许写入的字段
     * @var array
     */
    protected $field = [
        'category_id',
        'parent_id',
        'category_name',
        'category_level',
        'access_status',
        'category_discard',
        'allow_access_scope',
        'no_access_scope',
        'detail_url',
        'price_limit',
        'sort_order',
        'create_time',
        'update_time',
        'delete_time'
    ];

    /**
     * 类型转换
     * @var array
     */
    protected $type = [
        'category_id' => 'integer',
        'parent_id' => 'integer',
        'category_level' => 'integer',
        'access_status' => 'integer',
        'category_discard' => 'boolean',
        'price_limit' => 'float',
        'sort_order' => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer',
        'delete_time' => 'integer'
    ];

    /**
     * 获取分类列表
     * @param array $where 查询条件
     * @return array
     */
    public function getList(array $where = []): array
    {
        return $this->where($where)
            ->order('sort_order', 'asc')
            ->select()
            ->toArray();
    }

    /**
     * 获取分类树
     * @param int $parentId 父级ID，0为顶级
     * @param bool $includeDiscard 是否包含废弃分类
     * @return array
     */
    public function getCategoryTree(int $parentId = 0, bool $includeDiscard = false): array
    {
        $where = ['parent_id' => $parentId];

        if (!$includeDiscard) {
            $where['category_discard'] = false;
        }

        $list = $this->where($where)
            ->order('sort_order', 'asc')
            ->order('category_id', 'asc')
            ->select()
            ->toArray();

        foreach ($list as &$item) {
            $item['children'] = $this->getCategoryTree($item['category_id'], $includeDiscard);
        }

        return $list;
    }

    /**
     * 获取分类详情
     * @param int $categoryId 分类ID
     * @return array|null
     */
    public function getDetail(int $categoryId): ?array
    {
        $data = $this->where('category_id', $categoryId)->find();
        return $data ? $data->toArray() : null;
    }

    /**
     * 获取分类完整路径
     * @param int $categoryId 分类ID
     * @return array
     */
    public function getCategoryPath(int $categoryId): array
    {
        $path = [];
        $category = $this->getDetail($categoryId);

        if ($category) {
            $path[] = $category;

            if ($category['parent_id'] > 0) {
                $parentPath = $this->getCategoryPath($category['parent_id']);
                $path = array_merge($parentPath, $path);
            }
        }

        return $path;
    }
}
