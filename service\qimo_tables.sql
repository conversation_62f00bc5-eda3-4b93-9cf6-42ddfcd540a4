-- 七陌云呼相关表结构

-- 七陌坐席配置表
CREATE TABLE `qimo_agents` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '坐席名称',
  `account_id` varchar(50) NOT NULL COMMENT '坐席账号ID',
  `username` varchar(50) NOT NULL COMMENT '登录用户名',
  `password` varchar(100) NOT NULL COMMENT '登录密码',
  `pbx_url` varchar(255) NOT NULL COMMENT 'PBX地址',
  `login_type` varchar(20) DEFAULT 'Local' COMMENT '登录类型：Local/Remote',
  `description` varchar(200) DEFAULT '' COMMENT '坐席描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '坐席状态：0-禁用，1-空闲，2-忙碌，3-通话中',
  `last_call_time` int(11) DEFAULT NULL COMMENT '最后通话时间',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_account_id` (`account_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_call_time` (`last_call_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='七陌坐席配置表';

-- 七陌通话记录表
CREATE TABLE `qimo_call_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_id` int(11) DEFAULT 0 COMMENT '操作管理员ID',
  `agent_id` int(11) DEFAULT 0 COMMENT '坐席ID',
  `caller_number` varchar(20) DEFAULT '' COMMENT '主叫号码',
  `callee_number` varchar(20) DEFAULT '' COMMENT '被叫号码',
  `direction` tinyint(1) DEFAULT 1 COMMENT '呼叫方向：1-外呼，2-内呼',
  `call_status` tinyint(1) DEFAULT 1 COMMENT '通话状态：1-呼叫中，2-已接听，3-已挂断，4-呼叫失败',
  `call_id` varchar(100) DEFAULT '' COMMENT '七陌通话ID',
  `session_id` varchar(100) DEFAULT '' COMMENT '会话ID',
  `start_time` int(11) DEFAULT NULL COMMENT '开始时间',
  `answer_time` int(11) DEFAULT NULL COMMENT '接听时间',
  `end_time` int(11) DEFAULT NULL COMMENT '结束时间',
  `duration` int(11) DEFAULT 0 COMMENT '通话时长（秒）',
  `record_url` varchar(500) DEFAULT '' COMMENT '录音文件URL',
  `event_data` text COMMENT '事件原始数据（JSON格式）',
  `create_time` int(11) DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_call_id` (`call_id`),
  KEY `idx_callee_number` (`callee_number`),
  KEY `idx_call_status` (`call_status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='七陌通话记录表';

-- 插入默认坐席数据（可选）
INSERT INTO `qimo_agents` (`name`, `account_id`, `username`, `password`, `pbx_url`, `login_type`, `description`, `status`, `create_time`) VALUES
('默认坐席1', 'agent001', 'agent001', 'password123', 'https://your-pbx-url.com', 'Local', '默认坐席配置', 1, UNIX_TIMESTAMP()),
('默认坐席2', 'agent002', 'agent002', 'password123', 'https://your-pbx-url.com', 'Local', '默认坐席配置', 1, UNIX_TIMESTAMP()); 