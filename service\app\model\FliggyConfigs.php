<?php
declare(strict_types=1);

namespace app\model;

/**
 * 飞猪配置模型
 */
final class FliggyConfigs extends base
{
    protected $table = 'fliggy_configs';
    
    // 环境常量
    const ENV_SANDBOX = 'sandbox';
    const ENV_PRODUCTION = 'production';
    
    // 状态常量
    const STATUS_ENABLED = 1;
    const STATUS_DISABLED = 2;
    
    /**
     * 根据app_key获取配置
     */
    public static function getByAppKey(string $appKey): ?self
    {
        return self::where('app_key', $appKey)
            ->where('status', self::STATUS_ENABLED)
            ->find();
    }
    
    /**
     * 获取app_secret
     */
    public static function getAppSecret(string $appKey): string
    {
        $config = self::getByAppKey($appKey);
        return $config ? $config->app_secret : '';
    }
    
    /**
     * 获取所有启用的配置
     */
    public static function getEnabledConfigs(): array
    {
        return self::where('status', self::STATUS_ENABLED)
            ->select()
            ->toArray();
    }
    
    /**
     * 创建或更新配置
     */
    public static function createOrUpdate(array $data): self
    {
        $config = self::getByAppKey($data['app_key']);
        
        if ($config) {
            // 更新现有配置
            $config->app_secret = $data['app_secret'];
            $config->env = $data['env'] ?? self::ENV_SANDBOX;
            $config->status = $data['status'] ?? self::STATUS_ENABLED;
            $config->description = $data['description'] ?? '';
            $config->update_at = time();
            $config->save();
        } else {
            // 创建新配置
            $config = new self();
            $config->app_key = $data['app_key'];
            $config->app_secret = $data['app_secret'];
            $config->env = $data['env'] ?? self::ENV_SANDBOX;
            $config->status = $data['status'] ?? self::STATUS_ENABLED;
            $config->description = $data['description'] ?? '';
            $config->create_at = time();
            $config->update_at = time();
            $config->save();
        }
        
        return $config;
    }
} 