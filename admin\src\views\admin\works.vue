<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.mobile"
        placeholder="手机号"
        style="width: 200px; margin-right: 10px"
        class="filter-item"
      />
      <el-date-picker
        v-model="listQuery.times"
        class="filter-item"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        :default-time="['00:00:00', '23:59:59']"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd HH:mm:ss"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList"
      >
        搜索
      </el-button>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-circle-plus"
        @click="onAdd"
      >
        添加主播
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" fixed label="操作" width="220">
        <template slot-scope="scope">
          <!-- <el-button
            type="danger"
            @click="onDel(scope.row)"
            size="small"
            icon="el-icon-delete-solid"
          >
            删除
          </el-button> -->

          <el-button
            type="primary"
            @click="onAdd(scope.row)"
            size="small"
            icon="el-icon-edit"
          >
            修改
          </el-button>
        </template>
      </el-table-column>

      <!-- <el-table-column align="center" label="ID" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column> -->

      <el-table-column align="center" label="用户名" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.username }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="姓名" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="手机号" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="岗位" width="160">
        <template slot-scope="scope">
          <span>{{ scope.row.type == 2 ? "主播" : "中控" }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="排班时间">
        <template slot-scope="scope">
          {{ scope.row.start | parseTime("{y}-{m}-{d} {h}:{i}") }} -
          {{ scope.row.end | parseTime("{y}-{m}-{d} {h}:{i}") }}
        </template>
      </el-table-column> -->

      <!-- <el-table-column align="center" label="平台">
        <template slot-scope="scope">
          <template v-for="i in scope.row.os">
            {{ oss[i] }}
          </template>
        </template>
      </el-table-column> -->

      <el-table-column align="center" label="订单数">
        <template slot-scope="scope">
          {{ scope.row.orders }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="直播时长(小时)">
        <template slot-scope="scope">
          {{ scope.row.work_time }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="订单金额">
        <template slot-scope="scope">
          {{ scope.row.total }}
        </template>
      </el-table-column>
      <el-table-column width="140px" align="center" label="创建时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.create_time | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>

      <!-- <el-table-column width="140px" align="center" label="修改时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.update_time | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column> -->
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
    <!-- 
    <el-dialog title="修改排班" :visible.sync="dialogWork">
      <el-form label-width="120px" :model="item">
        <el-form-item label="上班时间">
          <el-date-picker
            v-model="item.date"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="渠道">
          <el-checkbox-group v-model="item.oss">
            <el-checkbox v-for="(v, i, k) in oss" :label="i" :key="k">{{
              v
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onSave()">保 存</el-button>
      </div>
    </el-dialog> -->

    <el-dialog :title="title" :visible.sync="dialogCreate">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input placeholder="用户名" v-model="form.username"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input placeholder="姓名" v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mobile">
          <el-input placeholder="请输入手机号" v-model="form.mobile"></el-input>
        </el-form-item>
        <el-form-item label="岗位" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio :label="2">主播</el-radio>
            <el-radio :label="3">中控</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-loading="loading" @click="onSaves(form)"
          >保 存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination"; // Secondary package based on el-pagination

export default {
  name: "Works",
  components: { Pagination },
  data() {
    return {
      oss: {},
      list: null,
      title: "",
      total: 0,
      listLoading: true,
      loading: false,
      listQuery: {
        page: 1,
        limit: 20,
      },
      form: {
        username: "",
        name: "",
        mobile: "",
        type: 2,
      },
      dialogWork: false,
      dialogCreate: false,
      item: {},
      anchors: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.$axios
        .get("/admin/liveroom/zhuboStatistics", { params: this.listQuery })
        .then((response) => {
          this.list = response.data.data;
          this.total = response.data.total;
          this.listLoading = false;
        })
        .catch((err) => {});
    },
    onDel(item) {
      this.$axios
        .post("/admin/work/del", { id: item.id })
        .then((res) => {
          this.dialogVisible = false;
          this.getList();
        })
        .catch((err) => {});
    },
    // onSave(from) {
    //   if (from) {
    //     this.$set(from, "date", [new Date(from.start), new Date(from.end)]);
    //     this.item = from;
    //     this.item.os = from.os + "";
    //     this.dialogWork = true;
    //     return;
    //   }
    //   this.$axios
    //     .post("/admin/work/save", this.item)
    //     .then((response) => {
    //       this.dialogWork = false;
    //       this.getList();
    //     })
    //     .catch((err) => {});
    // },
    onAdd(item) {
      // this.anchors = res.data || [];
      this.dialogCreate = true;
      this.$nextTick((res) => {
        this.$refs["form"].resetFields();
        this.title = item.id ? "编辑" : "添加";
        this.form = item.id
          ? { ...item }
          : {
              username: "",
              name: "",
              mobile: "",
              type: 2,
            };
      });
    },
    onSaves() {
      this.$axios
        .post("/admin/admin/save", this.form)
        .then((response) => {
          this.$message({
            message: "成功",
            type: "success",
          });
          this.dialogCreate = false;
          this.getList();
        })
        .catch((err) => {});
    },
  },
};
</script>
