import request from "@/utils/request";
// 获取路线列表
export function getProductsList() {
  return request({
    url: "admin/products/list",
    method: "get",
  });
}

export function addProducts(data) {
  return request({
    url: "/admin/products/add",
    method: "post",
    data,
  });
}
// 获取商品列表
export function getProductsListsApi(data) {
  return request({
    url: `/admin/products/list?page=${data.page}&limit=${data.limit}&product_name=${data.product_name??''}&third_product_id=${data.third_product_id ?? ''}`,
    method: "get",
  });
}
// 添加商品
export function postAddProductApi(data) {
  return request({
    url: `/admin/products/add`,
    method: "post",
    data,
  });
}

// 商品排期详情
export function getProductSchedules(data) {
  return request({
    url: `/admin/products/productschedules?id=${data.id}&date=${data.date}`,
    method: "get",
  });
}
// 添加商品排期
export function addProductSchedulesApi(data) {
  return request({
    url: `/admin/products/addproductschedules`,
    method: "post",
    data,
  });
}
// 获取线路列表
export function getRoutesListsApi(data) {
  return request({
    url: `/admin/routes/list?page=${data.page}&limit=${data.limit}&route_name=${data.route_name??''}&third_product_id=${data.third_product_id ?? ''}`,
    method: "get",
  });
}
// 添加线路
export function postAddRouteApi(data) {
  return request({
    url: `/admin/routes/add`,
    method: "post",
    data,
  });
}
// 获取路线列表
export function getProductsListXs() {
  return request({
    url: "admin/products-xs/list",
    method: "get",
  });
}

export function addProductsXs(data) {
  return request({
    url: "/admin/products-xs/add",
    method: "post",
    data,
  });
}
// 获取商品列表
export function getProductsListsApiXs(data) {
  return request({
    url: `/admin/products-xs/list?page=${data.page}&limit=${data.limit}&product_name=${data.product_name??''}&third_product_id=${data.third_product_id ?? ''}`,
    method: "get",
  });
}
// 添加商品
export function postAddProductApiXs(data) {
  return request({
    url: `/admin/products-xs/add`,
    method: "post",
    data,
  });
}

// ==================== 行程管理相关API ====================

// 获取行程列表
export function getItineraryListApi(params) {
  return request({
    url: '/admin/itinerary/list',
    method: 'get',
    params
  });
}

// 获取行程详情
export function getItineraryDetailApi(id) {
  return request({
    url: `/admin/itinerary/detail/${id}`,
    method: 'get'
  });
}

// 新增行程
export function createItineraryApi(data) {
  return request({
    url: '/admin/itinerary/create',
    method: 'post',
    data
  });
}

// 更新行程
export function updateItineraryApi(id, data) {
  return request({
    url: `/admin/itinerary/update/${id}`,
    method: 'post',
    data
  });
}

// 删除行程
export function deleteItineraryApi(id) {
  return request({
    url: `/admin/itinerary/delete/${id}`,
    method: 'post'
  });
}

// 上架行程
export function onlineItineraryApi(id) {
  return request({
    url: `/admin/itinerary/online/${id}`,
    method: 'post'
  });
}

// 下架行程
export function offlineItineraryApi(id) {
  return request({
    url: `/admin/itinerary/offline/${id}`,
    method: 'post'
  });
}

// 获取分类树
export function getCategoryTreeApi() {
  return request({
    url: '/admin/itinerary/tree',
    method: 'get'
  });
}

// ==================== 库存管理相关API ====================

// 获取行程指定月份的库存数据
export function getItineraryInventoryMonthApi(params) {
  return request({
    url: '/admin/itinerary/inventory/month',
    method: 'get',
    params
  });
}

// 批量更新行程库存
export function batchUpdateItineraryInventoryApi(data) {
  return request({
    url: '/admin/itinerary/inventory/batch-update',
    method: 'post',
    data
  });
}

// 获取行程指定日期的库存数据
export function getItineraryInventoryDateApi(params) {
  return request({
    url: '/admin/itinerary/inventory/date',
    method: 'get',
    params
  });
}

// 更新行程库存
export function updateItineraryInventoryApi(data) {
  return request({
    url: '/admin/itinerary/inventory/batch-update',
    method: 'post',
    data
  });
}

// 更新单个行程库存
export function updateItineraryInventorySingleApi(data) {
  return request({
    url: '/admin/itinerary/inventory/update',
    method: 'post',
    data
  });
}

// 兼容旧版本API（保持向后兼容）
export function getItineraryInventoryApi(id, params) {
  return getItineraryInventoryMonthApi({
    itineraryId: id,
    ...params
  });
}

// ==================== 行程分类相关API ====================

// 获取分类列表
export function getItineraryCategoryListApi(params) {
  return request({
    url: '/admin/itinerary-category/list',
    method: 'get',
    params
  });
}

// 获取分类树
export function getItineraryCategoryTreeApi(params) {
  return request({
    url: '/admin/itinerary-category/tree',
    method: 'get',
    params
  });
}

// ==================== 城市相关API ====================

// 获取城市列表
export function getCityListApi() {
  return request({
    url: '/admin/city-info/json',
    method: 'get'
  });
}

// 商品排期详情
export function getProductSchedulesXs(data) {
  return request({
    url: `/admin/products-xs/productschedules?id=${data.id}&date=${data.date}`,
    method: "get",
  });
}
// 添加商品排期
export function addProductSchedulesApiXs(data) {
  return request({
    url: `/admin/products-xs/addproductschedules`,
    method: "post",
    data,
  });
}
// 获取线路列表
export function getRoutesListsApiXs(data) {
  return request({
    url: `/admin/routes-xs/list?page=${data.page}&limit=${data.limit}&route_name=${data.route_name??''}&third_product_id=${data.third_product_id ?? ''}`,
    method: "get",
  });
}
// 添加线路
export function postAddRouteApiXs(data) {
  return request({
    url: `/admin/routes-xs/add`,
    method: "post",
    data,
  });
}
// 合同管理列表
export function getContractListsApi() {
  return request({
    url: `/admin/setting/getContractSetting`,
    method: "get",
  });
}
// 保存合同信息
export function postSubmitContractApi(data) {
  return request({
    url: `/admin/setting/savecontractsetting`,
    method: "post",
    data,
  });
}
// 下载合同信息
export function postDownloadContractApi() {
  return request({
    url: `/admin/setting/getContractSetting`,
    method: "get",
  });
}

// 获取管理员列表
export function fetchAdminList(params) {
  return request({
    url: '/admin/admin/index',
    method: 'get',
    params
  });
}
