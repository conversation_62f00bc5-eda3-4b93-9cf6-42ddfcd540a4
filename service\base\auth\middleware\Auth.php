<?php

namespace base\auth\middleware;

use base\auth\AuthHttpClient;
use base\auth\UserInfo;
use base\exception\BaseException;
use DI\Attribute\Inject;
use ReflectionMethod;
use <PERSON>man\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;

/** 权限鉴权中间件 */
class Auth implements MiddlewareInterface
{
    #[Inject]
    public AuthHttpClient $httpClient;

    /** 处理请求 */
    /**
     * @param Request $request
     * @param callable $handler
     * @return Response
     * @throws BaseException
     * @throws \JsonException
     * @throws \ReflectionException
     */
    public function process(Request $request, callable $handler): Response
    {
        $method = new ReflectionMethod($request->controller, $request->action);

        $attributes = $method->getAttributes(\base\auth\Auth::class);

        $actionList = [];
        foreach ($attributes as $auth) {
            /** @var \base\auth\Auth $authInstance */
            $authInstance = $auth->newInstance();
            $actionList[] = $authInstance->get();
        }
        var_dump($actionList);
        $userinfo = [];
        if ($actionList) {
            // 获取Authorization头中的token
            $token = $this->getTokenFromHeaders();

            try {
                // 使用IAM API进行鉴权
                $result = $this->httpClient->enforceWithIamApi($actionList, $token);
                $userinfo = $result['userinfo'] ?? [];

                if (!$userinfo) {
                    throw new BaseException('userinfo is empty');
                }
            } catch (BaseException $e) {
              throw new BaseException('API鉴权失败:'. $e->getMessage());
            }
        }

        UserInfo::init($userinfo);
        return $handler($request);
    }

    /**
     * 获取Header头部authorization令牌
     *
     * @throws BaseException
     */
    private function getTokenFromHeaders(): string
    {
        $authorization = request()?->header('authorization');

        if (empty($authorization)) {
            throw new BaseException('authorization not found');
        }

        return $authorization;
    }
}