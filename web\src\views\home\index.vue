<template>
  <el-row style="margin-top: 200px;">
    <el-col :span="2">
      <div class="grid-content ep-bg-purple image-container">
        <img src="../../../public/images/3.jpeg" alt="Product Image" class="product-image">
      </div>
    </el-col>
    <el-col :span="5" class="text-container" style="padding-left: 5px">
      <div class="grid-content ep-bg-purple-light" style="font-weight: bold;color: #616161;font-size: 18px">
        青季酒店(上海曹路民雷路地铁站上川路店)
      </div>
      <div class="grid-content ep-bg-purple-light">
        <span
            style="border-radius: 8px 2px 8px 8px;background-color: #4c4cec;color: #f2f2f2">&nbsp;&nbsp;5.0&nbsp;</span>
        <span style="border-radius: 3px 8px 8px 3px;background-color: #9999a8;color: #f2f2f2">&nbsp;超棒&nbsp;</span>
        <span style="color: #4c4cec">&nbsp;&nbsp;200+&nbsp;消费</span>
      </div>
      <div class="grid-content ep-bg-purple-light">
        <span>距离你直线<500m</span>
      </div>
      <div class="grid-content ep-bg-purple-light">
        市场价：<span style="color: #e82149;font-size: 20px;">18</span> 元
      </div>
      <div class="grid-content ep-bg-purple-light">
        分销价：<span style="color: #e82149;font-size: 20px;">12</span> 元
      </div>
      <div class="grid-content ep-bg-purple-light">
        佣金：<span style="color: #e82149;font-size: 20px;">1</span> 元
      </div>
    </el-col>
    <el-col :span="5" class="link-container">
      <div class="grid-content ep-bg-purple-light">
        <el-link type="success" href="">下载贴片</el-link>
      </div>
      <div class="grid-content ep-bg-purple-light">
        <el-link type="success" href="">下载轮播视频</el-link>
      </div>
      <div class="grid-content ep-bg-purple-light">
        <el-link type="success" href="">下载图片素材</el-link>
      </div>
      <div class="grid-content ep-bg-purple-light">
        <el-link type="success" href="">下载话术</el-link>
      </div>
    </el-col>
  </el-row>
</template>

<script setup>

</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: flex-start; /* 垂直对齐顶部 */
}

.el-col {
  border-radius: 4px;
  text-align: left; /* 文本居左 */
}

.grid-content {
  padding: 5px; /* 根据需要添加内边距 */
  border-radius: 4px;
  font-size: 14px;
  color: dimgray;
}

/*.ep-bg-purple {
  background-color: purple; !* 假设的紫色背景 *!
  color: white; !* 文本颜色为白色 *!
}*/

.image-container {
  display: flex;
  align-items: center;
  height: 100%;
}

.product-image {
  display: block;
  margin: 0 auto; /* 图片水平居中 */
  width: 150px;
  height: auto; /* 图片高度自动以保持比例 */
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
  transition: transform 0.3s ease, box-shadow 0.3s ease; /* 添加过渡效果 */
}

.product-image:hover {
  transform: scale(1.1); /* 鼠标悬停时放大 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
}

.text-container,
.link-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}
</style>
