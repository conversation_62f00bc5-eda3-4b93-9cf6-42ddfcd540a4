<?php

declare(strict_types=1);

namespace base\pipeline;

use base\base\DS;
class Payload
{
    /**
     * 实际存储的就是输入的所有参数
     *
     * @var DS
     */
    private DS $params;

    /**
     *
     * @var DS
     */
    private DS $response;

    /**
     * 获取存储的就是输入的所有参数
     *
     * @return DS
     */
    public function getParams(): DS
    {
        return $this->params;
    }

    /**
     * 设置存储的就是输入的所有参数
     *
     * @param array $params
     *
     * @return $this
     */
    public function setParams(array $params): Payload
    {
        $this->params = ds($params);

        return $this;
    }

    public function getResponse(): DS
    {
        return $this->response;
    }

    public function setResponse(array $response): Payload
    {
        $this->response = ds($response);

        return $this;
    }

    public function mergeResponse(array $response): Payload
    {
        $this->response->merge($response);

        return $this;
    }
}

