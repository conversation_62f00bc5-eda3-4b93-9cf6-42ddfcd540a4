<?php

namespace app\controller;

use support\Log;
use support\Redis;
use Webman\Http\Request;

class IndexController extends base
{
    private $platformHosts = [
        'Meituan' => 'mpc.meituan.com',
        'Douyin' => 'life.douyin.com',
        'Kuaishou' => 'lbs.kuaishou.com',
    ];
    private $platformCookieMaps = [
        'Meituan' => ['_lxsdk_cuid', '_lxsdk', 'WEBDFPID', 'e_b_id_352126', 'ly_bsid', 'bizAccountId', 'mpc_user_id', 'mpc_user_name', '_lxsdk_s', 'expirationDate'],
        'Douyin' => ['gfkadpd', 'csrf_session_id', 's_v_web_id', 'passport_csrf_token', 'passport_csrf_token_default', 'passport_auth_status_ls', 'passport_auth_status_ss_ls', 'sid_guard_ls', 'sid_guard_ls', 'is_hit_partitioned_cookie_canary', 'is_hit_partitioned_cookie_canary', 'uid_tt_ls', 'uid_tt_ls', 'uid_tt_ss_ls', 'uid_tt_ss_ls', 'is_hit_partitioned_cookie_canary_ss', 'is_hit_partitioned_cookie_canary_ss', 'sid_tt_ls', 'sid_tt_ls', 'sessionid_ls', 'sessionid_ls', 'sessionid_ss_ls', 'sessionid_ss_ls', 'sid_ucp_v1_ls', 'sid_ucp_v1_ls', 'ssid_ucp_v1_ls', 'ssid_ucp_v1_ls', 'odin_tt', 'ttwid', 'msToken', 'expirationDate'],
        'Kuaishou' => ['_did', 'did', 'userId', 'kuaishou.lbs.merchant_st', 'kuaishou.lbs.merchant_ph', 'merchantRoleId', 'merchantRoleType', 'expirationDate'],
    ];


    public function index()
    {
        return file_get_contents(base_path('public/index.html'));
    }

    public function json(Request $request)
    {
        $all = $request->get();
        return $this->success($all);
    }

    public function cookies(Request $request)
    {
        $post = $request->post();
        Log::warning("提交参数：", ['data' => $post]);
        file_put_contents('test.log', json_encode($post, JSON_UNESCAPED_UNICODE));

        if (!isset($post['xUrl']) || !isset($post['cookies'])) {
            Log::error("提交参数错误Err：", ['msg' => $post]);
            return $this->success(null);
        }

        $cookies = [];
        $platform = '';
        $old = PHP_INT_MAX;
        foreach ($this->platformHosts as $platformName => $host) {
            if (isUrlInDomainWithPath($post['xUrl'], $host)) {
                $platform = $platformName;
                break;
            }
        }

        foreach ($post['cookies'] as $c) {
            if (!empty($c['expirationDate'])) {
                $lost = intval($c['expirationDate']);
                if ($lost > 0 && $lost < $old) {
                    $old = $lost;
                }
            }
            //过滤不需要的cookie字段
            if (!in_array($c['name'], $this->platformCookieMaps[$platform])) continue;

            $name = $c['name'];
            $value = $c['value'];
            $cookies[] = "$name=$value";
        }

        $ex = $old - time();
        Redis::hSet("OS:COOKIE:EXP:DATE", $platform, $ex);

        if (!empty($platform)) {
            Redis::set($platform . ':cookie', join('; ', $cookies), $ex);
        }
        Log::warning('====cookies:', ['platform' => $platform, 'expirationDate' => $ex, 'cookies' => $cookies]);
        return $this->success(null);
    }

    /**
     * @param Request $request
     * @return \support\Response
     */
    public function setCookies(Request $request): \support\Response {
        $params = $request->all();
        if (!isset($params['cookie_name']) || !isset($params['cookie_value'])) {
            return $this->error('参数错误');
        }
        Redis::set($params['cookie_name'], $params['cookie_value'], null, 30*24*60*60);

        return $this->success(null);
    }
}

