<template>
  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        v-model="listQuery.times"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :default-time="['00:00:00', '23:59:59']"
        class="filter-item"
      />
      <el-select
        v-model="listQuery.os"
        placeholder="来源"
        clearable
        style="width: 140px"
        class="filter-item"
      >
        <el-option
          v-for="item in osOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button 
        class="filter-item" 
        type="primary" 
        icon="el-icon-search" 
        @click="getList"
      >
        查询
      </el-button>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="hover" class="total-stats">
          <div slot="header" class="clearfix">
            <span>总体统计</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-item-title">客户总数</div>
                <div class="stat-item-value">{{ totalStats.orders_num || 0 }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-item-title">已加微信数</div>
                <div class="stat-item-value">{{ totalStats.is_wechat_num || 0 }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-item-title">有效粉丝数</div>
                <div class="stat-item-value">{{ totalStats.fans_status_num || 0 }}</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-item-title">出行数</div>
                <div class="stat-item-value">{{ totalStats.chuxing_num || 0 }}</div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 20px">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-item-title">微信转化率</div>
                <div class="stat-item-value">{{ totalStats.is_wechat_radio || 0 }}%</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-item-title">有效粉丝率</div>
                <div class="stat-item-value">{{ totalStats.fans_status_radio || 0 }}%</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%; margin-top: 20px;"
    >
      <el-table-column align="center" label="客服" prop="admin.username" />
      <el-table-column align="center" label="客户数量" prop="orders_num" />
      <el-table-column align="center" label="已加微信数量" prop="is_wechat_num" />
      <el-table-column align="center" label="微信转化率" prop="is_wechat_radio">
        <template slot-scope="scope">
          {{ scope.row.is_wechat_radio }}%
        </template>
      </el-table-column>
      <el-table-column align="center" label="有效粉丝数" prop="fans_status_num" />
      <el-table-column align="center" label="有效粉丝率" prop="fans_status_radio">
        <template slot-scope="scope">
          {{ scope.row.fans_status_radio }}%
        </template>
      </el-table-column>
      <el-table-column align="center" label="出行数" prop="chuxing_num" />
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'PrivateStats',
  data() {
    return {
      list: [],
      listLoading: true,
      osOptions: [],
      totalStats: {
        orders_num: 0,
        is_wechat_num: 0,
        fans_status_num: 0,
        chuxing_num: 0,
        is_wechat_radio: 0,
        fans_status_radio: 0
      },
      listQuery: {
        times: [
          this.getStartDateOfMonth(),
          this.getEndDateOfMonth()
        ],
        os: ''
      }
    }
  },
  created() {
    this.getOsOptions()
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      const params = {}
      
      if (this.listQuery.times && this.listQuery.times.length === 2) {
        params.times = this.listQuery.times
      }
      
      if (this.listQuery.os) {
        params.os = this.listQuery.os
      }
      
      this.$axios.get('/admin/data/indexXs', { params })
        .then(response => {
          this.list = response.data
          
          // 计算总体统计数据
          this.calculateTotalStats()
          
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    getOsOptions() {
      this.$axios.get('/admin/line/parameter')
        .then(response => {
          if (response && response.data && response.data.source) {
            this.osOptions = Object.keys(response.data.source).map(key => {
              return {
                label: response.data.source[key],
                value: key
              }
            })
          }
        })
        .catch(error => {
          console.error('获取来源选项失败', error)
        })
    },
    calculateTotalStats() {
      this.totalStats = {
        orders_num: 0,
        is_wechat_num: 0,
        fans_status_num: 0,
        chuxing_num: 0,
        is_wechat_radio: 0,
        fans_status_radio: 0
      }
      
      if (!this.list || this.list.length === 0) return
      
      let total_orders = 0
      let total_wechat = 0
      let total_fans = 0
      let total_chuxing = 0
      
      this.list.forEach(item => {
        total_orders += parseInt(item.orders_num || 0)
        total_wechat += parseInt(item.is_wechat_num || 0)
        total_fans += parseInt(item.fans_status_num || 0)
        total_chuxing += parseInt(item.chuxing_num || 0)
      })
      
      this.totalStats.orders_num = total_orders
      this.totalStats.is_wechat_num = total_wechat
      this.totalStats.fans_status_num = total_fans
      this.totalStats.chuxing_num = total_chuxing
      
      // 计算总体比率
      if (total_orders > 0) {
        this.totalStats.is_wechat_radio = (total_wechat / total_orders * 100).toFixed(2)
        this.totalStats.fans_status_radio = (total_fans / total_orders * 100).toFixed(2)
      }
    },
    getStartDateOfMonth() {
      const date = new Date()
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01` + ' 00:00:00'
    },
    getEndDateOfMonth() {
      const date = new Date()
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const lastDay = new Date(year, month, 0).getDate()
      return `${year}-${String(month).padStart(2, '0')}-${lastDay}` + ' 23:59:59'
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
  .filter-item {
    margin-right: 10px;
  }
}

.total-stats {
  margin-bottom: 20px;
  
  .stat-item {
    text-align: center;
    padding: 10px;
    
    .stat-item-title {
      font-size: 14px;
      color: #909399;
      margin-bottom: 10px;
    }
    
    .stat-item-value {
      font-size: 24px;
      font-weight: bold;
      color: #409EFF;
    }
  }
}
</style> 