(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-dfa2aaa6"],{"058d":function(t,e,i){"use strict";i("9e5d")},"09f4":function(t,e,i){"use strict";i.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,i,a){return t/=a/2,t<1?i/2*t*t+e:(t--,-i/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,i){var o=s(),l=t-o,r=20,c=0;e="undefined"===typeof e?500:e;var u=function(){c+=r;var t=Math.easeInOutQuad(c,o,l,e);n(t),c<e?a(u):i&&"function"===typeof i&&i()};u()}},"4c7f":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-container"},[i("div",{staticClass:"filter-container"},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"标题"},model:{value:t.listQuery.title,callback:function(e){t.$set(t.listQuery,"title",e)},expression:"listQuery.title"}}),i("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"状态"},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},[i("el-option",{key:"",attrs:{label:"请选择",value:""}}),t._l(t.statusArr,(function(t,e){return i("el-option",{key:e,attrs:{label:t,value:e}})}))],2),i("el-button",{staticClass:"filter-item search",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v("搜索")]),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-circle-plus"},on:{click:t.onAdd}},[t._v("添加")])],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%","padding-bottom":"60px !important"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[i("el-table-column",{attrs:{align:"center",label:"ID",width:"80",prop:"id"}}),i("el-table-column",{attrs:{align:"center",label:"城市",width:"80",prop:"qaCitys.city_name"}}),i("el-table-column",{staticStyle:{overflow:"hidden"},attrs:{align:"center",label:"标题",width:"280"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("div",{staticClass:"ellipsis-text"},[t._v(t._s(e.row.title))])]}}])}),i("el-table-column",{attrs:{align:"center",label:"是否私域QA",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(i){return t.updatePrivateStatus(e.row)}},model:{value:e.row.is_private,callback:function(i){t.$set(e.row,"is_private",i)},expression:"scope.row.is_private"}})]}}])}),i("el-table-column",{attrs:{align:"center",label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(i){return t.updateStatus(e.row)}},model:{value:e.row.status,callback:function(i){t.$set(e.row,"status",i)},expression:"scope.row.status"}})]}}])}),i("el-table-column",{attrs:{align:"center",width:"220",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(i){return t.onAdd(e.row)}}},[t._v("编辑")]),i("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-delete"},on:{click:function(i){return t.onDel(e.row)}}},[t._v("删除")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}),t.dialogCreate?i("el-dialog",{ref:"dialog",attrs:{title:t.title,visible:t.dialogCreate},on:{"update:visible":function(e){t.dialogCreate=e}}},[i("div",{staticStyle:{display:"flex","justify-content":"flex-end","margin-bottom":"10px"}},[i("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{type:"primary"},on:{click:t.onSave}},[t._v("保 存")])],1),i("el-scrollbar",{ref:"scrollbar",staticClass:"scrollable-container"},[i("el-form",{ref:"addForm",attrs:{"label-width":"120px",model:t.anchors}},[i("el-form-item",{attrs:{label:"城市",prop:"city_id"}},[i("el-select",{attrs:{placeholder:"请选择"},model:{value:t.anchors.city_id,callback:function(e){t.$set(t.anchors,"city_id",e)},expression:"anchors.city_id"}},[i("el-form-item",{staticStyle:{display:"inline-flex","text-align":"left",width:"770px"}},t._l(t.getQaCitys,(function(t){return i("el-option",{key:t.city_id,staticStyle:{display:"inline-flex","word-break":"break-all"},attrs:{label:t.city_name,value:t.city_id}})})),1)],1)],1),i("el-form-item",{attrs:{label:"旅游路线",prop:"title"}},[i("el-input",{attrs:{type:"text",placeholder:"请输入旅游路线"},model:{value:t.anchors.title,callback:function(e){t.$set(t.anchors,"title",e)},expression:"anchors.title"}})],1),i("el-form-item",{attrs:{label:"是否私域QA"}},[i("el-radio-group",{model:{value:t.anchors.is_private,callback:function(e){t.$set(t.anchors,"is_private",e)},expression:"anchors.is_private"}},[i("el-radio",{attrs:{label:0}},[t._v("否")]),i("el-radio",{attrs:{label:1}},[t._v("是")])],1)],1),i("el-form-item",{attrs:{label:"QA内容"}},[t._l(t.anchors.qaQuestions,(function(e,a){return i("div",{staticClass:"mistake-content"},[i("div",{staticClass:"mistake-left"},[i("div",[t._v("副标题")]),i("div",{staticClass:"qa-desc"},[i("el-input",{staticStyle:{width:"100px","margin-right":"10px"},attrs:{type:"text",placeholder:"序号"},model:{value:e.sort,callback:function(i){t.$set(e,"sort",i)},expression:"item.sort"}}),i("el-input",{attrs:{type:"text",placeholder:"请输入副标题"},model:{value:e.title,callback:function(i){t.$set(e,"title",i)},expression:"item.title"}})],1),i("div",[t._v("内容")]),i("div",{staticStyle:{border:"1px solid #ccc"}},[i("myEditor",{model:{value:e.content,callback:function(i){t.$set(e,"content",i)},expression:"item.content"}})],1)]),i("div",{staticClass:"mistake-right"},[i("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.handleDel(a)}}},[t._v("删除")])],1)])})),i("div",{staticClass:"mistake-btn"},[i("el-button",{attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v("添加")])],1)],2),i("el-form-item",{attrs:{label:"状态"}},[i("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-color":"#13ce66","inactive-color":"#ff4949"},model:{value:t.anchors.status,callback:function(e){t.$set(t.anchors,"status",e)},expression:"anchors.status"}})],1),i("div",{staticStyle:{display:"flex","justify-content":"flex-end","margin-bottom":"10px"}},[i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onAddImg()}}},[t._v("添加图片")])],1),i("el-form-item",{attrs:{label:"上传图片"}},[i("div",{staticClass:"upload-list"},t._l(t.anchors.img_zip,(function(e,a){return i("div",{staticClass:"wu-yu"},[i("i",{staticClass:"close el-icon-close",on:{click:function(e){return e.stopPropagation(),t.handleClose("img_zip",a)}}}),i("el-upload",{staticClass:"avatar-uploader",attrs:{action:"","show-file-list":!1,"http-request":t.handlesAvatarSuccess,"on-success":function(e,i,n){return t.handleAvatarSuccess(e,i,n,a)}}},[e.file?i("div",{staticClass:"img-box"},[t.checkIfUrlContainsImage(e.file)?i("img",{staticClass:"avatar",staticStyle:{width:"100px",height:"100px"},attrs:{src:e.file,alt:""}}):i("i",{staticClass:"el-icon-folder"}),i("div",{staticClass:"desc"},[t._v(t._s(t.handleRegex(e.file)))])]):i("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),i("el-input",{attrs:{placeholder:"图片说明"},model:{value:e.desc,callback:function(i){t.$set(e,"desc",i)},expression:"item.desc"}})],1)})),0),i("div",{staticStyle:{color:"red"}},[t._v("(请上传.jpg, png的图片)")])]),i("div",{staticStyle:{display:"flex","justify-content":"flex-end","margin-bottom":"10px"}},[i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onAddtrip()}}},[t._v("添加行程")])],1),i("el-form-item",{attrs:{label:"上传行程"}},[i("div",{staticClass:"upload-list"},t._l(t.anchors.trip_zip,(function(e,a){return i("div",{staticClass:"wu-yu"},[i("i",{staticClass:"close el-icon-close",on:{click:function(e){return e.stopPropagation(),t.handleClose("trip_zip",a)}}}),i("el-upload",{staticClass:"avatar-uploader",attrs:{action:"","http-request":t.handlesAvatarSuccess,"show-file-list":!1,"on-success":function(e,i,n){return t.handleSuccess(e,i,n,a)}}},[e.file?i("div",{staticClass:"img-box"},[t.checkIfUrlContainsImage(e.file)?i("img",{staticClass:"avatar",staticStyle:{width:"100px",height:"100px"},attrs:{src:e.file,alt:""}}):i("i",{staticClass:"el-icon-folder"}),i("div",{staticClass:"desc"},[t._v(t._s(t.handleRegex(e.file)))])]):i("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),i("el-input",{attrs:{placeholder:"行程说明"},model:{value:e.desc,callback:function(i){t.$set(e,"desc",i)},expression:"item.desc"}})],1)})),0),i("span",{staticStyle:{color:"red"}},[t._v("(本行程请上传,ppt,word,pdf格式的文件)")])])],1)],1)],1):t._e()],1)},n=[],s=i("d09a"),o=i("0fc4"),l=i("7921"),r=(i("e168"),i("3dd5"),i("16dd"),i("485c"),i("90c8"),i("8d8a"),i("bf58"),i("5f23"),i("67f2")),c=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"relative",staticStyle:{border:"1px solid #ccc"}},[t.disabled?i("div",{staticClass:"disable-layer"}):t._e(),i("Toolbar",{ref:"toolbar",staticStyle:{"border-bottom":"1px solid #ccc"},attrs:{editor:t.editor,"default-config":t.toolbarConfig,mode:t.mode}}),i("Editor",{staticStyle:{height:"300px","overflow-y":"hidden"},attrs:{value:t.value,"default-config":t.editorConfig,mode:t.mode},on:{input:t.handleInput,onCreated:t.onCreated}})],1)},u=[],d=(i("9409"),i("46ba")),p=(i("e1ba"),{name:"WangEditor",components:{Editor:d["a"],Toolbar:d["b"]},props:{value:String,disabled:Boolean,cusHeight:{type:String,default:"250px"}},data:function(){return{editor:null,html:"",toolbarConfig:{toolbarKeys:["headerSelect","blockquote","header1","header2","header3","|","bold","underline","italic","through","color","bgColor","clearStyle","|","bulletedList","numberedList","todo","justifyLeft","justifyRight","justifyCenter","|","insertLink","insertTable"]},editorConfig:{placeholder:"请输入内容..."},mode:"default"}},mounted:function(){},created:function(){},beforeDestroy:function(){var t=this.editor;null!=t&&t.destroy()},methods:{onCreated:function(t){this.editor=Object.seal(t)},handleInput:function(t){this.$emit("input",t)}}}),h=p,f=(i("058d"),i("8a34")),m=Object(f["a"])(h,c,u,!1,null,"25113710",null),g=m.exports,v=i("5f87"),b={name:"getQa",components:{Pagination:r["a"],myEditor:g},data:function(){return{statusArr:{0:"禁用",1:"启用"},title:"",list:[],total:0,loading:!1,listLoading:!0,listQuery:{page:1,limit:10,status:null,city_name:"",title:"",content:"",img_zip:[],trip_zip:[]},dialogCreate:!1,dialogEdit:!1,item:{},anchors:{qaQuestions:[],img_zip:[{desc:"",file:""}],trip_zip:[{desc:"",file:""}],is_private:0},getQaCitys:{}}},created:function(){this.listQuery.status=this.$route.query.status||null,this.listQuery.content=this.$route.query.content||null,this.getList(),this.getQaCity()},methods:{checkIfUrlContainsImage:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=[".jpg",".jpeg",".png",".gif",".bmp",".svg",".webp"];return e.some((function(e){return t.toLowerCase().endsWith(e)}))},handlesAvatarSuccess:function(t){var e=this;return Object(l["a"])(Object(o["a"])().mark((function i(){var a,n;return Object(o["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,a=new FormData,a.append("file",t.file),e.upLoading=!0,e,i.next=7,e.$axios.post("/admin/upload/index",a,{headers:{"Content-type":"multipart/form-data","X-Token":Object(v["a"])()}});case 7:n=i.sent,t.onSuccess(n),i.next=14;break;case 11:i.prev=11,i.t0=i["catch"](0),console.error(i.t0);case 14:case"end":return i.stop()}}),i,null,[[0,11]])})))()},handleClose:function(t,e){1!=this.anchors[t].length?(console.log("i===",e),this.anchors[t].splice(e,1)):this.$message({message:"至少保留一条",type:"warning"})},handleAdd:function(){this.anchors.qaQuestions.push({sort:this.anchors.qaQuestions[this.anchors.qaQuestions.length-1].sort+1,title:"",content:""})},handleDel:function(t){1!=this.anchors.qaQuestions.length?this.anchors.qaQuestions=this.anchors.qaQuestions.filter((function(e,i){return i!==t})):this.$message({message:"至少保留一条",type:"warning"})},getList:function(){var t=this;this.listLoading=!0,this.$axios.get("/admin/qa/getQa",{params:this.listQuery}).then((function(e){t.list=e.data.data,t.total=e.data.total,t.listLoading=!1})).catch((function(){t.listLoading=!1}))},onAddImg:function(){this.anchors.img_zip.push({desc:"",file:""})},onAddtrip:function(){this.anchors.trip_zip.push({desc:"",file:""})},handleAvatarSuccess:function(t,e,i,a){t.data&&(this.anchors.img_zip[a].file="".concat(window.location.protocol,"//").concat(window.location.host).concat(t.data))},handleSuccess:function(t,e,i,a){console.log(t,e,i),t.data&&(this.anchors.trip_zip[a].file="".concat(window.location.protocol,"//").concat(window.location.host).concat(t.data))},handleRegex:function(t){var e=/\/([^\/]+)$/,i=t.match(e);return i?i[1]:t},handleFilter:function(){this.listQuery.page=1,this.getList()},onAdd:function(t){this.dialogCreate=!0,t.id?(this.title="编辑QA",t.qaQuestions.length?this.anchors=Object(s["a"])({},t):this.anchors=Object(s["a"])(Object(s["a"])({},t),{},{qaQuestions:[{sort:1,title:"",content:""}]}),this.anchors.img_zip=t.img_zip?t.img_zip:[{desc:"",file:""}],this.anchors.trip_zip=t.trip_zip?t.trip_zip:[{desc:"",file:""}]):(this.title="添加QA",this.anchors.qaQuestions=[{sort:1,title:"",content:""}],this.anchors.img_zip=[{desc:"",file:""}],this.anchors.trip_zip=[{desc:"",file:""}])},onSave:function(){var t=this;if(!this.loading){this.loading=!0;var e="添加QA"==this.title?"/admin/qa/addQa":"/admin/qa/editQa";this.$axios.post(e,this.anchors).then((function(){t.dialogCreate=!1,t.loading=!1,t.getList()})).catch((function(){t.loading=!1}))}},onDel:function(t){var e=this;this.$axios.post("/admin/qa/delQa",{id:t.id}).then((function(){e.getList()})).catch((function(){}))},getQaCity:function(){var t=this;this.$axios.post("/admin/qacity/getQaCity").then((function(e){t.getQaCitys=e.data,t.getList()})).catch((function(){}))},updateSort:function(t){var e=this;this.$axios.post("/admin/qa/editQa",{id:t.id,sort:t.sort}).then((function(){e.getList()})).catch((function(){}))},updateStatus:function(t){var e=this;this.$axios.post("/admin/qa/editQa",{id:t.id,status:t.status}).then((function(){e.getList()})).catch((function(){}))},updatePrivateStatus:function(t){var e=this;this.$axios.post("/admin/qa/editQa",{id:t.id,is_private:t.is_private}).then((function(){e.getList()})).catch((function(){}))}},mounted:function(){var t=this;setTimeout((function(){t.html="<p>模拟 Ajax 异步设置内容 HTML</p>"}),1500)},beforeDestroy:function(){var t=this.editor;null!=t&&t.destroy()}},y=b,_=(i("8f28"),i("a6f0"),Object(f["a"])(y,a,n,!1,null,"8dd4b4d2",null));e["default"]=_.exports},"67f2":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[i("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},n=[],s=(i("374d"),i("09f4")),o={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(s["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(s["a"])(0,800)}}},l=o,r=(i("7d30"),i("8a34")),c=Object(r["a"])(l,a,n,!1,null,"28fdfbeb",null);e["a"]=c.exports},7140:function(t,e,i){},"7d30":function(t,e,i){"use strict";i("7140")},"80b0":function(t,e,i){},"8f28":function(t,e,i){"use strict";i("80b0")},"9e5d":function(t,e,i){}}]);