(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0b25656c"],{"09f4":function(t,e,i){"use strict";i.d(e,"a",(function(){return s})),Math.easeInOutQuad=function(t,e,i,a){return t/=a/2,t<1?i/2*t*t+e:(t--,-i/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,i){var s=o(),r=t-s,l=20,c=0;e="undefined"===typeof e?500:e;var u=function(){c+=l;var t=Math.easeInOutQuad(c,s,r,e);n(t),c<e?a(u):i&&"function"===typeof i&&i()};u()}},"67f2":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[i("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},n=[],o=(i("374d"),i("09f4")),s={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(o["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(o["a"])(0,800)}}},r=s,l=(i("7d30"),i("8a34")),c=Object(l["a"])(r,a,n,!1,null,"28fdfbeb",null);e["a"]=c.exports},7140:function(t,e,i){},"7d30":function(t,e,i){"use strict";i("7140")},"8af1":function(t,e,i){},"948d":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-container"},[i("div",{staticClass:"filter-container"},[i("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-circle-plus"},on:{click:t.onAdd}},[t._v("添加")])],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[i("el-table-column",{attrs:{align:"center",fixed:"",label:"ID",width:"80",prop:"city_id"}}),i("el-table-column",{attrs:{align:"center",fixed:"",label:"城市",width:"220",prop:"city_name"}}),i("el-table-column",{attrs:{align:"center",label:"排序",width:"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-input-number",{staticClass:"small-input-number",staticStyle:{width:"110px",height:"36px"},attrs:{max:100,min:0},on:{change:function(i){return t.updateSort(e.row)}},model:{value:e.row.sort,callback:function(i){t.$set(e.row,"sort",i)},expression:"scope.row.sort"}})]}}])}),i("el-table-column",{attrs:{align:"center",width:"220",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(i){return t.onEdit(e.row)}}},[t._v("编辑")]),i("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-delete"},on:{click:function(i){return t.onDel(e.row)}}},[t._v("删除")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getQaCityList}}),i("el-dialog",{attrs:{title:"添加城市",visible:t.dialogCreate},on:{"update:visible":function(e){t.dialogCreate=e}}},[i("el-form",{attrs:{"label-width":"120px",model:t.anchors}},[i("el-form-item",{attrs:{label:"城市"}},[i("el-input",{attrs:{type:"text",placeholder:"请输入城市"},model:{value:t.anchors.city_name,callback:function(e){t.$set(t.anchors,"city_name",e)},expression:"anchors.city_name"}})],1),i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{attrs:{max:9999,min:0,"controls-position":"right"},model:{value:t.anchors.sort,callback:function(e){t.$set(t.anchors,"sort",e)},expression:"anchors.sort"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{type:"primary"},on:{click:t.onSave}},[t._v("保 存")])],1)],1),i("el-dialog",{attrs:{title:"编辑城市",visible:t.dialogEdit},on:{"update:visible":function(e){t.dialogEdit=e}}},[i("el-form",{attrs:{"label-width":"120px",model:t.anchors}},[i("el-form-item",{attrs:{label:"城市"}},[i("el-input",{attrs:{type:"text",placeholder:"请输入城市"},model:{value:t.anchors.city_name,callback:function(e){t.$set(t.anchors,"city_name",e)},expression:"anchors.city_name"}})],1),i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{attrs:{max:9999,min:0,"controls-position":"right"},model:{value:t.anchors.sort,callback:function(e){t.$set(t.anchors,"sort",e)},expression:"anchors.sort"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{type:"primary"},on:{click:t.onSave}},[t._v("保 存")])],1)],1)],1)},n=[],o=i("d09a"),s=(i("16dd"),i("67f2")),r={name:"GetQa",components:{Pagination:s["a"]},data:function(){return{statusArr:{0:"禁用",1:"启用"},list:[],total:0,loading:!1,listLoading:!0,listQuery:{page:1,limit:10,status:null,city_name:"",title:"",content:""},dialogCreate:!1,dialogEdit:!1,item:{},anchors:{}}},created:function(){this.listQuery.status=this.$route.query.status||null,this.listQuery.content=this.$route.query.content||null,this.getQaCityList()},methods:{getQaCityList:function(){var t=this;this.listLoading=!0,this.$axios.get("/admin/qacity/getQaCityList",{params:this.listQuery}).then((function(e){t.list=e.data.data,t.total=e.data.total,t.listLoading=!1})).catch((function(){t.listLoading=!1}))},onAdd:function(){this.anchors={sort:0},this.dialogCreate=!0},onEdit:function(t){this.anchors=Object(o["a"])({},t),this.dialogEdit=!0},onSave:function(){var t=this;if(!this.loading){this.loading=!0;var e=this.dialogCreate?"/admin/qacity/addQaCity":"/admin/qacity/editQaCity";this.$axios.post(e,this.anchors).then((function(){t.dialogCreate=!1,t.dialogEdit=!1,t.loading=!1,t.getQaCityList()})).catch((function(){t.loading=!1}))}},onDel:function(t){var e=this;this.$axios.post("/admin/qacity/delQaCity",{city_id:t.city_id}).then((function(){e.getQaCityList()})).catch((function(){}))},updateSort:function(t){var e=this;this.$axios.post("/admin/qacity/editQaCity",{city_id:t.city_id,sort:t.sort}).then((function(){e.getQaCityList()})).catch((function(){}))},updateStatus:function(t){var e=this;this.$axios.post("/admin/qacity/editQaCity",{city_id:t.city_id,status:t.status}).then((function(){e.getQaCityList()})).catch((function(){}))}}},l=r,c=(i("a41c"),i("8a34")),u=Object(c["a"])(l,a,n,!1,null,"47215198",null);e["default"]=u.exports},a41c:function(t,e,i){"use strict";i("8af1")}}]);