<?php
namespace app\utils;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;

class ExcelUtil
{
    /**
     * 将数组数据写入Excel文件
     * @param array $data 要写入的数据数组
     * @param array $header 表头配置 ['列名' => ['type' => 'string|select', 'options' => []]]
     * @param string $filename 文件名
     * @return string 生成的文件路径
     */
    public static function writeToExcel(array $data, array $header, string $filename): string
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // 写入表头
        $cols = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
        $current = 0;
        foreach ($header as $title => $config) {
            $col = $cols[$current];
            $sheet->setCellValue($col . '1', $title);
            // 如果是下拉选择，设置数据验证
            if (isset($config['type']) && $config['type'] === 'select' && !empty($config['options'])) {
                $validation = $sheet->getCell($col . '2')->getDataValidation();
                $validation->setType(DataValidation::TYPE_LIST)
                    ->setErrorStyle(DataValidation::STYLE_INFORMATION)
                    ->setAllowBlank(true)
                    ->setShowInputMessage(true)
                    ->setShowErrorMessage(true)
                    ->setShowDropDown(true)
                    ->setFormula1('"' . implode(',', $config['options']) . '"');
                for ($i = 3; $i <= 100; $i++) {
                    $sheet->getCell($col.$i)->setDataValidation(clone $validation);
                }
            }

            $current++;
        }

        $row = 2;
        foreach ($data as $rowData) {
            $current = 0;
            foreach ($rowData as $value) {
                $col = $cols[$current];
                $sheet->setCellValue($col . $row, $value);
                $current++;
            }
            $row++;
        }

        // 调整列宽
        foreach (range('A', $sheet->getHighestColumn()) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $filepath = public_path() . '/uploads/excel/' . date('Ymd');
        if (!is_dir($filepath)) {
            mkdir($filepath, 0755, true);
        }

        $fullPath = $filepath . '/' . $filename;
        $writer = new Xlsx($spreadsheet);
        $writer->save($fullPath);

        return 'public/uploads/excel/' . date('Ymd') . '/' . $filename;
    }

    /**
     * 读取Excel文件内容
     * @param string $filepath Excel文件路径
     * @return array 读取的数据数组
     */
    public static function readFromExcel(string $filepath): array
    {
        if (!file_exists($filepath)) {
            throw new \Exception('文件不存在');
        }

        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
        $spreadsheet = $reader->load($filepath);
        $worksheet = $spreadsheet->getActiveSheet();

        $data = [];
        foreach ($worksheet->getRowIterator() as $row) {
            $rowData = [];
            foreach ($row->getCellIterator() as $cell) {
                $rowData[] = $cell->getValue();
            }
            $data[] = $rowData;
        }

        return $data;
    }
}
