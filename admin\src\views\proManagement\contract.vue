<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.title"
        placeholder="标题"
        style="width: 200px"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.product_name"
        placeholder="线路名称"
        style="width: 200px"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList"
      >
        搜索
        </el-button>
        <el-button
          type="success"
        class="filter-item"
        icon="el-icon-plus"
        @click="handleCreate"
        >
        新增线路资质
        </el-button>
      </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" label="ID" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="标题" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.title }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="线路名称" min-width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.product.product_name }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="营业执照" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.ye_license">
            <div class="image-preview-wrapper" v-if="getImages(scope.row.ye_license).length > 0">
              <el-image 
                :src="getImages(scope.row.ye_license)[0]" 
                fit="cover" 
                class="preview-image"
                :preview-src-list="getImages(scope.row.ye_license)"
              >
              </el-image>
              <span v-if="getImages(scope.row.ye_license).length > 1" class="image-count">
                +{{ getImages(scope.row.ye_license).length - 1 }}
              </span>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="经营许可证" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.jy_license">
            <div class="image-preview-wrapper" v-if="getImages(scope.row.jy_license).length > 0">
              <el-image 
                :src="getImages(scope.row.jy_license)[0]" 
                fit="cover" 
                class="preview-image"
                :preview-src-list="getImages(scope.row.jy_license)"
              >
              </el-image>
              <span v-if="getImages(scope.row.jy_license).length > 1" class="image-count">
                +{{ getImages(scope.row.jy_license).length - 1 }}
              </span>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="收款二维码" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.pay_qr">
            <div class="image-preview-wrapper" v-if="getImages(scope.row.pay_qr).length > 0">
              <el-image 
                :src="getImages(scope.row.pay_qr)[0]" 
                fit="cover" 
                class="preview-image"
                :preview-src-list="getImages(scope.row.pay_qr)"
              >
              </el-image>
              <span v-if="getImages(scope.row.pay_qr).length > 1" class="image-count">
                +{{ getImages(scope.row.pay_qr).length - 1 }}
              </span>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="酒店图片" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.hotel_pictures">
            <div class="image-preview-wrapper" v-if="getImages(scope.row.hotel_pictures).length > 0">
              <el-image 
                :src="getImages(scope.row.hotel_pictures)[0]" 
                fit="cover" 
                class="preview-image"
                :preview-src-list="getImages(scope.row.hotel_pictures)"
              >
              </el-image>
              <span v-if="getImages(scope.row.hotel_pictures).length > 1" class="image-count">
                +{{ getImages(scope.row.hotel_pictures).length - 1 }}
              </span>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="餐厅图片" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.restaurant_picture">
            <div class="image-preview-wrapper" v-if="getImages(scope.row.restaurant_picture).length > 0">
              <el-image 
                :src="getImages(scope.row.restaurant_picture)[0]" 
                fit="cover" 
                class="preview-image"
                :preview-src-list="getImages(scope.row.restaurant_picture)"
              >
              </el-image>
              <span v-if="getImages(scope.row.restaurant_picture).length > 1" class="image-count">
                +{{ getImages(scope.row.restaurant_picture).length - 1 }}
              </span>
      </div>
    </div>
          <span v-else>-</span>
        </template>
      </el-table-column>


      <el-table-column align="center" label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleUpdate(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '新增线路资质' : '编辑线路资质'" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 90%; margin-left: 30px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="temp.title" placeholder="请输入标题" />
        </el-form-item>

        <el-form-item label="线路名称" prop="product_id">
          <el-select v-model="temp.product_id" filterable placeholder="请选择线路">
            <el-option
              v-for="item in productOptions"
              :key="item.id"
              :label="item.product_name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="营业执照">
          <multiple-image v-model="temp.ye_license" />
        </el-form-item>

        <el-form-item label="经营许可证">
          <multiple-image v-model="temp.jy_license" />
        </el-form-item>

        <el-form-item label="收款二维码">
          <multiple-image v-model="temp.pay_qr" />
        </el-form-item>

        <el-form-item label="酒店图片">
          <multiple-image v-model="temp.hotel_pictures" />
        </el-form-item>

        <el-form-item label="餐厅图片">
          <multiple-image v-model="temp.restaurant_picture" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import MultipleImage from '@/components/Upload/MultipleImage'
import { parseTime } from '@/utils'
import { getToken } from '@/utils/auth'

export default {
  name: 'ContractManagement',
  components: { Pagination, MultipleImage },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        title: '',
        product_name: ''
      },
      productOptions: [],
      temp: {
        id: undefined,
        title: '',
        product_id: '',
        ye_license: '',
        jy_license: '',
        pay_qr: '',
        hotel_pictures: '',
        restaurant_picture: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      rules: {
        title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
        product_id: [{ required: true, message: '请选择线路', trigger: 'change' }]
      },
      uploadPdfAction: '',
      templateFileList: [],
      pdfDialogVisible: false,
      currentPdfUrl: ''
    }
  },
  created() {
    this.getList()
    this.getProducts()
  },
  methods: {
    getList() {
      this.listLoading = true
      this.$axios.get('/admin/line/contract', {
        params: this.listQuery
      }).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    getProducts() {
      this.$axios.get('/admin/line/list', {
        params: { limit: 100 }
      }).then(response => {
        this.productOptions = response.data.data
      })
    },
    // 将逗号分隔的图片字符串转为数组
    getImages(imageStr) {
      if (!imageStr) return []
      return imageStr.split(',').filter(item => item.trim() !== '')
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        title: '',
        product_id: '',
        ye_license: '',
        jy_license: '',
        pay_qr: '',
        hotel_pictures: '',
        restaurant_picture: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleUpdate(row) {
      this.temp = {
        id: row.id,
        title: row.title,
        product_id: row.product_id,
        ye_license: row.ye_license,
        jy_license: row.jy_license,
        pay_qr: row.pay_qr,
        hotel_pictures: row.hotel_pictures,
        restaurant_picture: row.restaurant_picture
      }
      
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除此线路资质?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$axios.post('/admin/line/deleteContract', { id: row.id })
          .then(() => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            this.getList()
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.$axios.post('/admin/line/saveContract', this.temp)
            .then(() => {
              this.dialogFormVisible = false
              this.$message({
                message: '创建成功',
                type: 'success'
              })
              this.getList()
            })
        }
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          this.$axios.post('/admin/line/saveContract', tempData)
            .then(() => {
              this.dialogFormVisible = false
              this.$message({
                message: '更新成功',
                type: 'success'
              })
              this.getList()
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
  .filter-item {
    margin-right: 10px;
  }
}

.image-preview-wrapper {
  position: relative;
  width: 60px;
  height: 60px;
  cursor: pointer;
  
  .preview-image {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    object-fit: cover;
  }
  
  .image-count {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    font-size: 12px;
    padding: 2px 5px;
    border-radius: 0 0 4px 0;
  }
}

.el-image {
      display: flex;
  justify-content: center;
      align-items: center;
  ::v-deep .el-image__inner {
    max-height: 60px;
    max-width: 60px;
  }
}

/* 图片预览关闭按钮样式修改为白色 */
::v-deep .el-image-viewer__close {
  color: #ffffff;
}

/* 让关闭按钮更醒目 */
::v-deep .el-image-viewer__close {
  color: #ffffff;
  font-size: 64px;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(0, 0, 0, 0.8);
  opacity: 0.9;
  transition: all 0.3s;
}

::v-deep .el-image-viewer__close:hover {
  transform: scale(1.2);
  opacity: 1;
}
</style>
