(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0f008a40"],{"09f4":function(e,t,i){"use strict";i.d(t,"a",(function(){return l})),Math.easeInOutQuad=function(e,t,i,a){return e/=a/2,e<1?i/2*e*e+t:(e--,-i/2*(e*(e-2)-1)+t)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(e,t,i){var l=s(),r=e-l,o=20,c=0;t="undefined"===typeof t?500:t;var u=function(){c+=o;var e=Math.easeInOutQuad(c,l,r,t);n(e),c<t?a(u):i&&"function"===typeof i&&i()};u()}},"2cbf":function(e,t,i){"use strict";i("bac3")},"333d":function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[i("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},n=[],s=(i("374d"),i("09f4")),l={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e}),this.autoScroll&&Object(s["a"])(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&Object(s["a"])(0,800)}}},r=l,o=(i("2cbf"),i("8a34")),c=Object(o["a"])(r,a,n,!1,null,"6af373ef",null);t["a"]=c.exports},"7e02":function(e,t,i){"use strict";i("8fc5")},82330:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"app-container"},[i("div",{staticClass:"filter-container"},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"标题"},model:{value:e.listQuery.title,callback:function(t){e.$set(e.listQuery,"title",t)},expression:"listQuery.title"}}),i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"线路名称"},model:{value:e.listQuery.product_name,callback:function(t){e.$set(e.listQuery,"product_name",t)},expression:"listQuery.product_name"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.getList}},[e._v(" 搜索 ")]),i("el-button",{staticClass:"filter-item",attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.handleCreate}},[e._v(" 新增线路资质 ")])],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.list,border:"",fit:"","highlight-current-row":""}},[i("el-table-column",{attrs:{align:"center",label:"ID",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.id))])]}}])}),i("el-table-column",{attrs:{align:"center",label:"标题",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.title))])]}}])}),i("el-table-column",{attrs:{align:"center",label:"线路名称","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.product.product_name))])]}}])}),i("el-table-column",{attrs:{align:"center",label:"营业执照",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.ye_license?i("div",[e.getImages(t.row.ye_license).length>0?i("div",{staticClass:"image-preview-wrapper"},[i("el-image",{staticClass:"preview-image",attrs:{src:e.getImages(t.row.ye_license)[0],fit:"cover","preview-src-list":e.getImages(t.row.ye_license)}}),e.getImages(t.row.ye_license).length>1?i("span",{staticClass:"image-count"},[e._v(" +"+e._s(e.getImages(t.row.ye_license).length-1)+" ")]):e._e()],1):e._e()]):i("span",[e._v("-")])]}}])}),i("el-table-column",{attrs:{align:"center",label:"经营许可证",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.jy_license?i("div",[e.getImages(t.row.jy_license).length>0?i("div",{staticClass:"image-preview-wrapper"},[i("el-image",{staticClass:"preview-image",attrs:{src:e.getImages(t.row.jy_license)[0],fit:"cover","preview-src-list":e.getImages(t.row.jy_license)}}),e.getImages(t.row.jy_license).length>1?i("span",{staticClass:"image-count"},[e._v(" +"+e._s(e.getImages(t.row.jy_license).length-1)+" ")]):e._e()],1):e._e()]):i("span",[e._v("-")])]}}])}),i("el-table-column",{attrs:{align:"center",label:"收款二维码",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.pay_qr?i("div",[e.getImages(t.row.pay_qr).length>0?i("div",{staticClass:"image-preview-wrapper"},[i("el-image",{staticClass:"preview-image",attrs:{src:e.getImages(t.row.pay_qr)[0],fit:"cover","preview-src-list":e.getImages(t.row.pay_qr)}}),e.getImages(t.row.pay_qr).length>1?i("span",{staticClass:"image-count"},[e._v(" +"+e._s(e.getImages(t.row.pay_qr).length-1)+" ")]):e._e()],1):e._e()]):i("span",[e._v("-")])]}}])}),i("el-table-column",{attrs:{align:"center",label:"酒店图片",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.hotel_pictures?i("div",[e.getImages(t.row.hotel_pictures).length>0?i("div",{staticClass:"image-preview-wrapper"},[i("el-image",{staticClass:"preview-image",attrs:{src:e.getImages(t.row.hotel_pictures)[0],fit:"cover","preview-src-list":e.getImages(t.row.hotel_pictures)}}),e.getImages(t.row.hotel_pictures).length>1?i("span",{staticClass:"image-count"},[e._v(" +"+e._s(e.getImages(t.row.hotel_pictures).length-1)+" ")]):e._e()],1):e._e()]):i("span",[e._v("-")])]}}])}),i("el-table-column",{attrs:{align:"center",label:"餐厅图片",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.restaurant_picture?i("div",[e.getImages(t.row.restaurant_picture).length>0?i("div",{staticClass:"image-preview-wrapper"},[i("el-image",{staticClass:"preview-image",attrs:{src:e.getImages(t.row.restaurant_picture)[0],fit:"cover","preview-src-list":e.getImages(t.row.restaurant_picture)}}),e.getImages(t.row.restaurant_picture).length>1?i("span",{staticClass:"image-count"},[e._v(" +"+e._s(e.getImages(t.row.restaurant_picture).length-1)+" ")]):e._e()],1):e._e()]):i("span",[e._v("-")])]}}])}),i("el-table-column",{attrs:{align:"center",label:"操作",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(i){return e.handleUpdate(t.row)}}},[e._v(" 编辑 ")]),i("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v(" 删除 ")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){return e.$set(e.listQuery,"page",t)},"update:limit":function(t){return e.$set(e.listQuery,"limit",t)},pagination:e.getList}}),i("el-dialog",{attrs:{title:"create"===e.dialogStatus?"新增线路资质":"编辑线路资质",visible:e.dialogFormVisible},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[i("el-form",{ref:"dataForm",staticStyle:{width:"90%","margin-left":"30px"},attrs:{rules:e.rules,model:e.temp,"label-position":"left","label-width":"120px"}},[i("el-form-item",{attrs:{label:"标题",prop:"title"}},[i("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.temp.title,callback:function(t){e.$set(e.temp,"title",t)},expression:"temp.title"}})],1),i("el-form-item",{attrs:{label:"线路名称",prop:"product_id"}},[i("el-select",{attrs:{filterable:"",placeholder:"请选择线路"},model:{value:e.temp.product_id,callback:function(t){e.$set(e.temp,"product_id",t)},expression:"temp.product_id"}},e._l(e.productOptions,(function(e){return i("el-option",{key:e.id,attrs:{label:e.product_name,value:e.id}})})),1)],1),i("el-form-item",{attrs:{label:"营业执照"}},[i("multiple-image",{model:{value:e.temp.ye_license,callback:function(t){e.$set(e.temp,"ye_license",t)},expression:"temp.ye_license"}})],1),i("el-form-item",{attrs:{label:"经营许可证"}},[i("multiple-image",{model:{value:e.temp.jy_license,callback:function(t){e.$set(e.temp,"jy_license",t)},expression:"temp.jy_license"}})],1),i("el-form-item",{attrs:{label:"收款二维码"}},[i("multiple-image",{model:{value:e.temp.pay_qr,callback:function(t){e.$set(e.temp,"pay_qr",t)},expression:"temp.pay_qr"}})],1),i("el-form-item",{attrs:{label:"酒店图片"}},[i("multiple-image",{model:{value:e.temp.hotel_pictures,callback:function(t){e.$set(e.temp,"hotel_pictures",t)},expression:"temp.hotel_pictures"}})],1),i("el-form-item",{attrs:{label:"餐厅图片"}},[i("multiple-image",{model:{value:e.temp.restaurant_picture,callback:function(t){e.$set(e.temp,"restaurant_picture",t)},expression:"temp.restaurant_picture"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(" 取消 ")]),i("el-button",{attrs:{type:"primary"},on:{click:function(t){"create"===e.dialogStatus?e.createData():e.updateData()}}},[e._v(" 确认 ")])],1)],1)],1)},n=[],s=(i("3dd5"),i("90c8"),i("bd1a"),i("333d")),l=i("b560"),r=(i("ed08"),i("5f87"),{name:"ContractManagement",components:{Pagination:s["a"],MultipleImage:l["a"]},data:function(){return{list:[],total:0,listLoading:!0,listQuery:{page:1,limit:10,title:"",product_name:""},productOptions:[],temp:{id:void 0,title:"",product_id:"",ye_license:"",jy_license:"",pay_qr:"",hotel_pictures:"",restaurant_picture:""},dialogFormVisible:!1,dialogStatus:"",rules:{title:[{required:!0,message:"标题不能为空",trigger:"blur"}],product_id:[{required:!0,message:"请选择线路",trigger:"change"}]},uploadPdfAction:"",templateFileList:[],pdfDialogVisible:!1,currentPdfUrl:""}},created:function(){this.getList(),this.getProducts()},methods:{getList:function(){var e=this;this.listLoading=!0,this.$axios.get("/admin/line/contract",{params:this.listQuery}).then((function(t){e.list=t.data.data,e.total=t.data.total,e.listLoading=!1})).catch((function(){e.listLoading=!1}))},getProducts:function(){var e=this;this.$axios.get("/admin/line/list",{params:{limit:100}}).then((function(t){e.productOptions=t.data.data}))},getImages:function(e){return e?e.split(",").filter((function(e){return""!==e.trim()})):[]},resetTemp:function(){this.temp={id:void 0,title:"",product_id:"",ye_license:"",jy_license:"",pay_qr:"",hotel_pictures:"",restaurant_picture:""}},handleCreate:function(){var e=this;this.resetTemp(),this.dialogStatus="create",this.dialogFormVisible=!0,this.$nextTick((function(){e.$refs["dataForm"].clearValidate()}))},handleUpdate:function(e){var t=this;this.temp={id:e.id,title:e.title,product_id:e.product_id,ye_license:e.ye_license,jy_license:e.jy_license,pay_qr:e.pay_qr,hotel_pictures:e.hotel_pictures,restaurant_picture:e.restaurant_picture},this.dialogStatus="update",this.dialogFormVisible=!0,this.$nextTick((function(){t.$refs["dataForm"].clearValidate()}))},handleDelete:function(e){var t=this;this.$confirm("确认删除此线路资质?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((function(){t.$axios.post("/admin/line/deleteContract",{id:e.id}).then((function(){t.$message({type:"success",message:"删除成功!"}),t.getList()}))})).catch((function(){t.$message({type:"info",message:"已取消删除"})}))},createData:function(){var e=this;this.$refs["dataForm"].validate((function(t){t&&e.$axios.post("/admin/line/saveContract",e.temp).then((function(){e.dialogFormVisible=!1,e.$message({message:"创建成功",type:"success"}),e.getList()}))}))},updateData:function(){var e=this;this.$refs["dataForm"].validate((function(t){if(t){var i=Object.assign({},e.temp);e.$axios.post("/admin/line/saveContract",i).then((function(){e.dialogFormVisible=!1,e.$message({message:"更新成功",type:"success"}),e.getList()}))}}))}}}),o=r,c=(i("ab04"),i("8a34")),u=Object(c["a"])(o,a,n,!1,null,"52235149",null);t["default"]=u.exports},"8fc5":function(e,t,i){},9269:function(e,t,i){},ab04:function(e,t,i){"use strict";i("9269")},b560:function(e,t,i){"use strict";var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"multiple-image-upload"},[i("el-upload",{staticClass:"image-uploader",attrs:{data:e.dataObj,multiple:!0,"file-list":e.fileList,"on-success":e.handleSuccess,"on-remove":e.handleRemove,"on-preview":e.handlePictureCardPreview,"http-request":e.handlesAvatarSuccess,action:"","list-type":"picture-card"}},[i("i",{staticClass:"el-icon-plus"})]),i("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[i("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1)},n=[],s=i("0fc4"),l=i("7921"),r=i("756f"),o=(i("e168"),i("3dd5"),i("3066"),i("e224"),i("4cc3"),i("485c"),i("452e"),i("90c8"),i("bd1a"),i("5f87")),c={name:"MultipleImageUpload",props:{value:{type:String,default:""}},data:function(){return{dialogVisible:!1,dialogImageUrl:"",tempUrls:[],fileList:[],dataObj:{token:"",key:""}}},watch:{value:{handler:function(e){if(e){var t=e.split(",").filter((function(e){return""!==e.trim()}));this.tempUrls=Object(r["a"])(t),this.fileList=t.map((function(e,t){return{name:"图片".concat(t+1),url:e,uid:Date.now()+t}}))}else this.tempUrls=[],this.fileList=[]},immediate:!0}},methods:{handlesAvatarSuccess:function(e){var t=this;return Object(l["a"])(Object(s["a"])().mark((function i(){var a,n,l;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,a=new FormData,a.append("file",e.file),i.next=5,t.$axios.post("/admin/upload/index",a,{headers:{"Content-type":"multipart/form-data","X-Token":Object(o["a"])()}});case 5:n=i.sent,n.data&&(l="".concat(window.location.protocol,"//").concat(window.location.host).concat(n.data),e.onSuccess({data:{url:l}})),i.next=13;break;case 9:i.prev=9,i.t0=i["catch"](0),console.error("上传失败:",i.t0),e.onError(i.t0);case 13:case"end":return i.stop()}}),i,null,[[0,9]])})))()},handleSuccess:function(e,t){if(e&&e.data&&e.data.url){this.tempUrls.push(e.data.url),this.emitInput(this.tempUrls.join(","));var i={name:t.name,url:e.data.url,uid:t.uid||Date.now()},a=this.fileList.findIndex((function(e){return e.uid===i.uid}));a>=0?this.fileList.splice(a,1,i):this.fileList.push(i)}},handleRemove:function(e,t){var i=this.tempUrls.findIndex((function(t){return e.url===t}));-1!==i&&(this.tempUrls.splice(i,1),this.emitInput(this.tempUrls.join(",")))},emitInput:function(e){this.$emit("input",e)},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0}}},u=c,p=(i("7e02"),i("8a34")),d=Object(p["a"])(u,a,n,!1,null,"364987a4",null);t["a"]=d.exports},bac3:function(e,t,i){}}]);