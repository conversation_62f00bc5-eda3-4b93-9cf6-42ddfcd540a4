<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.username"
        placeholder="用户名"
        style="width: 200px; margin-right: 10px"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-circle-plus"
        @click="handleCreate({ routes: [] })"
      >
        添加
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" label="操作" width="300">
        <template slot-scope="scope">
          <el-button
            type="primary"
            @click="handleCreate(scope.row)"
            size="small"
            icon="el-icon-edit"
          >
            修改
          </el-button>

          <!-- <el-button
            type="primary"
            v-if="scope.row.status"
            @click="onWork(scope.row)"
            size="small"
            icon="el-icon-date"
          >
            排班
          </el-button> -->

          <el-button
            type="success"
            @click="handleUpdateRoutes(scope.row)"
            size="small"
            icon="el-icon-map-location"
            style="margin-top:5px"
          >
            更新线路
          </el-button>

          <el-button
            type="info"
            @click="handleViewLogs(scope.row)"
            size="small"
            icon="el-icon-document"
            style="margin-top:5px"
          >
            操作日志
          </el-button>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="ID"
        width="80"
        prop="id"
      ></el-table-column>

      <el-table-column
        align="center"
        label="用户名"
        width="160"
        prop="username"
      ></el-table-column>

      <el-table-column
        align="center"
        label="姓名"
        width="160"
        prop="name"
      ></el-table-column>

      <el-table-column
        align="center"
        label="手机"
        width="160"
        prop="mobile"
      ></el-table-column>
      <!-- <el-table-column width="138px" align="center" label="路线">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.route_type === 10" type="border-card"
            >境内路线</el-tag
          >
          <el-tag v-if="scope.row.route_type === 20" type="success"
            >境外路线</el-tag
          >
        </template>
      </el-table-column> -->

      <!--      <el-table-column align="center" label="头像">
        <template slot-scope="scope">
          <el-avatar :size="50" :src="scope.row.avatar"></el-avatar>
        </template>
      </el-table-column>-->

      <el-table-column class-name="status-col" label="状态" width="180">
        <template slot-scope="{ row }">
          <el-switch
            v-model="row.status"
            @change="setStatus(row)"
            active-text="启用"
            :active-value="1"
            :inactive-value="0"
            inactive-text="禁用"
          >
          </el-switch>
        </template>
      </el-table-column>

      <el-table-column width="180px" align="center" label="创建时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.create_time | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column width="180px" align="center" label="修改时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.update_time | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column width="180px" align="center" label="最后登陆地址">
        <template slot-scope="scope">
          <span>{{
            scope.row.ip_address
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-dialog title="修改管理员" :visible.sync="dialogVisible">
      <el-form label-width="120px" :model="item">
        <el-form-item label="用户名">
          <el-input
            v-model="item.username"
            name="username"
            placeholder="管理员的用户名"
          ></el-input>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input
            v-model="item.name"
            name="name"
            placeholder="管理员的姓名"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机">
          <el-input
            v-model="item.mobile"
            name="name"
            placeholder="发送短时的时候会用到这个手机"
          ></el-input>
        </el-form-item>
        <el-form-item label="微信">
          <el-input
            v-model="item.wechat"
            name="wechat"
            placeholder="微信号"
          ></el-input>
        </el-form-item>
        <el-form-item label="微信图片">
          <el-upload
            action = ""
            list-type="picture-card"
            multiple:false
            :show-file-list="false"
            :http-request="handlesAvatarSuccess"
            :on-success="
                    (response, file, fileList) =>
                      handleSuccess(response, file, fileList, 1)"
          >
            <img v-if="item.wechat_pic" :src="item.wechat_pic" style="width: 120px; height: 120px;margin-top: 14px;"/>
            <img v-if="!item.wechat_pic" src="~@/assets/home/<USER>" style="width: 120px; height: 120px;margin-top: 14px;"/>
          </el-upload>
        </el-form-item>
        <!-- <el-form-item label="密码">
          <el-input
            autocomplete="off"
            v-model="item.password"
            type="password"
            name="password"
            placeholder="管理员的新密码"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="是否分配">
          <el-switch
            v-model="item.is_order"
            active-text="分配"
            :active-value="1"
            inactive-text="不分配"
            :inactive-value="0"
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="是否主播">
          <el-switch
            v-model="item.is_anchor"
            active-text="主播"
            :active-value="1"
            inactive-text=""
            :inactive-value="0"
          >
          </el-switch>
        </el-form-item>
        <el-form-item v-if="!checkPermission(['franchisee'])" label="是否加盟商">
          <el-switch
            v-model="item.is_franchisee"
            active-text="加盟商"
            :active-value="1"
            inactive-text=""
            :inactive-value="0"
          >
          </el-switch>
        </el-form-item>

        <el-form-item label="是否私域">
          <el-switch
            v-model="item.is_xs"
            active-text="私域"
            :active-value="1"
            inactive-text=""
            :inactive-value="0"
          >
          </el-switch>
        </el-form-item>
        <el-form-item label="抖音昵称">
          <el-input
            v-model="item.dy_nickname"
            name="dy_nickname"
            placeholder="抖音昵称"
          ></el-input>
        </el-form-item>
        <el-form-item label="重置密码">
          <el-switch
            v-model="item.reset_passwd"
            :active-value="1"
            inactive-text=""
            :inactive-value="0"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onSave(item)">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog title="添加排班" :visible.sync="dialogWork">
      <el-form label-width="120px" :model="from">
        <el-form-item label="上班日期">
          <el-date-picker
            style="margin-right: 10px"
            type="dates"
            v-model="from.dates"
            placeholder="选择一个或多个日期"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="上班时间段">
          <el-time-picker
            is-range
            v-model="from.time"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择时间范围"
          >
          </el-time-picker>
        </el-form-item>

        <el-form-item label="渠道">
          <el-checkbox-group v-model="from.oss">
            <el-checkbox v-for="(v, i, k) in oss" :label="i" :key="k">{{
              v
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onWork()">保 存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="添加线路" :visible.sync="isAddRouters" width="30%">
      <el-form
        :rules="addRoutes"
        ref="AddRoutersForm"
        :model="AddRoutersForm"
        label-width="80px"
      >
        <el-form-item label="平台" prop="os">
          <el-select v-model="AddRoutersForm.os" placeholder="请选择平台">
            <el-option
              v-for="item in platformList"
              :label="item.os"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="线路名称" prop="product_name">
          <el-input
            placeholder="请输入线路名称"
            v-model="AddRoutersForm.product_name"
          ></el-input>
        </el-form-item>
        <el-form-item label="线路id" prop="third_product_id">
          <el-input
            placeholder="请输入线路id"
            v-model="AddRoutersForm.third_product_id"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isAddRouters = false">取 消</el-button>
        <el-button type="primary" @click="handleAddRouters">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="更新线路" :visible.sync="dialogRoutes" width="50%">
      <el-form :model="routesItem" label-width="120px">
        <el-form-item label="管理员">
          <span>{{ routesItem.username }} ({{ routesItem.name }})</span>
        </el-form-item>

        <el-form-item label="线路">
          <el-select
            filterable
            v-model="routesItem.product_ids"
            multiple
            placeholder="请选择"
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.product_name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="私域线路">
          <el-select
            filterable
            v-model="routesItem.product_xs_ids"
            multiple
            placeholder="请选择私域线路"
          >
            <el-option
              v-for="item in optionsXs"
              :key="item.id"
              :label="item.product_name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="OTA线路分组">
          <el-select
            filterable
            v-model="routesItem.route_group_ids"
            multiple
            placeholder="请选择OTA线路分组"
          >
            <el-option
              v-for="group in routeGroups"
              :key="group.id"
              :label="group.group_name"
              :value="group.id"
            >
            </el-option>
          </el-select>
          <div style="color: #909399; font-size: 12px; margin-top: 5px;">
            选择线路分组后，该管理员将获得分组中所有线路的权限
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogRoutes = false">取 消</el-button>
        <el-button type="primary" @click="saveRoutes">保 存</el-button>
      </div>
    </el-dialog>

    <!-- 操作日志对话框 -->
    <el-dialog title="操作日志" :visible.sync="operationLogDialog" width="80%">
      <div class="operation-log-container">
        <!-- 筛选条件 -->
        <div class="filter-container">
          <el-form :inline="true" :model="logQuery" class="demo-form-inline">
            <el-form-item label="操作类型">
              <el-select v-model="logQuery.operation_type" placeholder="请选择操作类型" clearable>
                <el-option label="创建" value="create"></el-option>
                <el-option label="更新" value="update"></el-option>
                <el-option label="启用" value="enable"></el-option>
                <el-option label="禁用" value="disable"></el-option>
                <el-option label="重置密码" value="reset_password"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="logDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getOperationLogs">搜索</el-button>
              <el-button @click="resetLogFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 操作日志列表 -->
        <el-table
          v-loading="logLoading"
          :data="operationLogs"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column label="操作时间" width="160" align="center">
            <template slot-scope="scope">
              {{ parseTime(scope.row.operation_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}
            </template>
          </el-table-column>

          <el-table-column label="操作者" width="120" align="center">
            <template slot-scope="scope">
              {{ (scope.row.operator && scope.row.operator.name) || '--' }}
            </template>
          </el-table-column>

          <el-table-column label="操作类型" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getOperationTypeTag(scope.row.operation_type)">
                {{ scope.row.operation_type_name }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="变更内容" min-width="200">
            <template slot-scope="scope">
              <div v-if="scope.row.field_changes && Object.keys(scope.row.field_changes).length > 0">
                <div
                  v-for="(change, field, index) in getLimitedChanges(scope.row.field_changes)"
                  :key="field"
                  class="change-item"
                >
                  <span class="field-label">{{ change.label }}:</span>
                  <span class="old-value">{{ change.old_value || '--' }}</span>
                  <span class="arrow"> → </span>
                  <span class="new-value">{{ change.new_value || '--' }}</span>
                </div>
                <div v-if="Object.keys(scope.row.field_changes).length > 2" class="more-changes">
                  <el-button 
                    type="text" 
                    size="mini" 
                    @click="handleLogDetail(scope.row)"
                    style="color: #409EFF; padding: 0;"
                  >
                    还有 {{ Object.keys(scope.row.field_changes).length - 2 }} 项变更，查看详情
                  </el-button>
                </div>
              </div>
              <span v-else class="text-muted">{{ scope.row.remark || '--' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="IP地址" width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.ip_address || '--' }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="120" align="center">
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                @click="handleLogDetail(scope.row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination
          v-show="logTotal > 0"
          :total="logTotal"
          :page.sync="logQuery.page"
          :limit.sync="logQuery.limit"
          @pagination="getOperationLogs"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="operationLogDialog = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 操作日志详情对话框 -->
    <el-dialog title="操作日志详情" :visible.sync="logDetailDialog" width="60%">
      <div v-if="currentLogRecord">
        <el-card class="detail-card">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">操作时间：</span>
                <span class="detail-value">
                  {{ parseTime(currentLogRecord.operation_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">操作类型：</span>
                <span class="detail-value">{{ currentLogRecord.operation_type_name }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">操作者：</span>
                <span class="detail-value">
                  {{ (currentLogRecord.operator && currentLogRecord.operator.name) || '--' }}
                  ({{ (currentLogRecord.operator && currentLogRecord.operator.username) || '--' }})
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">IP地址：</span>
                <span class="detail-value">{{ currentLogRecord.ip_address || '--' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div class="detail-item">
                <span class="detail-label">备注：</span>
                <span class="detail-value">{{ currentLogRecord.remark || '--' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <el-card v-if="currentLogRecord.field_changes && Object.keys(currentLogRecord.field_changes).length > 0" class="detail-card" style="margin-top: 15px">
          <div slot="header" class="clearfix">
            <span>变更详情</span>
          </div>
          <el-table :data="getFormattedChanges(currentLogRecord.field_changes)" border>
            <el-table-column label="字段" width="120" align="center">
              <template slot-scope="scope">
                {{ scope.row.label }}
              </template>
            </el-table-column>
            <el-table-column label="变更前" align="center">
              <template slot-scope="scope">
                {{ scope.row.old_value || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="变更后" align="center">
              <template slot-scope="scope">
                {{ scope.row.new_value || '--' }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="logDetailDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from "@/components/Pagination"; // Secondary package based on el-pagination
import { getProductsList, addProducts, getProductsListXs } from "@/api/admin";
import { fetchAdminOperationLogList, fetchAdminOperationLogDetail } from "@/api/admin-operation-log";
import { fetchActiveOtaRouteGroups } from "@/api/ota-route-group";
import { getToken } from "@/utils/auth";
import { parseTime } from '@/utils';
import checkPermission from '@/utils/permission';
export default {
  name: "Adminlist",
  components: { Pagination },
  data() {
    return {
      list: [],
      ids: "",
      oss: [],
      platformList: [],
      isAddRouters: false,
      AddRoutersForm: {
        third_product_id: "",
        product_name: "",
        os: "",
      },
      from: { oss: [] },
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
      },
      dialogVisible: false,
      dialogWork: false,
      item: {
        btn: [],
        product_ids: [],
        product_xs_ids: [],
      },
      route_type: "",
      options: [],
      optionsXs: [],
      routeGroups: [],
      addRoutes: {
        third_product_id: [
          { required: true, message: "请输入线路id", trigger: "blur" },
        ],
        os: [{ required: true, message: "请选择平台", trigger: "change" }],
        product_name: [
          { required: true, message: "请输入线路名称", trigger: "blur" },
        ],
      },
      dialogRoutes: false,
      routesItem: {
        id: null,
        username: '',
        name: '',
        product_ids: [],
        product_xs_ids: [],
        route_group_ids: []
      },
      // 操作日志相关
      operationLogDialog: false,
      logDetailDialog: false,
      currentAdmin: null,
      currentLogRecord: null,
      operationLogs: [],
      logTotal: 0,
      logLoading: false,
      logDateRange: [],
      logQuery: {
        page: 1,
        limit: 20,
        admin_id: '',
        operation_type: '',
        start_time: '',
        end_time: ''
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    parseTime,
    checkPermission,
    getList() {
      this.$axios
        .get("/admin/admin/index", { params: this.listQuery })
        .then((response) => {
          this.list = response.data.data;
          this.total = response.data.total;
          this.oss = response.ext.oss;
          // for(let k in response.ext.oss){
          //   this.oss.push({id:k,val:response.ext.oss[k]})
          // }
          console.log(this.oss);
          this.listLoading = false;
        })
        .catch((err) => {});
    },
    checkIfUrlContainsImage(url = "") {
      const imageExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".bmp",
        ".svg",
        ".webp",
      ];
      return imageExtensions.some((extension) =>
        url.toLowerCase().endsWith(extension)
      );
    },
    handleAddRoutes() {
      this.isAddRouters = true;
    },
    async handlesAvatarSuccess(file) {
      try {
        var formdata = new FormData();
        formdata.append("file", file.file);

        this.upLoading = true;
        const _this = this;
        const res = await this.$axios.post("/admin/upload/index", formdata, {
          headers: {
            "Content-type": "multipart/form-data",
            "X-Token": getToken(),
          },
        });
        this.item.wechat_pic = `${window.location.protocol}//${window.location.host}${res.data}`;
        file.onSuccess(res);
      } catch (error) {
        console.error('error:', error);
      }
    },
    handleSuccess(res, file, fileList) {
      console.log(res, file, fileList);
      if (!res.data) return;
      this.item.wechat_pic = `${window.location.protocol}//${window.location.host}${res.data}`;
    },
    handleAddRouters() {
      this.$refs["AddRoutersForm"].validate(async (valid) => {
        if (valid) {
          let add = await addProducts(this.AddRoutersForm);
          this.isAddRouters = false;
          if (add.msg == "ok") {
            this.$message({
              message: "线路添加成功",
              type: "success",
            });
            let res = await getProductsList();
            this.options = res.data.data;
            this.platformList = res.ext.oss;
          }
        } else {
          return false;
        }
      });
    },
    setStatus(item) {
      console.log(item);
      this.$axios
        .post("/admin/admin/disabled", item)
        .then((res) => {
          this.$notify({
            title: "成功",
            message: "修改成功",
            type: "success",
            duration: 2000,
          });
        })
        .catch((err) => {
          this.$notify({
            title: "成功",
            message: "修改失败",
            type: "error",
            duration: 2000,
          });
        });
    },
    async handleCreate(item) {
      this.dialogVisible = true;
      item.reset_passwd = 0;
      this.item = { ...item };
    },
    onSave(from) {
      this.$axios
        .post("/admin/admin/save", {
          ...from,
        })
        .then((response) => {
          this.$message({
            message: "成功",
            type: "success",
          });
          this.item = {};
          this.dialogVisible = false;
          this.getList();
        })
        .catch((err) => {});
    },
    onWork(item) {
      if (item) {
        this.item = item;
        this.from = { oss: [] };
        this.dialogWork = true;
        return;
      }
      this.from.admin_id = this.item.id;
      this.$axios
        .post("/admin/work/saves", this.from)
        .then((response) => {
          this.item = {};
          this.dialogWork = false;
        })
        .catch((err) => {});
    },
    setTime(val) {
      console.log(val);
    },
    async handleUpdateRoutes(row) {
      let res = await getProductsList();
      let resXs = await getProductsListXs();
      await this.getRouteGroups();

      this.options = res.data.data;
      this.optionsXs = resXs.data.data;

      this.routesItem = {
        id: row.id,
        username: row.username,
        name: row.name,
        product_ids: row.product_ids ? row.product_ids.split(",").map(item => Number(item)) : [],
        product_xs_ids: row.product_xs_ids ? row.product_xs_ids.split(",").map(item => Number(item)) : [],
        route_group_ids: row.route_group_ids ? row.route_group_ids.split(",").map(item => Number(item)) : []
      };

      this.dialogRoutes = true;
    },
    async getRouteGroups() {
      try {
        const response = await fetchActiveOtaRouteGroups();
        this.routeGroups = response.data || [];
      } catch (error) {
        console.error('获取线路组失败:', error);
      }
    },

    saveRoutes() {
      const saveData = {
        id: this.routesItem.id,
        product_ids: this.routesItem.product_ids.join(),
        product_xs_ids: this.routesItem.product_xs_ids.join(),
        route_group_ids: this.routesItem.route_group_ids.join()
      };

      this.$axios.post("/admin/admin/save", saveData)
        .then(response => {
          this.$message({
            message: "线路更新成功",
            type: "success"
          });
          this.dialogRoutes = false;
          this.getList();
        })
        .catch(err => {
          this.$message.error("线路更新失败");
        });
    },

    // 查看操作日志
    handleViewLogs(row) {
      this.currentAdmin = row;
      this.logQuery.admin_id = row.id;
      this.operationLogDialog = true;
      this.getOperationLogs();
    },

    // 获取操作日志列表
    getOperationLogs() {
      this.logLoading = true;

      const params = { ...this.logQuery };

      // 处理日期范围
      if (this.logDateRange && this.logDateRange.length === 2) {
        params.start_time = this.logDateRange[0];
        params.end_time = this.logDateRange[1];
      }

      fetchAdminOperationLogList(params).then(response => {
        this.operationLogs = response.data.data || [];
        this.logTotal = response.data.total || 0;
        this.logLoading = false;
      }).catch(error => {
        console.error('获取操作日志失败:', error);
        this.logLoading = false;
      });
    },

    // 重置日志筛选条件
    resetLogFilter() {
      this.logQuery = {
        page: 1,
        limit: 20,
        admin_id: this.currentAdmin ? this.currentAdmin.id : '',
        operation_type: '',
        start_time: '',
        end_time: ''
      };
      this.logDateRange = [];
      this.getOperationLogs();
    },

    // 查看日志详情
    handleLogDetail(row) {
      fetchAdminOperationLogDetail({ id: row.id }).then(response => {
        this.currentLogRecord = response.data;
        this.logDetailDialog = true;
      }).catch(error => {
        console.error('获取日志详情失败:', error);
        this.$message.error('获取日志详情失败');
      });
    },

    // 获取操作类型标签颜色
    getOperationTypeTag(type) {
      const typeMap = {
        'create': 'success',
        'update': 'primary',
        'enable': 'success',
        'disable': 'warning',
        'reset_password': 'danger'
      };
      return typeMap[type] || 'info';
    },

    // 格式化变更数据
    getFormattedChanges(fieldChanges) {
      return Object.keys(fieldChanges).map(field => ({
        field: field,
        label: fieldChanges[field].label,
        old_value: fieldChanges[field].old_value,
        new_value: fieldChanges[field].new_value
      }));
    },

    // 获取限制显示的变更内容（最多2条）
    getLimitedChanges(fieldChanges) {
      const changes = {};
      const keys = Object.keys(fieldChanges);
      
      // 只取前2条
      for (let i = 0; i < Math.min(2, keys.length); i++) {
        const key = keys[i];
        changes[key] = fieldChanges[key];
      }
      
      return changes;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep.el-select {
  width: 100%;
}
.addRoutes {
  display: flex;
  justify-content: end;
}

// 操作日志样式
.operation-log-container {
  .filter-container {
    margin-bottom: 20px;
  }

  .change-item {
    margin-bottom: 5px;
    font-size: 12px;

    .field-label {
      font-weight: bold;
      color: #606266;
    }

    .old-value {
      color: #F56C6C;
      text-decoration: line-through;
    }

    .arrow {
      color: #909399;
      margin: 0 5px;
    }

    .new-value {
      color: #67C23A;
      font-weight: bold;
    }
  }

  .more-changes {
    margin-top: 8px;
    padding-top: 5px;
    border-top: 1px dashed #e4e7ed;
    
    .el-button--text {
      font-size: 12px;
      color: #409EFF;
      
      &:hover {
        color: #66b1ff;
      }
    }
  }

  .text-muted {
    color: #909399;
    font-style: italic;
  }
}

// 详情对话框样式
.detail-card {
  .el-card__header {
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    
    .clearfix span {
      font-weight: bold;
      color: #303133;
    }
  }

  .detail-item {
    margin-bottom: 15px;
    padding: 8px 0;

    .detail-label {
      font-weight: bold;
      color: #606266;
      display: inline-block;
      min-width: 80px;
    }

    .detail-value {
      color: #303133;
      word-break: break-all;
    }
  }

  .el-row:last-child .detail-item {
    margin-bottom: 0;
  }
}
// .el-input__suffix .el-input__suffix-inner {
//   // 解决聚焦的时候会有闪现一下滚动条
//   .el-input__icon {
//     transform: rotateZ(0deg);
//     // transition: transform 20s;
//     // background: pink;
//     &::before {
//       display: inline-block;
//       transition: transform 0.3s;
//       transform: rotateZ(180deg);
//     }
//     &.is-reverse::before {
//       transform: rotateZ(0deg);
//     }
//   }
// }
</style>
