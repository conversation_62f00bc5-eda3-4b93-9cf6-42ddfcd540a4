<?php

namespace app\command;

use app\model\Admins;
use app\model\Orders;
use app\model\Orders as OrdersModel;
use support\Log;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;


class AutoFishDyOrder extends Command
{
    protected static $defaultName = 'auto_fish_dy_direct_order';
    protected static $defaultDescription = '分配抖音直连订单。';

    /**
     * @return void
     */
    protected function configure()
    {
        // $this->addArgument('name', InputArgument::OPTIONAL, 'Name description');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output($output, self::$defaultName . " started, " . date('Y-m-d H:i:s'));

        $orders = $this->orders();
        $total = count($orders);

        $this->output($output, "got {$total} orders");

        if (1 > $total) {
            return self::SUCCESS;
        }

        foreach($orders as $order) {
            $this->giveOrder($order);
        }

        return self::SUCCESS;
    }

    private function output(OutputInterface $output, string $message)
    {
        $output->writeln(sprintf('{"time":"%s", "message":"%s"}', date('Y-m-d H:i:s'), $message));
    }

    /**
     * 重新分配订单
     * @param $order
     * @throws \think\db\exception\BindParamException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function giveOrder($order) {
        try {
            $customer = \app\server\Orders::allocatCustomer($order);
            $order->admin_id = $customer['adminId'];
            $order->shop_id = $customer['shopId'];
            $order->give_time = time();
            $order->save();
            
            // 补充记录分配日志（allocatCustomer内部已记录，这里是补充来源信息）
            if ($order->admin_id && $order->sn) {
                \app\server\Orders::recordAllocateLog(
                    $order->sn,
                    $order->admin_id,
                    $order->shop_id,
                    '自动重新分配抖音订单',
                    ['source' => '自动重新分配抖音直连订单', 'mobile' => $order->mobile]
                );
            }
        } catch (\Exception $e) {
            Log::info(sprintf('重新分配订单失败：%s, 失败原因：%s', json_encode($order), $e->getMessage().$e->getFile().$e->getLine())  );
        }
    }

    /**
     * @return OrdersModel[]
     */
    private function orders(): array {
        $list = OrdersModel::where('is_direct_mode', 1)
            ->where('admin_id', 0)
            ->select()
            ->all();

        return $list;
    }
}
