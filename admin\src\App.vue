<template>
  <div id="app">
    <!-- {{ username }} -->
    <router-view />
  </div>
</template>

<script>

import addWatermark from '@/directive/watermark'

export default {
  watch: {
    // 监听 name 的变化，更新水印
    '$store.getters.name': 'updateWatermark'
  },
  created() {
    // 在 created 钩子中添加默认水印
    this.addDefaultWatermark()
  },
  mounted() {
    // 在 mounted 钩子中更新水印，确保可以获取到 name 的最新值
    this.updateWatermark()
  },
  methods: {
    addDefaultWatermark() {
      addWatermark({
        container: document.body,
        content: '新国旅'
      })
    },
    updateWatermark() {
      const username = this.$store.getters.name
      addWatermark({
        container: document.body,
        content: username ? `${username}` : '新国旅'
      })
    }
  }
}
</script>

<style scoped>
@import "./assets/fonts/font.css";
body{
  font-family: PingFang !important;
}
</style>
