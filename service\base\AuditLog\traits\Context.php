<?php

namespace base\AuditLog\traits;


use think\helper\Arr;

trait Context
{
    /**
     * 用户数据上下文
     *
     * @var array
     */
    protected array $context = [];


    public function getContext(): array
    {
        return $this->context;
    }

    public function setContext($context): self
    {
        $this->context = $context;

        return $this;
    }

    /**
     * 是否为空
     *
     * @access public
     * @return bool
     */
    public function isEmptyContext(): bool
    {
        return empty($this->context);
    }

    /**
     * Merge data.
     *
     * @param self|array $items
     *
     * @return $this
     */
    public function merge($items): self
    {
        foreach ($items as $key => $value) {
            $this->context[$key] = $value;
        }

        return $this;
    }

    /**
     * 确定指定的元素是否存在
     *
     * @param string $key
     *
     * @return bool
     */
    public function has(string $key): bool
    {
        return ! is_null(Arr::get($this->context, $key));
    }

    /**
     * Retrieve item from Collection.
     *
     * @param string|null $key
     * @param mixed $default
     *
     * @return mixed
     */
    public function get(?string $key = null, $default = null)
    {
        return Arr::get($this->context, $key, $default);
    }

    /**
     * Retrieve item from Collection.
     *
     * @param string $key
     * @param mixed $value
     *
     * @return array
     */
    public function set(string $key, $value): array
    {
        return Arr::set($this->context, $key, $value);
    }
}
