<template>
  <div class="multiple-image-upload">
    <el-upload
      :data="dataObj"
      :multiple="true"
      :file-list="fileList"
      :on-success="handleSuccess"
      :on-remove="handleRemove"
      :on-preview="handlePictureCardPreview"
      :http-request="handlesAvatarSuccess"
      action=""
      list-type="picture-card"
      class="image-uploader"
    >
      <i class="el-icon-plus"></i>
    </el-upload>

    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'

export default {
  name: 'MultipleImageUpload',
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: '',
      tempUrls: [],
      fileList: [],
      dataObj: { token: '', key: '' }
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          // 将逗号分隔的URL字符串转换为数组，并生成fileList
          const urls = val.split(',').filter(item => item.trim() !== '')
          this.tempUrls = [...urls]
          this.fileList = urls.map((url, index) => ({
            name: `图片${index + 1}`,
            url: url,
            uid: Date.now() + index
          }))
        } else {
          this.tempUrls = []
          this.fileList = []
        }
      },
      immediate: true
    }
  },
  methods: {
    async handlesAvatarSuccess(file) {
      try {
        var formdata = new FormData()
        formdata.append("file", file.file)

        const res = await this.$axios.post("/admin/upload/index", formdata, {
          headers: {
            "Content-type": "multipart/form-data",
            "X-Token": getToken(),
          }
        })
        
        if (res.data) {
          const fileUrl = `${window.location.protocol}//${window.location.host}${res.data}`
          file.onSuccess({ data: { url: fileUrl } })
        }
      } catch (error) {
        console.error('上传失败:', error)
        file.onError(error)
      }
    },
    handleSuccess(response, file) {
      if (!response || !response.data || !response.data.url) return
      
      this.tempUrls.push(response.data.url)
      this.emitInput(this.tempUrls.join(','))
      
      // 更新文件列表
      const newFile = {
        name: file.name,
        url: response.data.url,
        uid: file.uid || Date.now()
      }
      
      // 查找是否已存在相同uid的文件
      const existingIndex = this.fileList.findIndex(item => item.uid === newFile.uid)
      if (existingIndex >= 0) {
        // 替换已存在的文件
        this.fileList.splice(existingIndex, 1, newFile)
      } else {
        // 添加新文件
        this.fileList.push(newFile)
      }
    },
    handleRemove(file, fileList) {
      const index = this.tempUrls.findIndex(url => file.url === url)
      if (index !== -1) {
        this.tempUrls.splice(index, 1)
        this.emitInput(this.tempUrls.join(','))
      }
    },
    emitInput(val) {
      this.$emit('input', val)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.multiple-image-upload {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
  flex-wrap: wrap;

  .image-uploader {
    border-radius: 6px;
    cursor: pointer;
    width: 100%;
  }

  ::v-deep .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }

  ::v-deep .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
  }
}
</style> 