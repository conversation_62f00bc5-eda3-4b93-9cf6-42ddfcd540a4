<?php
namespace app\server;

class SMS {
    const JUHE_TMP_REMINDER_ORDER = 263585;

    static function juhe_sms_send($mobile, $tempid, $vars)
    {
        foreach($vars as &$v) {
            $v = str_replace(['【','】'],['[',']'], $v);
        }
        $data = [
            'key' => 'f884be98ea615e26a02b3416f8a298bc',
            'mobile' => $mobile,
            'tpl_id' => $tempid,
            'vars' => json_encode($vars, JSON_UNESCAPED_UNICODE)
        ];
        $res = self::http_post("http://v.juhe.cn/sms/send", http_build_query($data), ["Content-Type:application/x-www-form-urlencoded"]);
        if ($res) {
            return json_decode($res, true);
        } else {
            return [
                "reason" => "发送失败",
                "error_code" => 99999
            ];
        }

    }

    /**
     * 发送http post请求
     */
    static function http_post($url, $postfields = null, $headers = array(), $extra = array())
    {
        return self::http_request($url, $method = "POST", $postfields, $headers, $extra);
    }


    /**
     * 发送http get请求
     */
    static function http_get($url, $headers = array(), $extra = array())
    {
        return self::http_request($url, $method = "GET", null, $headers, $extra);
    }


    /**
     * 发送http请求
     */
    static function http_request($url, $method = "GET", $postfields = null, $headers = array(), $extra = array())
    {
        $ci = curl_init();
        /* Curl settings */
        curl_setopt($ci, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ci, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($ci, CURLOPT_TIMEOUT, 30);
        curl_setopt($ci, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ci, CURLOPT_SSL_VERIFYPEER, false); //不验证证书
        curl_setopt($ci, CURLOPT_SSL_VERIFYHOST, false); //不验证证书
        curl_setopt_array($ci, $extra);
        switch ($method) {
            case 'POST':
                curl_setopt($ci, CURLOPT_POST, true);
                if (!empty($postfields)) {
                    curl_setopt($ci, CURLOPT_POSTFIELDS, $postfields);
                }
                break;
        }
        curl_setopt($ci, CURLOPT_URL, $url);
        $headers[] = 'Transfer-Encoding:';//7.4版本修改
        curl_setopt($ci, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ci, CURLINFO_HEADER_OUT, true);

        $response = curl_exec($ci);

        $httpInfo = curl_getinfo($ci, CURLINFO_HTTP_CODE);
        curl_close($ci);
        $code = json_decode($response, true);
        return $response;
    }

}