CREATE TABLE `audit_log_rate_plan` (
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `module`      varchar(64) NOT NULL COMMENT '模块名',
    `trace_id`    varchar(64)  DEFAULT NULL COMMENT '追踪ID',
    `message`     varchar(255) DEFAULT NULL COMMENT '消息内容',
    `old`         json         DEFAULT NULL COMMENT '旧数据',
    `new`         json         DEFAULT NULL COMMENT '新数据',
    `diff`        json         DEFAULT NULL COMMENT '差异数据',
    `desc`        json         DEFAULT NULL COMMENT '描述信息',
    `lang`        varchar(16)  DEFAULT NULL COMMENT '语言包',
    `config`      json         DEFAULT NULL COMMENT '配置信息',
    `uid`         varchar(64)  DEFAULT '0' COMMENT '用户ID',
    `username`    varchar(64)  DEFAULT '' COMMENT '用户名',
    `create_time` int(11) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY           `idx_module` (`module`),
    KEY           `idx_traceId` (`trace_id`),
    KEY           `idx_uid` (`uid`),
    KEY           `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COMMENT='变更日志表';