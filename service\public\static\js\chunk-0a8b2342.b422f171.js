(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0a8b2342"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function i(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,a){var o=r(),l=t-o,s=20,u=0;e="undefined"===typeof e?500:e;var c=function(){u+=s;var t=Math.easeInOutQuad(u,o,l,e);i(t),u<e?n(c):a&&"function"===typeof a&&a()};c()}},"31ad":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"300px"},attrs:{placeholder:"订单号"},model:{value:t.listQuery.sn,callback:function(e){t.$set(t.listQuery,"sn",e)},expression:"listQuery.sn"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v(" 搜索 ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"center",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-check"},on:{click:function(a){return t.handle(e.row,1)}}},[t._v(" 同意 ")]),a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-check"},on:{click:function(a){return t.handle(e.row,2)}}},[t._v(" 拒绝 ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"订单号",prop:"order_id"}}),a("el-table-column",{attrs:{align:"center",label:"售后类型",prop:"after_sale_type_agg_name"}}),a("el-table-column",{attrs:{align:"center",label:"售后原因",prop:"after_sale_reason"}}),a("el-table-column",{attrs:{align:"center",label:"退款金额",prop:"refund_amount"}}),a("el-table-column",{attrs:{align:"center",label:"申请时间",formatter:t.formatTime,prop:"after_sale_apply_time_ms"}}),a("el-table-column",{attrs:{align:"center",label:"到期时间",formatter:t.formatTime,prop:"audit_expire_time_ms"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}})],1)},i=[],r=a("67f2"),o={name:"Orderlist",components:{Pagination:r["a"]},filters:{},data:function(){return{list:[],total:0,listLoading:!0,listQuery:{page:1,limit:10},oss:{},item:{}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.$axios.get("/admin/order/dyAfterSaleOrder",{params:this.listQuery}).then((function(e){t.list=e.data.data,t.total=e.data.total,t.oss=e.ext,t.listLoading=!1}))},handle:function(t,e){var a=this;this.$axios.post("/admin/order/dyAfterSaleOrderHandle",{id:t.id,audit_result:e}).then((function(t){a.$message({message:"处理成功",type:"success"}),a.item={},a.getList()})).catch((function(t){}))},formatTime:function(t,e,a){var n=new Date(a);return n.toLocaleString()}}},l=o,s=(a("e4fe"),a("8a34")),u=Object(s["a"])(l,n,i,!1,null,"203394c6",null);e["default"]=u.exports},"67f2":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[a("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},i=[],r=(a("374d"),a("09f4")),o={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(r["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(r["a"])(0,800)}}},l=o,s=(a("7d30"),a("8a34")),u=Object(s["a"])(l,n,i,!1,null,"28fdfbeb",null);e["a"]=u.exports},7140:function(t,e,a){},"7d30":function(t,e,a){"use strict";a("7140")},a8bc:function(t,e,a){},e4fe:function(t,e,a){"use strict";a("a8bc")}}]);