<?php

return [
    'value' => [
        'default' => '-',
    ],
    "highlight" => [
        // 与post_tags一起使用，定义用于突出显示文本的HTML标记。默认情况下，突出显示的文本被包装在和标记中。指定为字符串。当前配置会影响下级配置。
        "pre_tags"  => "&nbsp&nbsp<b>",
        // 与pre_tags一起使用，定义用于突出显示文本的HTML标记。默认情况下，突出显示的文本被包装在和标记中。指定为字符串。当前配置会影响下级配置。
        "post_tags" => "</b>&nbsp&nbsp",
        "message"   => [
            // 与post_tags一起使用，定义用于突出显示文本的HTML标记。默认情况下，突出显示的文本被包装在和标记中。指定为字符串.当配置当前参数时，上级同名配置不再生效。
//            "pre_tags"  => '',
            // 与pre_tags一起使用，定义用于突出显示文本的HTML标记。默认情况下，突出显示的文本被包装在和标记中。指定为字符串.当配置当前参数时，上级同名配置不再生效。
//            "post_tags" => '',
        ],
        "detail"      => [
            // 与post_tags一起使用，定义用于突出显示文本的HTML标记。默认情况下，突出显示的文本被包装在和标记中。指定为字符串.当配置当前参数时，上级同名配置不再生效。
//            "pre_tags"  => '',
            // 与pre_tags一起使用，定义用于突出显示文本的HTML标记。默认情况下，突出显示的文本被包装在和标记中。指定为字符串.当配置当前参数时，上级同名配置不再生效。
//            "post_tags" => '',
        ],
        'array_index_splitter' => '::',
    ],
    'detail' => [
        'add' => 'add',
        'delete' => 'delete',
        'update' => 'update',
    ],
    'lang' => [
        // 默认加载语言
        'locale' => 'zh-CN',
        // 默认回滚语言包, 如果当前语言包不存在，则回滚到下一个语言包
        'fallbacks' => ['en-US'],
        'detail' => [
            'zh-CN' => [
                'add' => '新增%title%为%value%;',
                'delete' => '删除%title%为%value%;',
                'update' => '修改%title%，旧值为%oldValue%，新值为%newValue%;',
            ],
            'en-US' => [
                'add' => 'Add %title% to %value%;',
                'delete' => 'Delete %title% to %value%;',
                'update' => 'Update %title%, old value is %oldValue%, new value is %newValue%;',
            ],
        ],
    ],
];