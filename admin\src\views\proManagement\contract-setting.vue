<template>
  <div class="contract-setting">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>合同管理</span>
      </div>
      
      <el-row :gutter="20">
        <!-- 境内合同 -->
        <el-col :span="12">
          <el-card shadow="hover" class="contract-card">
            <div slot="header" class="clearfix">
              <span>境内合同</span>
              <el-button 
                v-if="contractInfo.domestic" 
                type="text" 
                class="button-right"
                @click="downloadContract('domestic')"
              >
                <i class="el-icon-download"></i> 下载
              </el-button>
            </div>
            <div class="content-container">
              <div v-if="contractInfo.domestic" class="file-info">
                <div class="file-preview">
                  <div class="file-icon">
                    <i v-if="isPdf(contractInfo.domestic)" class="el-icon-document" style="color: #ff5252;"></i>
                    <i v-else-if="isWordDoc(contractInfo.domestic)" class="el-icon-document" style="color: #4b8bf4;"></i>
                    <i v-else class="el-icon-document"></i>
                  </div>
                  <div class="file-detail">
                    <span class="file-name">{{ getFileName(contractInfo.domestic) }}</span>
                    <span class="file-type">{{ getFileType(contractInfo.domestic) }}</span>
                  </div>
                </div>
                <div class="file-actions">
                  <el-button 
                    size="mini" 
                    type="primary" 
                    @click="openPreviewDialog(contractInfo.domestic)"
                    v-if="isPdf(contractInfo.domestic)"
                  >
                    <i class="el-icon-view"></i> 预览
                  </el-button>
                  <el-button 
                    size="mini" 
                    type="success" 
                    @click="downloadContract('domestic')"
                  >
                    <i class="el-icon-download"></i> 下载
                  </el-button>
                  <el-button 
                    size="mini" 
                    type="danger" 
                    @click="handleRemoveFile('domestic')"
                    v-permission="['admin']"
                  >
                    <i class="el-icon-delete"></i> 移除
                  </el-button>
                </div>
              </div>
              <div v-else class="upload-container">
                <el-upload
                  class="upload"
                  :action="uploadActionUrl"
                  :auto-upload="false"
                  :on-change="(file) => handleFileChange(file, 'domestic')"
                  :file-list="domesticFileList"
                  :limit="1"
                  :on-exceed="handleExceed"
                >
                  <el-button slot="trigger" size="small" type="primary">选择文件</el-button>
                  <div class="el-upload__tip">支持上传PDF、Word文档（.doc/.docx）</div>
                </el-upload>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 境外合同 -->
        <el-col :span="12">
          <el-card shadow="hover" class="contract-card">
            <div slot="header" class="clearfix">
              <span>境外合同</span>
              <el-button 
                v-if="contractInfo.abroad" 
                type="text" 
                class="button-right"
                @click="downloadContract('abroad')"
              >
                <i class="el-icon-download"></i> 下载
              </el-button>
            </div>
            <div class="content-container">
              <div v-if="contractInfo.abroad" class="file-info">
                <div class="file-preview">
                  <div class="file-icon">
                    <i v-if="isPdf(contractInfo.abroad)" class="el-icon-document" style="color: #ff5252;"></i>
                    <i v-else-if="isWordDoc(contractInfo.abroad)" class="el-icon-document" style="color: #4b8bf4;"></i>
                    <i v-else class="el-icon-document"></i>
                  </div>
                  <div class="file-detail">
                    <span class="file-name">{{ getFileName(contractInfo.abroad) }}</span>
                    <span class="file-type">{{ getFileType(contractInfo.abroad) }}</span>
                  </div>
                </div>
                <div class="file-actions">
                  <el-button 
                    size="mini" 
                    type="primary" 
                    @click="openPreviewDialog(contractInfo.abroad)"
                    v-if="isPdf(contractInfo.abroad)"
                  >
                    <i class="el-icon-view"></i> 预览
                  </el-button>
                  <el-button 
                    size="mini" 
                    type="success" 
                    @click="downloadContract('abroad')"
                  >
                    <i class="el-icon-download"></i> 下载
                  </el-button>
                  <el-button 
                    size="mini" 
                    type="danger" 
                    @click="handleRemoveFile('abroad')"
                     v-permission="['admin']"
                  >
                    <i class="el-icon-delete"></i> 移除
                  </el-button>
                </div>
              </div>
              <div v-else class="upload-container">
                <el-upload
                  class="upload"
                  :action="uploadActionUrl"
                  :auto-upload="false"
                  :on-change="(file) => handleFileChange(file, 'abroad')"
                  :file-list="abroadFileList"
                  :limit="1"
                  :on-exceed="handleExceed"
                >
                  <el-button slot="trigger" size="small" type="primary">选择文件</el-button>
                  <div class="el-upload__tip">支持上传PDF、Word文档（.doc/.docx）</div>
                </el-upload>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
    </el-card>
    
    <!-- PDF预览对话框 -->
    <el-dialog 
      :title="previewTitle" 
      :visible.sync="previewDialogVisible" 
      fullscreen 
      append-to-body
      :close-on-click-modal="false"
      class="pdf-preview-dialog"
    >
      <div class="pdf-container" ref="pdfContainer">
        <div class="pdf-toolbar">
          <el-button size="small" icon="el-icon-download" @click="downloadCurrentPreview">下载</el-button>
          <el-button size="small" icon="el-icon-full-screen" @click="toggleFullscreen">{{ isFullscreen ? '退出全屏' : '全屏' }}</el-button>
          <el-button size="small" icon="el-icon-close" @click="previewDialogVisible = false">关闭</el-button>
        </div>
        <iframe 
          :src="previewUrl" 
          class="pdf-iframe" 
          frameborder="0"
          ref="pdfIframe"
        ></iframe>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getContractListsApi, postSubmitContractApi } from '@/api/admin'
import { getToken } from '@/utils/auth'

export default {
  name: 'ContractSetting',
  data() {
    return {
      contractInfo: {
        domestic: '',
        abroad: ''
      },
      domesticFileList: [],
      abroadFileList: [],
      domesticFile: null,
      abroadFile: null,
      uploadActionUrl: '/admin/upload/index', // 上传接口
      previewDialogVisible: false,
      previewUrl: '',
      previewTitle: '文件预览',
      currentPreviewFile: null,
      isFullscreen: false
    }
  },
  created() {
    this.getContractSettings()
  },
  mounted() {
    document.addEventListener('fullscreenchange', this.fullscreenChangeHandler)
    document.addEventListener('webkitfullscreenchange', this.fullscreenChangeHandler)
    document.addEventListener('mozfullscreenchange', this.fullscreenChangeHandler)
    document.addEventListener('MSFullscreenChange', this.fullscreenChangeHandler)
  },
  beforeDestroy() {
    document.removeEventListener('fullscreenchange', this.fullscreenChangeHandler)
    document.removeEventListener('webkitfullscreenchange', this.fullscreenChangeHandler)
    document.removeEventListener('mozfullscreenchange', this.fullscreenChangeHandler)
    document.removeEventListener('MSFullscreenChange', this.fullscreenChangeHandler)
  },
  methods: {
    // 获取合同设置
    async getContractSettings() {
      try {
        const response = await getContractListsApi()
        console.log(response);
        
        if (response && response.data) {
          this.contractInfo = response.data
          
          // 如果有境内合同，设置文件列表
          if (this.contractInfo.domestic) {
            this.domesticFileList = [{
              name: this.getFileName(this.contractInfo.domestic),
              url: this.contractInfo.domestic
            }]
          }
          
          // 如果有境外合同，设置文件列表
          if (this.contractInfo.abroad) {
            this.abroadFileList = [{
              name: this.getFileName(this.contractInfo.abroad),
              url: this.contractInfo.abroad
            }]
          }
        }
      } catch (error) {
        console.error('获取合同设置失败:', error)
        this.$message.error('获取合同设置失败')
      }
    },
    
    // 保存合同设置
    async saveContractSettings(type = null) {
      try {
        // 创建保存的载荷对象，根据类型只包含需要的字段
        let payload = {}
        
        if (type === 'domestic') {
          payload = { domestic: this.contractInfo.domestic || '' }
        } else if (type === 'abroad') {
          payload = { abroad: this.contractInfo.abroad || '' }
        } else {
          // 如果没有指定类型，则保存所有字段
          payload = {
            domestic: this.contractInfo.domestic || '',
            abroad: this.contractInfo.abroad || ''
          }
        }
        
        // 发送请求保存设置
        const response = await postSubmitContractApi(payload)
        
        if (response && response.error === 0) {
          return true
        } else {
          this.$message.error('保存失败')
          return false
        }
      } catch (error) {
        console.error('保存合同设置失败:', error)
        this.$message.error('保存合同设置失败')
        return false
      }
    },
    
    // 文件变更处理
    handleFileChange(file, type) {
      // 上传文件并获取URL
      this.uploadFile(file.raw, type)
    },
    
    // 新增上传文件方法
    async uploadFile(file, type) {
      try {
        // 检查文件类型和大小
        const isValidType = this.validateFileType(file)
        const isValidSize = this.validateFileSize(file)
        
        if (!isValidType) {
          this.$message.error('请上传PDF或Word文件(.pdf/.doc/.docx)')
          // 重置文件列表
          if (type === 'domestic') {
            this.domesticFileList = []
          } else {
            this.abroadFileList = []
          }
          return
        }
        
        if (!isValidSize) {
          this.$message.error('文件大小不能超过10MB')
          // 重置文件列表
          if (type === 'domestic') {
            this.domesticFileList = []
          } else {
            this.abroadFileList = []
          }
          return
        }
        
        // 显示上传中的loading
        const loading = this.$loading({
          lock: true,
          text: '文件上传中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        
        // 创建FormData用于上传文件
        const formData = new FormData()
        formData.append('file', file)
        
        // 上传文件到服务器
        this.$axios.post('/admin/upload/index', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'X-Token': getToken()
          }
        }).then(async response => {
          if (response.error === 0) {
            const fileUrl = `${window.location.protocol}//${window.location.host}${response.data}`
            
            // 根据类型保存URL到对应的字段
            if (type === 'domestic') {
              this.contractInfo.domestic = fileUrl
              this.domesticFileList = [{
                name: file.name,
                url: fileUrl
              }]
            } else {
              this.contractInfo.abroad = fileUrl
              this.abroadFileList = [{
                name: file.name,
                url: fileUrl
              }]
            }
            
            // 直接调用保存接口保存当前字段
            await this.saveContractSettings(type)
            this.$message.success('文件上传成功并已保存')
          } else {
            this.$message.error('文件上传失败')
            // 重置文件列表
            if (type === 'domestic') {
              this.domesticFileList = []
            } else {
              this.abroadFileList = []
            }
          }
          loading.close()
        }).catch(error => {
          loading.close()
          this.$message.error('文件上传失败')
          console.error('上传失败:', error)
          
          // 重置文件列表
          if (type === 'domestic') {
            this.domesticFileList = []
          } else {
            this.abroadFileList = []
          }
        })
      } catch (error) {
        console.error('上传处理失败:', error)
        this.$message.error('上传处理失败')
        
        // 重置文件列表
        if (type === 'domestic') {
          this.domesticFileList = []
        } else {
          this.abroadFileList = []
        }
      }
    },
    
    // 验证文件类型
    validateFileType(file) {
      const fileName = file.name.toLowerCase()
      return fileName.endsWith('.pdf') || fileName.endsWith('.doc') || fileName.endsWith('.docx')
    },
    
    // 验证文件大小
    validateFileSize(file) {
      const maxSize = 10 * 1024 * 1024 // 10MB
      return file.size <= maxSize
    },
    
    // 超出文件数量限制
    handleExceed() {
      this.$message.warning('最多只能上传一个文件')
    },
    
    // 下载合同
    downloadContract(type) {
      const url = this.contractInfo[type]
      if (!url) return
      
      try {
        const a = document.createElement('a')
        a.href = url
        a.target = '_blank'
        a.download = this.getFileName(url)
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        this.$message.success('下载已开始')
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败')
      }
    },
    
    // 打开预览对话框
    openPreviewDialog(url) {
      if (this.isPdf(url)) {
        // 添加参数确保PDF在iframe内正常预览
        const previewUrl = url.includes('?') ? 
          `${url}&embedded=true#view=FitH` : 
          `${url}?embedded=true#view=FitH`
        
        this.previewUrl = previewUrl
        this.previewTitle = '文件预览: ' + this.getFileName(url)
        this.currentPreviewFile = url
        this.previewDialogVisible = true
        
        // 重置全屏状态
        this.isFullscreen = false
      } else {
        this.$message.warning('该文件不是PDF格式，无法在线预览，请下载后查看')
      }
    },
    
    // 下载当前预览的文件
    downloadCurrentPreview() {
      if (this.currentPreviewFile) {
        try {
          const a = document.createElement('a')
          a.href = this.currentPreviewFile
          a.target = '_blank'
          a.download = this.getFileName(this.currentPreviewFile)
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          this.$message.success('下载已开始')
        } catch (error) {
          console.error('下载失败:', error)
          this.$message.error('下载失败')
        }
      }
    },
    
    // 预览文件（旧方法，保留但不使用）
    previewFile(url) {
      if (this.isPdf(url)) {
        window.open(url, '_blank')
      } else {
        this.$message.warning('该文件不是PDF格式，无法在线预览，请下载后查看')
      }
    },
    
    // 判断是否为PDF文件
    isPdf(url) {
      if (!url) return false
      return url.toLowerCase().endsWith('.pdf')
    },
    
    // 判断是否为Word文档
    isWordDoc(url) {
      if (!url) return false
      const lowerUrl = url.toLowerCase()
      return lowerUrl.endsWith('.doc') || lowerUrl.endsWith('.docx')
    },
    
    // 获取文件名
    getFileName(url) {
      if (!url) return ''
      const parts = url.split('/')
      return parts[parts.length - 1]
    },
    
    // 获取文件类型显示文本
    getFileType(url) {
      if (this.isPdf(url)) {
        return 'PDF文档'
      } else if (this.isWordDoc(url)) {
        return 'Word文档'
      } else {
        return '文档'
      }
    },
    
    // 添加文件移除函数
    handleRemoveFile(type) {
      try {
        if (type === 'domestic') {
          this.domesticFileList = []
          this.contractInfo.domestic = ''
        } else {
          this.abroadFileList = []
          this.contractInfo.abroad = ''
        }
        
        // 调用保存接口，更新相应字段为空
        this.saveContractSettings(type).then(success => {
          if (success) {
            this.$message.success('文件已移除')
          }
        })
      } catch (error) {
        console.error('移除文件失败:', error)
        this.$message.error('移除文件失败')
      }
    },
    
    toggleFullscreen() {
      const pdfContainer = this.$refs.pdfContainer
      if (this.isFullscreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      } else {
        if (pdfContainer.requestFullscreen) {
          pdfContainer.requestFullscreen()
        } else if (pdfContainer.webkitRequestFullscreen) {
          pdfContainer.webkitRequestFullscreen()
        } else if (pdfContainer.mozRequestFullScreen) {
          pdfContainer.mozRequestFullScreen()
        } else if (pdfContainer.msRequestFullscreen) {
          pdfContainer.msRequestFullscreen()
        }
        
        // 添加轻微延迟以确保PDF在全屏后能正确渲染
        setTimeout(() => {
          // 尝试调整iframe内容，如果PDF查看器支持的话
          const iframe = this.$refs.pdfIframe
          if (iframe && iframe.contentWindow) {
            try {
              // 尝试触发内部PDF查看器的重新渲染
              iframe.contentWindow.dispatchEvent(new Event('resize'))
            } catch (e) {
              console.log('PDF iframe内容刷新失败', e)
            }
          }
        }, 300)
      }
      this.isFullscreen = !this.isFullscreen
    },

    fullscreenChangeHandler() {
      const pdfContainer = this.$refs.pdfContainer
      this.isFullscreen = document.fullscreenElement === pdfContainer || 
        document.webkitFullscreenElement === pdfContainer || 
        document.mozFullScreenElement === pdfContainer || 
        document.msFullscreenElement === pdfContainer
    }
  }
}
</script>

<style lang="scss" scoped>
.contract-setting {
  padding: 20px;
  
  .contract-card {
    height: 100%;
    margin-bottom: 20px;
    
    .clearfix {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .button-right {
        margin-left: auto;
      }
    }
    
    .content-container {
      min-height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    
    .file-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 0;
      
      .file-preview {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        
        .file-icon {
          font-size: 48px;
          margin-right: 15px;
        }
        
        .file-detail {
          display: flex;
          flex-direction: column;
          
          .file-name {
            font-weight: 500;
            font-size: 16px;
            margin-bottom: 5px;
          }
          
          .file-type {
            font-size: 14px;
            color: #909399;
          }
        }
      }
      
      .file-actions {
        display: flex;
        gap: 10px;
      }
    }
    
    .upload-container {
      padding: 30px 0;
      text-align: center;
      
      .upload {
        .el-upload {
          width: 100%;
        }
      }
      
      .el-upload__tip {
        margin-top: 10px;
      }
    }
  }
  
  .pdf-container {
    width: 100%;
    height: calc(100vh - 150px);
    overflow: hidden;
    
    .pdf-iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
  }
}

// 全屏对话框样式
.pdf-preview-dialog {
  ::v-deep .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    width: 100% !important;
    height: 100% !important;
  }
  
  ::v-deep .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e9e9e9;
  }
  
  ::v-deep .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 0;
  }
  
  .pdf-container {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    
    &:fullscreen {
      width: 100vw;
      height: 100vh;
      background-color: white;
    }
  }

  .pdf-toolbar {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 5px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .pdf-iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}
</style> 