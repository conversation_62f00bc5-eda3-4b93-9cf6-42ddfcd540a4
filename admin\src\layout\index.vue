<template>
  <div :class="classObj" class="app-wrapper">
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <sidebar class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar :appointment_num="appointment_num" />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <!-- <right-panel v-if="showSettings">
        <settings />
      </right-panel> -->
    </div>
  </div>
</template>

<script>
import RightPanel from "@/components/RightPanel";
import { AppMain, Navbar, Settings, Sidebar, TagsView } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapState } from "vuex";
import { getToken } from "@/utils/auth";

export default {
  name: "Layout",
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      showSettings: (state) => state.settings.showSettings,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
  },
  data() {
    return {
      iswork: null,
      appointment_num: 0,
    };
  },
  created() {
    this.iswork = setInterval(() => {
      let toke = getToken();
      if (!toke || toke.length <= 0) return;
      this.$axios
        .get("/admin/index/iswork")
        .then((res) => {
          // console.log(res);

          this.appointment_num = res.data.appointment_num;
          console.log(this.appointment_num);

          // 更新路由红点数量
          if (res.data.order_num !== undefined) {
            this.$store.dispatch('permission/updateRouteDot', { 
              name: 'Orders', 
              value: res.data.order_num 
            })
          }
                    // 更新路由红点数量
          if (res.data.order_num !== undefined) {
            this.$store.dispatch('permission/updateRouteDot', { 
              name: 'OrderList', 
              value: res.data.order_num 
            })
          }

          if (res.data.private_num !== undefined) {
            this.$store.dispatch('permission/updateRouteDot', { 
              name: 'OrdersXs', 
              value: res.data.private_num 
            })
          }
          if (res.data.private_num !== undefined) {
            this.$store.dispatch('permission/updateRouteDot', { 
              name: 'OrderListXs', 
              value: res.data.private_num 
            })
          }
          if (res && res.data.new > 0) {
            this.$notify({
              title: "新的订单提醒",
              duration: 0,
              dangerouslyUseHTMLString: true,
              message:
                "<strong>你有（" +
                res.data.new +
                "）个新的订单，需要处理</strong>",
            });
          }
          if (res && res.data.follow > 0) {
            this.$notify({
              title: "新的跟进提醒",
              duration: 10000,
              dangerouslyUseHTMLString: true,
              message:
                "<strong>你有（" +
                res.data.follow +
                "）个跟进订单，需要处理</strong>",
            });
          }
          if (res && res.data.back > 0) {
            this.$notify({
              title: "转单申请",
              duration: 10000,
              dangerouslyUseHTMLString: true,
              message:
                "<strong>你有（" +
                res.data.back +
                "）个转单订单，需要处理</strong>",
            });
          }
          if (res && res.data.order_write_off > 0) {
            this.$notify({
              title: "订单核销",
              duration: 10000,
              dangerouslyUseHTMLString: true,
              message:
                "<strong>你有（" +
                res.data.order_write_off +
                "）个核销订单</strong>",
            });
          }
          if (res && res.data.follow_message > 0) {
            this.$notify({
              // res.data.follow_order_id
              title: "跟进提醒",
              duration: 50000,
              dangerouslyUseHTMLString: true,
              message:
                "<strong>你有（" +
                res.data.follow_message +
                "）个跟进提醒</strong>",
              onClick: () => {
                // console.log(res.data.follow_order_id);
                this.$router.push({
                  path: "/order/index/",
                  query: { id: res.data.follow_order_id, refresh: Date.now() },
                });
              },
            });
          }
        })
        .catch((err) => {
          console.log(err);
        });
    }, 30000);
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
    followDetail(orderId) {
      console.log(orderId);
      // this.$router.push({ path: "/order/index/" + orderId });
    },
    onInfo(item) {
      this.value = null;
      this.next_follow = null;
      this.$set(item, "next_follow", null);
      this.item = item;
      this.active = "follow";
      this.$axios
        .get("/admin/order/info", { params: { id: item.id } })
        .then((res) => {
          this.item = res.data;
          this.dialogVisible = true;
        })
        .catch((err) => {});
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
