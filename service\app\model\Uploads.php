<?php

namespace app\model;

/**
 * uploads 上传文件
 * @property integer $id (主键)
 * @property integer $admin_id 谁上传的
 * @property integer $filesize 文件大小
 * @property string $filepath 存储路径
 * @property string $mime mime 类型
 * @property integer $create_at 上传时间
 */
class Uploads extends base
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'mysql';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'uploads';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $pk = 'id';
}