<?php

namespace app\admin\controller;

use app\model\Admins;
use app\model\Orders;
use app\model\Works;
use support\Redis;
use support\Request;
use function Symfony\Component\Translation\t;

class WorkController extends base
{
    public function index(Request $request)
    {
        $admin_id = $request->get('id');
        $admin = $request->get('username');
        if ($admin) {
            $admin_id = Admins::where('username', $admin)->value('id');
        }

        $query = Works::with('admin')->where('status', 1)->order('start', 'desc');
        if ($admin_id) $query->where('admin_id', $admin_id);
        $list = $query->paginate($request->get('limit', 30));

        return $this->success($list->append(['total', 'oss']), '', ['oss' => Orders::OSS]);
    }

    public function saves(Request $request)
    {
        return '';
        $admin_id = $request->post('admin_id');
        $dates = $request->post('dates');
        $time = $request->post('time');
        $oss = $request->post('oss', []);

        if (empty($dates)) return $this->error(2001, '');

        $timeS = strtotime($time[0]);
        $timeE = strtotime($time[1]);

        $timeL = $timeE - $timeS;

        foreach ($dates as $date) {
            $d = strtotime($date);
            $dS = date('Y-m-d', $d) . ' ' . date('H:i:s', $timeS);
            $dE = date('Y-m-d H:i:s', strtotime($dS) + $timeL);

            $month = Date('Ym', $d);

            $works = new Works();
            $works->admin_id = $admin_id;
            $works->month = $month;
            $works->start = $dS;
            $works->end = $dE;
            $works->status = 1;
            $works->os = join(',', $oss);
            $works->save();
        }

        return $this->success($date);
    }

    public function save2(Request $request)
    {
        $times = $request->post('times');
        if (!empty($times)) {
            $times = $request->post('times');
            $os = $request->post('os');

            $start = strtotime($times[0]);
            $end = strtotime($times[1]);

            if (count($os) <= 0) return $this->error(2001, '请选择平台');
            if ($start >= $end) return $this->error(2001, '播放时间太短了');
            if ($end - $start <= 3600) return $this->error(2001, '时间太短了');
            if (time() - $start > 3600 * 24 || time() - $end) return $this->error(2001, '时间太久远了，没有办法自己排班，找主管排吧');

            $os = join(',', $os);

            Works::create([
                'admin_id' => $request->admin->id,
                'month' => date('Ym', $start),
                'os' => $os,
                'start' => date('Y-m-d H:i:s', $start),
                'end' => date('Y-m-d H:i:s', $end),
                'status' => 1,
            ]);
            return $this->success(null);
        }

        if ($request->admin->is_super == 0) return $this->error(2001, '管理员才可以添加');
        $all = $request->post();
        foreach ($all as $a) {

            if (!isset($a['times'])) continue;

            $start = strtotime($a['times'][0]);
            $end = strtotime($a['times'][1]);
            $os = join(',', $a['os']);

            Works::create([
                'admin_id' => $a['id'],
                'month' => date('Ym', $start),
                'os' => $os,
                'start' => date('Y-m-d H:i:s', $start),
                'end' => date('Y-m-d H:i:s', $end),
                'status' => 1,
            ]);
        }

        return $this->success(null);
    }

    /**
     * @保存一天的时间
     */
    public function save(Request $request)
    {
        $id = $request->post('id', 0);
        $date = $request->post('date');
        $oss = $request->post('oss');

        if ($id) {
            $item = Works::find($id);
        }
        if (empty($item)) {
            return $this->error(2101);
        }

        $start = date('Y-m-d H:i:s', strtotime($date[0]));
        $end = date('Y-m-d H:i:s', strtotime($date[1]));

        $item->start = $start;
        $item->end = $end;
        $item->status = 1;
        $item->os = join(',', $oss);

        $back = $item->save();
        if ($back)
            return $this->success($item);

        return $this->error(2003);
    }

    public function del(Request $request)
    {
        $id = $request->post('id', 0);

        if ($id) {
            $item = Works::find($id);
            if ($item) {
                $item->status = 0;
                $item->save();
                return $this->success(null);
            }
        } else {
            return $this->error(2002, '没有提交ID');
        }
    }

    public function anchor(Request $request)
    {
        $anchor = Admins::where('status', 1)->where('is_anchor', 1)->select();
        foreach ($anchor as &$an) {
            $an->os = [];
        }
        return $this->success($anchor->hidden(['password', 'remember_token']));
    }

    public function getworkstatus(Request $request)
    {
//        $workstatus = Redis::get("CRM:USER:ENDWORK:" . $request->admin->id);
        $data = Admins::where('id' , $request->admin->id)->find();
        $workstatus = !$data->is_order;
        return $this->success((bool)$workstatus);
    }
}