<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <div class="filter-container">
      <el-form :inline="true" :model="listQuery" class="demo-form-inline">
        <el-form-item label="组名">
          <el-input
            v-model="listQuery.group_name"
            placeholder="请输入组名"
            style="width: 200px"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="listQuery.status" placeholder="请选择状态" clearable>
            <el-option label="启用" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">搜索</el-button>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="success" @click="handleCreate">新增分组</el-button>
          <el-button type="info" @click="handleViewLogs">操作日志</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据列表 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="ID" width="60" align="center">
        <template slot-scope="scope">
          {{ scope.row.id }}
        </template>
      </el-table-column>

      <el-table-column label="组名" width="150" align="center">
        <template slot-scope="scope">
          {{ scope.row.group_name }}
        </template>
      </el-table-column>

      <el-table-column label="包含线路" min-width="200">
        <template slot-scope="scope">
          {{ scope.row.product_names }}
        </template>
      </el-table-column>

      <el-table-column label="排序权重" width="100" align="center">
        <template slot-scope="scope">
          {{ scope.row.sort_order }}
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            @change="handleStatusChange(scope.row)"
            active-text="启用"
            :active-value="1"
            :inactive-value="0"
            inactive-text="禁用"
          />
        </template>
      </el-table-column>

      <el-table-column label="描述" width="150">
        <template slot-scope="scope">
          {{ scope.row.description || '--' }}
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="160" align="center">
        <template slot-scope="scope">
          {{ parseTime(scope.row.created_at, '{y}-{m}-{d} {h}:{i}') }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="180" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="info"
            size="mini"
            @click="handleViewDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            type="danger"
            size="mini"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogType === 'create' ? '新增线路分组' : '编辑线路分组'"
      :visible.sync="dialogVisible"
      width="60%"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="组名" prop="group_name">
          <el-input v-model="temp.group_name" placeholder="请输入组名" />
        </el-form-item>

        <el-form-item label="包含线路" prop="product_ids">
          <el-select
            v-model="temp.product_ids"
            multiple
            filterable
            placeholder="请选择线路"
            style="width: 100%"
          >
            <el-option
              v-for="product in products"
              :key="product.id"
              :label="product.product_name"
              :value="product.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="排序权重">
          <el-input-number
            v-model="temp.sort_order"
            :min="0"
            :max="9999"
            placeholder="数字越大排序越靠前"
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-radio-group v-model="temp.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="temp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述（可选）"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="handleSave">
          确定
        </el-button>
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="线路分组详情" :visible.sync="detailDialogVisible" width="60%">
      <div v-if="currentRecord">
        <el-card class="detail-card">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">组名：</span>
                <span class="detail-value">{{ currentRecord.group_name }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">状态：</span>
                <span class="detail-value">
                  <el-tag :type="currentRecord.status === 1 ? 'success' : 'danger'">
                    {{ currentRecord.status === 1 ? '启用' : '禁用' }}
                  </el-tag>
                </span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">排序权重：</span>
                <span class="detail-value">{{ currentRecord.sort_order }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">创建时间：</span>
                <span class="detail-value">
                  {{ parseTime(currentRecord.created_at, '{y}-{m}-{d} {h}:{i}:{s}') }}
                </span>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div class="detail-item">
                <span class="detail-label">描述：</span>
                <span class="detail-value">{{ currentRecord.description || '--' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="detail-card" style="margin-top: 15px">
          <div slot="header" class="clearfix">
            <span>包含的线路</span>
          </div>
          <div v-if="currentRecord.products && currentRecord.products.length > 0">
            <el-table :data="currentRecord.products" border>
              <el-table-column label="线路ID" width="100" align="center">
                <template slot-scope="scope">
                  {{ scope.row.id }}
                </template>
              </el-table-column>
              <el-table-column label="线路名称" align="center">
                <template slot-scope="scope">
                  {{ scope.row.product_name }}
                </template>
              </el-table-column>
              <el-table-column label="第三方产品ID" width="150" align="center">
                <template slot-scope="scope">
                  {{ scope.row.third_product_id || '--' }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-else class="no-data">
            <el-empty description="暂无线路" />
          </div>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 操作日志对话框 -->
    <el-dialog title="操作日志" :visible.sync="logsDialogVisible" width="80%">
      <div class="operation-log-container">
        <!-- 筛选条件 -->
        <div class="filter-container">
          <el-form :inline="true" :model="logsQuery" class="demo-form-inline">
            <el-form-item label="操作类型">
              <el-select v-model="logsQuery.operation_type" placeholder="请选择操作类型" clearable>
                <el-option label="创建" value="create"></el-option>
                <el-option label="更新" value="update"></el-option>
                <el-option label="删除" value="delete"></el-option>
                <el-option label="启用" value="enable"></el-option>
                <el-option label="禁用" value="disable"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="logDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleLogsFilter">搜索</el-button>
              <el-button @click="resetLogsFilter">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 操作日志列表 -->
        <el-table
          v-loading="logsLoading"
          :data="logsList"
          border
          fit
          highlight-current-row
          style="width: 100%">

          <el-table-column label="操作时间" width="160" align="center">
            <template slot-scope="scope">
              {{ parseTime(scope.row.operation_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}
            </template>
          </el-table-column>

          <el-table-column label="操作者" width="120" align="center">
            <template slot-scope="scope">
              {{ (scope.row.operator && scope.row.operator.name) || '--' }}
            </template>
          </el-table-column>

          <el-table-column label="操作类型" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="getOperationTypeTag(scope.row.operation_type)">
                {{ scope.row.operation_type_name }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="变更内容" min-width="200">
            <template slot-scope="scope">
              <div v-if="scope.row.field_changes && Object.keys(scope.row.field_changes).length > 0">
                <div
                  v-for="(change, field, index) in getLimitedChanges(scope.row.field_changes)"
                  :key="field"
                  class="change-item"
                >
                  <span class="field-label">{{ change.label }}:</span>
                  <span class="old-value">{{ change.old_value || '--' }}</span>
                  <span class="arrow"> → </span>
                  <span class="new-value">{{ change.new_value || '--' }}</span>
                </div>
                <div v-if="Object.keys(scope.row.field_changes).length > 2" class="more-changes">
                  <el-button 
                    type="text" 
                    size="mini" 
                    @click="handleLogDetail(scope.row)"
                    style="color: #409EFF; padding: 0;"
                  >
                    还有 {{ Object.keys(scope.row.field_changes).length - 2 }} 项变更，查看详情
                  </el-button>
                </div>
              </div>
              <span v-else class="text-muted">{{ scope.row.remark || '--' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="IP地址" width="120" align="center">
            <template slot-scope="scope">
              {{ scope.row.ip_address || '--' }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="80" align="center">
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                @click="handleLogDetail(scope.row)"
              >
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <pagination
          v-show="logsTotal > 0"
          :total="logsTotal"
          :page.sync="logsQuery.page"
          :limit.sync="logsQuery.limit"
          @pagination="getLogsList"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="logsDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 操作日志详情对话框 -->
    <el-dialog title="操作日志详情" :visible.sync="logDetailDialog" width="60%">
      <div v-if="currentLogRecord">
        <el-card class="detail-card">
          <div slot="header" class="clearfix">
            <span>基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">操作时间：</span>
                <span class="detail-value">
                  {{ parseTime(currentLogRecord.operation_time * 1000, '{y}-{m}-{d} {h}:{i}:{s}') }}
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">操作类型：</span>
                <span class="detail-value">{{ currentLogRecord.operation_type_name }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">操作者：</span>
                <span class="detail-value">
                  {{ (currentLogRecord.operator && currentLogRecord.operator.name) || '--' }}
                  ({{ (currentLogRecord.operator && currentLogRecord.operator.username) || '--' }})
                </span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="detail-item">
                <span class="detail-label">IP地址：</span>
                <span class="detail-value">{{ currentLogRecord.ip_address || '--' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div class="detail-item">
                <span class="detail-label">备注：</span>
                <span class="detail-value">{{ currentLogRecord.remark || '--' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <el-card v-if="currentLogRecord.field_changes && Object.keys(currentLogRecord.field_changes).length > 0" class="detail-card" style="margin-top: 15px">
          <div slot="header" class="clearfix">
            <span>变更详情</span>
          </div>
          <el-table :data="getFormattedChanges(currentLogRecord.field_changes)" border>
            <el-table-column label="字段" width="120" align="center">
              <template slot-scope="scope">
                {{ scope.row.label }}
              </template>
            </el-table-column>
            <el-table-column label="变更前" align="center">
              <template slot-scope="scope">
                {{ scope.row.old_value || '--' }}
              </template>
            </el-table-column>
            <el-table-column label="变更后" align="center">
              <template slot-scope="scope">
                {{ scope.row.new_value || '--' }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="logDetailDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  fetchOtaRouteGroupList,
  fetchOtaRouteGroupDetail,
  saveOtaRouteGroup,
  deleteOtaRouteGroup,
  updateOtaRouteGroupStatus,
  fetchOtaRouteGroupProducts,
  fetchOtaRouteGroupLogs
} from '@/api/ota-route-group'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'OtaRouteGroup',
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        group_name: '',
        status: ''
      },
      dialogVisible: false,
      detailDialogVisible: false,
      logsDialogVisible: false,
      logDetailDialog: false,
      dialogType: 'create',
      temp: {
        id: null,
        group_name: '',
        product_ids: [],
        status: 1,
        sort_order: 0,
        description: ''
      },
      currentRecord: null,
      products: [],
      rules: {
        group_name: [
          { required: true, message: '请输入组名', trigger: 'blur' }
        ],
        product_ids: [
          { required: true, message: '请选择线路', trigger: 'change' }
        ]
      },
      // 操作日志相关
      logsList: [],
      logsTotal: 0,
      logsLoading: false,
      logsQuery: {
        page: 1,
        limit: 20,
        operation_type: '',
        start_time: '',
        end_time: ''
      },
      currentLogRecord: null,
      logDateRange: []
    }
  },
  created() {
    this.getList()
    this.getProducts()
  },
  methods: {
    parseTime,
    getList() {
      this.listLoading = true
      fetchOtaRouteGroupList(this.listQuery).then(response => {
        this.list = response.data.data || []
        this.total = response.data.total || 0
        this.listLoading = false
      }).catch(error => {
        console.error('获取线路分组列表失败:', error)
        this.listLoading = false
      })
    },

    getProducts() {
      fetchOtaRouteGroupProducts().then(response => {
        this.products = response.data || []
      }).catch(error => {
        console.error('获取产品列表失败:', error)
      })
    },

    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    resetFilter() {
      this.listQuery = {
        page: 1,
        limit: 20,
        group_name: '',
        status: ''
      }
      this.getList()
    },

    handleCreate() {
      this.temp = {
        id: null,
        group_name: '',
        product_ids: [],
        status: 1,
        sort_order: 0,
        description: ''
      }
      this.dialogType = 'create'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm.clearValidate()
      })
    },

    handleEdit(row) {
      // 先获取详细信息
      fetchOtaRouteGroupDetail({ id: row.id }).then(response => {
        const data = response.data
        this.temp = {
          id: data.id,
          group_name: data.group_name,
          product_ids: data.product_ids_array ? data.product_ids_array.map(id => parseInt(id)) : [],
          status: data.status,
          sort_order: data.sort_order,
          description: data.description || ''
        }
        this.dialogType = 'edit'
        this.dialogVisible = true
        this.$nextTick(() => {
          this.$refs.dataForm.clearValidate()
        })
      }).catch(error => {
        console.error('获取分组详情失败:', error)
        this.$message.error('获取分组详情失败')
      })
    },

    handleSave() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          saveOtaRouteGroup(this.temp).then(response => {
            this.$message.success('保存成功')
            this.dialogVisible = false
            this.getList()
          }).catch(error => {
            console.error('保存失败:', error)
            this.$message.error(error.response?.data?.msg || '保存失败')
          })
        }
      })
    },

    handleDelete(row) {
      this.$confirm('确定要删除该线路分组吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteOtaRouteGroup({ id: row.id }).then(response => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(error => {
          console.error('删除失败:', error)
          this.$message.error(error.response?.data?.msg || '删除失败')
        })
      })
    },

    handleStatusChange(row) {
      updateOtaRouteGroupStatus({ id: row.id, status: row.status }).then(response => {
        this.$message.success('状态更新成功')
      }).catch(error => {
        console.error('状态更新失败:', error)
        this.$message.error('状态更新失败')
        // 回滚状态
        row.status = row.status === 1 ? 0 : 1
      })
    },

    handleViewDetail(row) {
      fetchOtaRouteGroupDetail({ id: row.id }).then(response => {
        this.currentRecord = response.data
        this.detailDialogVisible = true
      }).catch(error => {
        console.error('获取详情失败:', error)
        this.$message.error('获取详情失败')
      })
    },

    // 查看操作日志
    handleViewLogs() {
      this.logsDialogVisible = true
      this.resetLogsFilter()
      this.getLogsList()
    },

    // 获取操作日志列表
    getLogsList() {
      this.logsLoading = true

      const params = { ...this.logsQuery }

      // 处理日期范围
      if (this.logDateRange && this.logDateRange.length === 2) {
        params.start_time = this.logDateRange[0]
        params.end_time = this.logDateRange[1]
      }

      fetchOtaRouteGroupLogs(params).then(response => {
        this.logsList = response.data.data || []
        this.logsTotal = response.data.total || 0
        this.logsLoading = false
      }).catch(error => {
        console.error('获取操作日志失败:', error)
        this.logsLoading = false
      })
    },

    // 操作日志筛选
    handleLogsFilter() {
      this.logsQuery.page = 1
      this.getLogsList()
    },

    // 重置日志筛选条件
    resetLogsFilter() {
      this.logsQuery = {
        page: 1,
        limit: 20,
        operation_type: '',
        start_time: '',
        end_time: ''
      }
      this.logDateRange = []
      this.getLogsList()
    },

    // 处理时间范围变更
    handleDateRangeChange(value) {
      if (value && value.length === 2) {
        this.logsQuery.start_time = value[0]
        this.logsQuery.end_time = value[1]
      } else {
        this.logsQuery.start_time = ''
        this.logsQuery.end_time = ''
      }
    },

    // 查看日志详情
    handleLogDetail(row) {
      this.currentLogRecord = row
      this.logDetailDialog = true
    },

    // 格式化变更数据
    getFormattedChanges(fieldChanges) {
      return Object.keys(fieldChanges).map(field => ({
        field: field,
        label: fieldChanges[field].label,
        old_value: fieldChanges[field].old_value,
        new_value: fieldChanges[field].new_value
      }))
    },

    // 获取限制显示的变更内容（最多2条）
    getLimitedChanges(fieldChanges) {
      const changes = {};
      const keys = Object.keys(fieldChanges);
      
      // 只取前2条
      for (let i = 0; i < Math.min(2, keys.length); i++) {
        const key = keys[i];
        changes[key] = fieldChanges[key];
      }
      
      return changes;
    },

    // 获取操作类型标签
    getOperationTypeTag(type) {
      const typeMap = {
        'create': 'success',
        'update': 'primary',
        'delete': 'danger',
        'enable': 'success',
        'disable': 'warning'
      }
      return typeMap[type] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-container {
    margin-bottom: 20px;
  }

  .detail-card {
    .el-card__header {
      background-color: #f5f7fa;
      border-bottom: 1px solid #ebeef5;
      
      .clearfix span {
        font-weight: bold;
        color: #303133;
      }
    }

    .detail-item {
      margin-bottom: 15px;
      padding: 8px 0;

      .detail-label {
        font-weight: bold;
        color: #606266;
        display: inline-block;
        min-width: 80px;
      }

      .detail-value {
        color: #303133;
        word-break: break-all;
      }
    }

    .el-row:last-child .detail-item {
      margin-bottom: 0;
    }
  }

  .no-data {
    text-align: center;
    padding: 20px 0;
  }

  // 操作日志样式
  .operation-log-container {
    .filter-container {
      margin-bottom: 20px;
    }

    .change-item {
      margin-bottom: 5px;
      font-size: 12px;

      .field-label {
        font-weight: bold;
        color: #606266;
      }

      .old-value {
        color: #F56C6C;
        text-decoration: line-through;
      }

      .arrow {
        color: #909399;
        margin: 0 5px;
      }

      .new-value {
        color: #67C23A;
        font-weight: bold;
      }
    }

    .more-changes {
      margin-top: 8px;
      padding-top: 5px;
      border-top: 1px dashed #e4e7ed;
      
      .el-button--text {
        font-size: 12px;
        color: #409EFF;
        
        &:hover {
          color: #66b1ff;
        }
      }
    }

    .text-muted {
      color: #909399;
      font-style: italic;
    }
  }
}
</style> 