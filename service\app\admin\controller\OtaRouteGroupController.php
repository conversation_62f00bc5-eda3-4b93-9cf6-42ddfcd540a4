<?php
namespace app\admin\controller;

use app\model\OtaRouteGroups;
use app\model\Products;
use app\model\AdminOperationLogs;
use support\Log;
use support\Request;

class OtaRouteGroupController extends base
{
    /**
     * 获取OTA线路分组列表
     * @param Request $request
     * @return \support\Response
     */
    public function index(Request $request)
    {
        $groupName = $request->get('group_name', '');
        $status = $request->get('status', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 20);

        $query = OtaRouteGroups::query();

        $shopId = $request->admin->shop_id;
        $query->where('shop_id', $shopId);
        // 应用搜索条件
        if (!empty($groupName)) {
            $query->where('group_name', 'like', "%{$groupName}%");
        }

        if ($status !== '') {
            $query->where('status', $status);
        }

        $groups = $query->order('sort_order', 'desc')
                       ->order('id', 'desc')
                       ->paginate($limit);

        // 为每个分组添加产品名称信息
        $groups->each(function ($group) {
            $group->product_ids_array = $group->product_ids ? explode(',', $group->product_ids) : [];
            $group->product_names = $this->getProductNames($group->product_ids_array);
        });

        return $this->success($groups);
    }

    /**
     * 查看单个OTA线路分组详情
     * @param Request $request
     * @return \support\Response
     */
    public function show(Request $request)
    {
        $id = $request->get('id', 0);

        $group = OtaRouteGroups::find($id);

        if (!$group) {
            return $this->error(404, 'OTA线路分组不存在');
        }

        // 添加格式化数据
        $group['product_ids_array'] = $group->product_ids ? explode(',', $group->product_ids) : [];
        $group['product_names'] = $this->getProductNames($group['product_ids_array']);

        // 获取产品详情
        if (!empty($group['product_ids_array'])) {
            $group['products'] = Products::whereIn('id', $group['product_ids_array'])
                                        ->field('id,product_name,third_product_id')
                                        ->select();
        } else {
            $group['products'] = [];
        }

        return $this->success($group);
    }

    /**
     * 保存OTA线路分组
     * @param Request $request
     * @return \support\Response
     */
    public function save(Request $request)
    {
        $id = $request->post('id', 0);
        $groupName = $request->post('group_name', '');
        $productIds = $request->post('product_ids', []);
        $status = $request->post('status', 1);
        $sortOrder = $request->post('sort_order', 0);
        $description = $request->post('description', '');

        // 验证必填字段
        if (empty($groupName)) {
            return $this->error(400, '组名不能为空');
        }

        // 处理产品ID
        if (is_array($productIds)) {
            $productIdsStr = implode(',', array_filter($productIds));
        } else {
            $productIdsStr = $productIds;
        }

        if ($id > 0) {
            // 更新
            $group = OtaRouteGroups::find($id);
            if (!$group) {
                return $this->error(404, 'OTA线路分组不存在');
            }
        } else {
            // 创建
            $group = new OtaRouteGroups();
            $group->shop_id = $request->admin->shop_id;
        }

        // 检查组名是否重复
        $existQuery = OtaRouteGroups::where('group_name', $groupName);
        if ($id > 0) {
            $existQuery->where('id', '<>', $id);
        }
        $existGroup = $existQuery->find();
        if ($existGroup) {
            return $this->error(400, '组名已存在');
        }
        $oldData = clone $group;

        $group->group_name = $groupName;
        $group->product_ids = $productIdsStr;
        $group->status = $status;
        $group->sort_order = $sortOrder;
        $group->description = $description;

        $result = $group->save();

        if ($result) {
            // 记录操作日志
            $operationType = $id > 0 ? 'update' : 'create';
            $remark = $id > 0 ? "更新OTA线路分组: {$groupName}" : "创建OTA线路分组: {$groupName}";


            // 更新管理员日志
            $fieldChanges = OtaRouteGroups::compareData($oldData->toArray(), $group->toArray());
            AdminOperationLogs::recordLog(
                $group->id, // 使用分组ID作为记录标识
                $request->admin, // 操作者
                $operationType,
                $fieldChanges, // 字段变更信息
                $remark,
                $request->getRealIp(),
                $request->header('User-Agent', ''),
                'ota_route_group'
            );

            return $this->success($group);
        } else {
            return $this->error(500, '保存失败');
        }
    }

    /**
     * 删除OTA线路分组
     * @param Request $request
     * @return \support\Response
     */
    public function delete(Request $request)
    {
        $id = $request->post('id', 0);

        $group = OtaRouteGroups::find($id);
        if (!$group) {
            return $this->error(404, 'OTA线路分组不存在');
        }

        // 检查是否有管理员在使用此线路组
        $adminCount = \app\model\Admins::where('route_group_ids', 'like', "%{$id}%")->count();
        if ($adminCount > 0) {
            return $this->error(400, '该线路分组正在被管理员使用，无法删除');
        }

        $groupName = $group->group_name;
        $result = $group->delete();

        if ($result) {
            // 记录操作日志
            AdminOperationLogs::recordLog(
                $group->id, // 使用分组ID作为记录标识
                $request->admin, // 操作者ID
                'delete',
                [], // 字段变更信息
                "删除OTA线路分组: {$groupName}",
                $request->getRealIp(),
                $request->header('User-Agent', ''),
                'ota_route_group'
            );

            return $this->success('删除成功');
        } else {
            return $this->error(500, '删除失败');
        }
    }

    /**
     * 更新状态
     * @param Request $request
     * @return \support\Response
     */
    public function updateStatus(Request $request)
    {
        $id = $request->post('id', 0);
        $status = $request->post('status', 1);

        $group = OtaRouteGroups::find($id);
        if (!$group) {
            return $this->error(404, 'OTA线路分组不存在');
        }

        $oldStatus = $group->status;
        $group->status = $status;
        $result = $group->save();

        if ($result) {
            // 记录操作日志
            $statusText = $status == 1 ? '启用' : '禁用';
            AdminOperationLogs::recordLog(
                $group->id, // 使用分组ID作为记录标识
                $request->admin, // 操作者
                $status == 1 ? 'enable' : 'disable',
                [], // 字段变更信息
                "OTA线路分组状态变更: {$group->group_name} - {$statusText}",
                $request->getRealIp(),
                $request->header('User-Agent', ''),
                'ota_route_group'
            );

            return $this->success($group);
        } else {
            return $this->error(500, '状态更新失败');
        }
    }

    /**
     * 获取所有启用的线路组（用于下拉选择）
     * @param Request $request
     * @return \support\Response
     */
    public function getActiveGroups(Request $request)
    {
        $groups = OtaRouteGroups::getActiveGroups($request->admin->shop_id);
        return $this->success($groups);
    }

    /**
     * 获取可选择的产品列表
     * @param Request $request
     * @return \support\Response
     */
    public function getProducts(Request $request)
    {
        $products = Products::where('status', 1)
                            ->where('shop_id', $request->admin->shop_id)
                           ->field('id,product_name,third_product_id')
                           ->order('id', 'desc')
                           ->select();

        return $this->success($products);
    }

    /**
     * 批量更新排序
     * @param Request $request
     * @return \support\Response
     */
    public function updateSort(Request $request)
    {
        $sorts = $request->post('sorts', []);

        if (empty($sorts)) {
            return $this->error(400, '排序数据不能为空');
        }

        try {
            foreach ($sorts as $sort) {
                if (isset($sort['id']) && isset($sort['sort_order'])) {
                    OtaRouteGroups::where('id', $sort['id'])
                                 ->update(['sort_order' => $sort['sort_order']]);
                }
            }
            return $this->success('排序更新成功');
        } catch (\Exception $e) {
            Log::error('更新线路组排序失败: ' . $e->getMessage());
            return $this->error(500, '排序更新失败');
        }
    }

    /**
     * 根据产品ID数组获取产品名称
     * @param array $productIds
     * @return string
     */
    private function getProductNames($productIds)
    {
        if (empty($productIds)) {
            return '暂无线路';
        }

        $products = Products::whereIn('id', $productIds)
                           ->field('product_name')
                           ->select();

        if (empty($products)) {
            return '暂无线路';
        }

        $names = [];
        foreach ($products as $product) {
            $names[] = $product->product_name;
        }

        return implode('、', array_slice($names, 0, 3)) . (count($names) > 3 ? '等' : '');
    }

    /**
     * 获取操作日志
     * @param Request $request
     * @return \support\Response
     */
    public function logs(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 20);
        $operatorId = $request->get('operator_id', '');
        $operationType = $request->get('operation_type', '');
        $startTime = $request->get('start_time', '');
        $endTime = $request->get('end_time', '');

        $params = [
            'page' => $page,
            'limit' => $limit,
            'operator_id' => $operatorId,
            'operation_type' => $operationType,
            'start_time' => $startTime,
            'end_time' => $endTime,
            'shop_id' => $request->admin->shop_id
        ];

        $result = AdminOperationLogs::getModuleLogs('ota_route_group', $params);

        return $this->success([
            'data' => $result['list'],
            'total' => $result['total']
        ]);
    }
}
