<?php
namespace app\model;

/**
 * 七陌坐席配置模型
 */
class QimoAgents extends base
{
    protected $table = 'qimo_agents';

    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;
    const STATUS_BUSY = 2;
    const STATUS_CALLING = 3;

    const STATUS_MAP = [
        self::STATUS_DISABLED => '禁用',
        self::STATUS_ENABLED => '空闲',
        self::STATUS_BUSY => '忙碌',
        self::STATUS_CALLING => '通话中',
    ];

    public function getStatusNameAttr($val)
    {
        return self::STATUS_MAP[$this->status] ?? '未知';
    }

    /**
     * 获取空闲坐席
     * @return array|null
     */
    public static function getAvailableAgent()
    {
        return self::where('status', self::STATUS_ENABLED)
            ->order('last_call_time', 'asc')
            ->find();
    }

    /**
     * 更新坐席状态
     * @param int $id
     * @param int $status
     * @return bool
     */
    public static function updateStatus($id, $status)
    {
        return self::where('id', $id)->update([
            'status' => $status,
            'last_call_time' => $status == self::STATUS_CALLING ? time() : null
        ]);
    }
}
