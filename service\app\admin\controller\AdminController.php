<?php
namespace app\admin\controller;

use app\model\Admins;
use app\model\AdminOperationLogs;
use app\model\Onlines;
use app\model\Orders;
use app\model\Products;
use support\Log;
use support\Request;
use support\Redis;

class AdminController extends base
{
    const ROUTE_LISTS = ['境内' => 10, '境外' => 20];
    const INITIAL_PASSWORD = '123456';

    public function Index(Request $request) {
        $query = Admins::order('status', 'desc')->where('shop_id', $request->admin->shop_id)->order('id', 'desc');
        if($username = $request->get('username')) {
            $query->whereLike('username', '%' . $username . '%');
        }
        if($status = $request->get('status')) {
            $query->where('status', $status);
        }
        if($is_order = $request->get('is_order')) {
            $query->where('is_order', $is_order);
        }
        if($is_private = $request->get('is_private')) {
            $query->where('is_private', $is_private);
        }

        $list = $query->paginate($request->get('limit',10));
        return $this->success($list->hidden(['password','remember_token'])->append(['ip_address']),null,['oss' => Orders::OSS]);
    }

    public function edit(Request $request) {
        $id = $request->get('id', 0);

        $info = (new Admins())->find($id);

        return $this->success($info->hidden(['password','remember_token']));
    }

    public function pwd(Request $request) {
        $oldpwd = $request->post('oldpwd', null);
        $pwd = $request->post('pwd', null);

        if(empty($pwd) || empty($oldpwd)) return $this->error(2003, '请填写密码');

        $admin = Admins::where('id', $request->admin->id)->find();
        if($admin->checkPwd($oldpwd)) {
            $admin->password = $pwd;
            $back = $admin->save();
            if($back) {
                return $this->success("");
            }
        }else{
            return $this->error(2003, '密码输入错误');
        }
        return $this->error(2003, '修改密码失败');
    }

    public function save(Request $request) {
        $id = $request->post('id',0);
        $username = $request->post('username');
        $name = $request->post('name');
        $mobile = $request->post('mobile', '');
        $is_order = $request->post('is_order', '');
        $is_anchor = $request->post('is_anchor', '');
        $is_franchisee = $request->post('is_franchisee', '');
        $product_ids = $request->post('product_ids','');
        $product_xs_ids  = $request->post('product_xs_ids','');
        $route_group_ids = $request->post('route_group_ids');
        $type = $request->post('type','0');
        $is_xs = $request->post('is_xs');

        // 记录原始数据（用于日志）
        $oldData = [];
        $isCreate = false;

        if($id) {
            $item = (new Admins())->find($id);
            $oldData = $item->toArray();
        }else{
            $item = new Admins();
            $item->pid = $request->admin->id;
            $item->shop_id = $request->admin->shop_id;
            $isCreate = true;
        }

        if(!($request->admin->is_super || $request->admin->is_franchisee)) return $this->error(2000, '超级管理员或加盟商才可以管理人员');

        // 记录是否重置密码
        $isResetPassword = false;

        if($item->id > 0) {
            if(!empty($name) && $item->name != $name) {
                $item->name = $name;
            }
            if(!empty($username) && $item->username != $username) {
                $item->username = $username;
            }
            if ($request->post('reset_passwd')) {
                $item->password = self::INITIAL_PASSWORD;
                $isResetPassword = true;
            }
        }else{
            if(empty($username)) return $this->error(2002);
            if(empty($name)) return $this->error(2003);
            $item->password = self::INITIAL_PASSWORD;
            $has = Admins::where('username', $username)->find();
            if($has) {
                return $this->error(2004, '用户已经存在,换个用户名');
            }
            $item->username = $username;
            $item->name = $name;
        }
        if (!empty($mobile)) {
            $item->mobile = $mobile;
        }
        if (is_numeric($is_order)) {
            $item->is_order = $is_order;
        }
        if (is_numeric($is_anchor)) {
            $item->is_order = $is_anchor;
        }
        if (is_numeric($is_franchisee)) {
            $item->is_franchisee = $is_franchisee;
        }
        if (is_numeric($is_xs)) {
            $item->is_xs = $is_xs;
        }

        $wechat = $request->post('wechat','');
        if (!empty($wechat)) {
            $item->wechat = $wechat;
        }
        $wechatPic = $request->post('wechat_pic','');
        if (!empty($wechatPic)) {
            $item->wechat_pic = $wechatPic;
        }
        $dyNickName = $request->post('dy_nickname','');
        if (!empty($dyNickName)) {
            $item->dy_nickname = $dyNickName;
        }
        $item->product_ids = $product_ids;
        $item->product_xs_ids = $product_xs_ids;
        if ($route_group_ids !== null) {
            $item->route_group_ids = $route_group_ids;
        }
        $item->type = $type;
        $back = $item->save();

        if($back) {
            // 记录操作日志
            $newData = $item->toArray();

            if ($isCreate) {
                // 创建管理员日志
                AdminOperationLogs::recordLog(
                    $item->id,
                    $request->admin,
                    'create',
                    [],
                    '创建新管理员',
                    $request->getRealIp(),
                    $request->header('user-agent', '')
                );
            } else {
                // 更新管理员日志
                $fieldChanges = AdminOperationLogs::compareAdminData($oldData, $newData);

                if (!empty($fieldChanges)) {
                    AdminOperationLogs::recordLog(
                        $item->id,
                        $request->admin,
                        'update',
                        $fieldChanges,
                        '更新管理员信息',
                        $request->getRealIp(),
                        $request->header('user-agent', '')
                    );
                }

                // 如果重置了密码，单独记录
                if ($isResetPassword) {
                    AdminOperationLogs::recordLog(
                        $item->id,
                        $request->admin,
                        'reset_password',
                        [],
                        '重置管理员密码',
                        $request->getRealIp(),
                        $request->header('user-agent', '')
                    );
                }
            }

            return $this->success($item->hidden(['password','remember_token']));
        }
        return $this->error(2003, '写入数据库错误');
    }

    //开启和关闭
    public function disabled(Request $request) {
        $id = $request->post('id',0);
        $status = $request->post('status',0);
        $status = $status == 1? 1:0;

        if(!$request->admin->is_super && !$request->admin->is_franchisee) return $this->error(2000, '超级管理员才可以管理人员');

        if($id) {
            $item = Admins::find($id);
            $oldStatus = $item->status;
            $item->status = $status;
            $back = $item->save();
            if($back) {
                if($status == 0) {
                    Redis::set('Admin:disabled:'.$item->id, time() , 'EX', 3600*24*10);
                }else{
                    Redis::del('Admin:disabled:'.$item->id);
                }

                // 记录状态变更日志
                $operationType = $status == 1 ? 'enable' : 'disable';
                $remark = $status == 1 ? '启用管理员' : '禁用管理员';

                AdminOperationLogs::recordLog(
                    $item->id,
                    $request->admin,
                    $operationType,
                    [
                        'status' => [
                            'label' => '状态',
                            'old_value' => $oldStatus == 1 ? '启用' : '禁用',
                            'new_value' => $status == 1 ? '启用' : '禁用'
                        ]
                    ],
                    $remark,
                    $request->getRealIp(),
                    $request->header('user-agent', '')
                );

                return $this->success($item->hidden(['password','remember_token']));
            }else{
                return $this->error(2001); //找不到管理员
            }
        }
        return $this->error(2001); //找不到管理员
    }

    public function getOnlineList(Request $request)
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);

        $query = Admins::where('status',1)->where('shop_id', $request->admin->shop_id);

        if ($request->get('username')) {
            $query->whereLike('username|name', '%' . $request->get('username') . '%');
        }

        // 获取分页数据
        $paginator = $query->paginate($limit);
        $users = $paginator->items();
        $total = $paginator->total();

        $list = [];
        foreach ($users as $user){
            $adminId = $user->id;

            $list[$adminId]['isEndWork'] = 0;
            if ($user->is_order == 1){
                $list[$adminId]['isEndWork'] = 1;//是否分单
            }

            if ($user->is_private == 1){
                $list[$adminId]['is_private'] = 1;//是否分单
            }

            $list[$adminId]['last_work_time'] = $user->last_work_time;
            $list[$adminId]['isOnline'] = 0;
            if (Redis::get('CRM:USER:ONLINE:'.$adminId)){
                $list[$adminId]['isOnline'] = 1;
                $list[$adminId]['last_work_time'] = 0;//在线时不用展示
            }

            if (!$list[$adminId]['isOnline']) {
                $list[$adminId]['onlineTime'] = strtotime(date('Y-m-d H:i:00',$user->end_work_time)) - strtotime(date('Y-m-d H:i:00',$user->start_work_time));
            }else{
                $list[$adminId]['onlineTime'] = $user->last_work_time > 0 ? strtotime(date('Y-m-d H:i:00',$user->last_work_time)) - strtotime(date('Y-m-d H:i:00',$user->start_work_time)) : 0;
            }
            if ($user->start_work_time <=0 || $list[$adminId]['onlineTime'] < 0){
                $list[$adminId]['onlineTime'] = 0;
            }
            $list[$adminId]['start_work_time'] = $user->start_work_time;
            $list[$adminId]['end_work_time'] = $user->end_work_time;
            $list[$adminId]['order_num'] = $user->order_num;
            $list[$adminId]['id'] = $adminId;
            $list[$adminId]['username'] = $user->username;
            $list[$adminId]['route_type'] = $user->route_type;
            $list[$adminId]['routes'] = [];
            if ($user->product_ids) {
                $list[$adminId]['routes'] = Products::query()->field(['product_name'])->whereIn('id', explode(',', $user->product_ids))->select();
            }
        }

        return $this->success([
            'data' => array_values($list),
            'total' => $total,
            'current_page' => $page,
            'per_page' => $limit
        ]);
    }

    public function editInfo(Request $request)
    {
        $post = $request->post();

        if (empty($post['id'])){
            $post['id'] = $request->admin->id;
        }

        $post['end_work_time'] = time();
        if ((isset($post['is_order']) && $post['is_order'] == 1) || (isset($post['is_private']) && $post['is_private'] == 1)){
            $post['end_work_time'] = 0;
        } else {
            // 判断在线时长，低于8小时不可下线
            $admin = Admins::where('id', $request->admin->id)->find();
            $workTime = time() - $admin->start_work_time;
            if ($admin->is_super == 0 && $workTime < 60*60*8) {
                return $this->error(1004,'未满8小时暂不能下线，请联系管理员');
            }
        }

        try {
            $oldData = Admins::find($post['id']);
            Admins::update($post);

            // 记录状态变更日志
            $operationType = 'update';
            $fieldChanges = AdminOperationLogs::compareAdminData($oldData->toArray(), $post);
            if (!empty($fieldChanges)) {
                AdminOperationLogs::recordLog(
                    $post['id'],
                    $request->admin,
                    $operationType,
                    $fieldChanges,
                    '更新管理员信息',
                    $request->getRealIp(),
                    $request->header('user-agent', '')
                );
            }
        }catch (\Exception $e){
            return $this->error(2001,$e->getMessage());
        }
        Log::info('editInfo:operate admin_id:' . $request->admin->id . ',logs:' . json_encode($post));

        return $this->success(true);
    }
}
