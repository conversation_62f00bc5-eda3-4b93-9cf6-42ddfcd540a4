<?php

namespace app\common;

use support\Log;
use ReflectionClass;

/**
 * @method static string SUPPORT_BLACKS()
 */
class Keys {
    const SUPPORT_BLACKS = "SUPPORT:BLACKS";

    //
    // ------------------------------------------------------------------------------
    //   请 在 上 方 设 置 Redis Key
    // ------------------------------------------------------------------------------
    private static $instance = null;

    /**
     * 简单的使用魔术函数，sprintf() 输出
     * @param $name
     * @param $arguments
     * @return string
     */
    public static function __callStatic($name, $arguments)
    {
        if (is_null(self::$instance)) {
            try {
                self::$instance = (new ReflectionClass(__CLASS__))->getConstants();
            } catch (\ReflectionException $e) {
                Log::error(__METHOD__, [$e->getMessage(),$e->getCode()]);
            }

        }
        if (isset(self::$instance[$name])) {
            $format = self::$instance[$name];
        } else {
            $format = '';
        }

        return vsprintf($format, $arguments);
    }
}
    
