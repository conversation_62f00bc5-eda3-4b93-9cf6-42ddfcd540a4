<template>
  <el-dialog
    title="编辑直播间"
    :visible.sync="dialogVisible"
    width="30%"
    :before-close="handleClose"
  >
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="直播间id" prop="product_ids">
        <el-input v-model="form.product_ids"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleEdit">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      dialogVisible: false,
      form: {
        product_ids: "",
        live_room_id: "",
      },
    };
  },
  methods: {
    init(live_room_id, product_ids) {
      this.dialogVisible = true;
      this.form.product_ids = product_ids;
      this.form.live_room_id = live_room_id;
      console.log(this.form);
    },

    handleClose() {
      this.dialogVisible = false;
    },
    handleEdit() {
      this.dialogVisible = false;
      this.$emit("update", this.form);
    },
  },
};
</script>

<style scoped lang="scss"></style>
