<?php

namespace app\admin\controller;

use app\model\ItineraryInventoryModel;
use app\model\ItineraryModel;
use support\Request;
use support\Response;

class Inventory extends base
{
    /**
     * 获取行程指定月份的库存数据
     * @param Request $request
     * @return Response
     */
    public function month(Request $request): Response
    {
        $itineraryId = $request->input('itineraryId');
        $month = $request->input('month'); // 格式：YYYY-MM

        if (!$itineraryId || !$month) {
            return error(400, '参数错误');
        }

        try {
            // 检查行程是否存在
            $itineraryModel = new ItineraryModel();
            $itinerary = $itineraryModel->where('id', $itineraryId)->where('delete_time', 0)->find();

            if (!$itinerary) {
                return error(404, '行程不存在1');
            }

            $model = new ItineraryInventoryModel();
            $items = $model->getMonthInventory($itineraryId, $month);

            return success([
                'items' => $items
            ]);
        } catch (\Exception $e) {
            return error(500, '获取失败：' . $e->getMessage() . $e->getFile() . $e->getLine());
        }
    }

    /**
     * 批量更新行程库存
     * @param Request $request
     * @return Response
     */
    public function batchUpdate(Request $request): Response
    {
        $data = $request->post();

        // 验证参数
        if (empty($data['itineraryId']) || empty($data['dateRanges']) || !isset($data['status'])) {
            return error(400, '参数错误');
        }

        $itineraryId = $data['itineraryId'];
        $dateRanges = [$data['dateRanges']];
        $selectedDays = $data['selectedDays'] ?? [];
        $status = (int)$data['status'];
        $limit = $data['limit']; // null 表示不限制
        $surcharges = $data['surcharges'] ?? null;

        try {
            // 检查行程是否存在
            $itineraryModel = new ItineraryModel();
            $itinerary = $itineraryModel->where('id', $itineraryId)->where('delete_time', 0)->find();

            if (!$itinerary) {
                return error(404, '行程不存在');
            }

            // 将所选日期转换为正确的格式
            $dayMap = [
                'MON' => 'Mon',
                'TUE' => 'Tue',
                'WED' => 'Wed',
                'THU' => 'Thu',
                'FRI' => 'Fri',
                'SAT' => 'Sat',
                'SUN' => 'Sun'
            ];

            $formattedSelectedDays = [];
            foreach ($selectedDays as $day) {
                if (isset($dayMap[$day])) {
                    $formattedSelectedDays[] = $dayMap[$day];
                }
            }

            // 生成所有需要更新的日期
            $dates = [];
            foreach ($dateRanges as $range) {
                $startDate = $range['startDate'];
                $endDate = $range['endDate'];

                $currentDate = $startDate;
                // 如果没有选中特定周几，则更新所有日期
                if (empty($formattedSelectedDays)) {
                    while (strtotime($currentDate) <= strtotime($endDate)) {
                        // 检查日期是否在有效期内
                        if ($this->isDateInValidRange($currentDate, $itinerary)) {
                            $dates[] = $currentDate;
                        }
                        $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
                    }
                } else {
                    // 仅更新选中的周几
                    while (strtotime($currentDate) <= strtotime($endDate)) {
                        $dayOfWeek = date('D', strtotime($currentDate));
                        if (in_array($dayOfWeek, $formattedSelectedDays)) {
                            // 检查日期是否在有效期内
                            if ($this->isDateInValidRange($currentDate, $itinerary)) {
                                $dates[] = $currentDate;
                            }
                        }
                        $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
                    }
                }
            }

            // 去重
            $dates = array_unique($dates);

            if (empty($dates)) {
                return error(400, '没有可更新的有效日期');
            }

            // 批量更新
            $model = new ItineraryInventoryModel();
            $result = $model->batchUpdate($itineraryId, $dates, $status, $limit, $surcharges);

            // 返回更新后的结果
            return success([
                'updatedDates' => count($dates),
                'dates' => $dates
            ], '更新成功');
        } catch (\Exception $e) {
            return error(500, '更新失败：' . $e->getMessage() . $e->getFile() . $e->getLine());
        }
    }

    /**
     * 查询单个日期库存详情
     * @param Request $request
     * @return Response
     */
    public function date(Request $request): Response
    {
        $itineraryId = $request->input('itineraryId');
        $date = $request->input('date');

        if (!$itineraryId || !$date) {
            return error(400, '参数错误');
        }

        try {
            // 检查行程是否存在
            $itineraryModel = new ItineraryModel();
            $itinerary = $itineraryModel->where('id', $itineraryId)->where('delete_time', 0)->find();

            if (!$itinerary) {
                return error(404, '行程不存在');
            }

            // 检查日期是否在有效期内
            if (!$this->isDateInValidRange($date, $itinerary)) {
                return error(400, '所查询日期不在行程有效期内');
            }

            $model = new ItineraryInventoryModel();
            $inventory = $model->getDateInventory($itineraryId, $date);
            $inventory['itinerary'] = [
                'id' => $itinerary['id'],
                'title' => $itinerary['title'],
                'validFrom' => $itinerary['valid_from'],
                'validTo' => $itinerary['valid_to'],
                'isLongTerm' => $itinerary['is_long_term'],
                'deposit' => $itinerary['deposit'],
                'promotionPrice' => $itinerary['promotion_price'],
                'singleRoomSurcharge' => $itinerary['single_room_surcharge'],
                'roomUpgradeSurcharge' => $itinerary['room_upgrade_surcharge'],
            ];
            return success($inventory);
        } catch (\Exception $e) {
            return error(500, '获取失败：' . $e->getMessage());
        }
    }

    /**
     * 更新单个日期库存
     * @param Request $request
     * @return Response
     */
    public function update(Request $request): Response
    {
        $data = $request->post();

        // 验证参数
        if (empty($data['itineraryId']) || empty($data['date']) || !isset($data['status'])) {
            return error(400, '参数错误');
        }

        $itineraryId = $data['itineraryId'];
        $date = $data['date'];
        $status = (int)$data['status'];
        $limit = $data['limit']; // null 表示不限制
        $surcharges = $data['surcharges'] ?? null;

        try {
            // 检查行程是否存在
            $itineraryModel = new ItineraryModel();
            $itinerary = $itineraryModel->where('id', $itineraryId)->where('delete_time', 0)->find();

            if (!$itinerary) {
                return error(404, '行程不存在');
            }

            // 检查日期是否在有效期内
            if (!$this->isDateInValidRange($date, $itinerary)) {
                return error(400, '所更新日期不在行程有效期内');
            }

            $model = new ItineraryInventoryModel();
            $model->batchUpdate($itineraryId, [$date], $status, $limit, $surcharges);

            // 返回更新后的库存数据
            $inventory = $model->getDateInventory($itineraryId, $date);

            return success($inventory, '更新成功');
        } catch (\Exception $e) {
            return error(500, '更新失败：' . $e->getMessage());
        }
    }

    /**
     * 检查日期是否在行程有效期范围内
     * @param string $date 日期
     * @param array|object $itinerary 行程信息
     * @return bool
     */
    private function isDateInValidRange(string $date, $itinerary): bool
    {
        // 如果是长期有效，则不需要检查有效期
        if (!empty($itinerary['is_long_term'])) {
            return true;
        }

        $checkDate = strtotime($date);
        $validFrom = !empty($itinerary['valid_from']) ? strtotime($itinerary['valid_from']) : null;
        $validTo = !empty($itinerary['valid_to']) ? strtotime($itinerary['valid_to']) : null;

        // 没有设置有效期，则默认有效
        if ($validFrom === null && $validTo === null) {
            return true;
        }

        // 只设置了开始日期
        if ($validFrom !== null && $validTo === null) {
            return $checkDate >= $validFrom;
        }

        // 只设置了结束日期
        if ($validFrom === null && $validTo !== null) {
            return $checkDate <= $validTo;
        }

        // 两个日期都设置了
        return $checkDate >= $validFrom && $checkDate <= $validTo;
    }
}
