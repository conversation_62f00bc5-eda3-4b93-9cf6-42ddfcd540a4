<?php

namespace app\admin\controller;

use app\model\Announcements;
use support\Request;

class AnnouncementsController extends base
{
    /**
     * 列表
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DbException
     */
    public function index(Request $request)
    {
        if (!$request->admin->is_super) return $this->error(2000, '管理员才能查看');

        $list = Announcements::with('adminInfo')->order('id desc')->paginate($request->get('limit',10));

        return $this->success($list);
    }

    /**
     * 添加
     * @param Request $request
     * @return \support\Response
     */
    public function add(Request $request)
    {
        $title = $request->post('title');
        $content = $request->post('content');

        if (empty($title) || empty($content)) {
            return $this->error(2001, '请填写完整参数');
        }

        $annModel = new Announcements();
        $annModel->title = $title;
        $annModel->content = $content;
        $annModel->admin_id = $request->admin->id;

        if ($annModel->save()) {
            return $this->success(null, '添加成功');
        }

        return $this->error(2000, '添加失败');
    }

    /**
     * 获取详情
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function detail(Request $request)
    {
        $id = $request->post('id');

        $data = Announcements::find(['id' => $id]);

        return $this->success($data);
    }

    /**
     * 编辑
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function edit(Request $request)
    {
        $title = $request->post('title');
        $content = $request->post('content');
        $id = $request->post('id');

        if (empty($title) || empty($content)) {
            return $this->error(2001, '请填写完整参数');
        }

        $annModel = Announcements::find(['id' => $id]);
        $annModel->title = $title;
        $annModel->content = $content;
        $annModel->admin_id = $request->admin->id;

        if ($annModel->save()) {
            return $this->success(null, '修改成功');
        }

        return $this->error(2000, '修改失败');
    }

    /**
     * 拉取最新的
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getLast()
    {
        $data = Announcements::order('id desc')->find();

        return $this->success($data);
    }
}