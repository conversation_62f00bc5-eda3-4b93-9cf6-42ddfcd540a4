<?php

namespace app\server;

use app\common\Error;
use app\model\Admins;
use app\model\Admins as AdminsModel;
use app\model\Blacks;
use app\model\FilterMobiles;
use app\model\Finances as FinancesModel;
use app\model\Line;
use app\model\LiveRoomWorks;
use app\model\OrderAllocateLogs;
use app\model\OrdersXs as OrdersModel;
use app\model\Products;
use Carbon\Carbon;
use http\Exception\InvalidArgumentException;
use support\Log;
use support\Redis;

class OrdersXs {
    protected static $redisPool = [];
    public static function isDaishiyong(OrdersModel $order): bool
    {
        // 根据 OrdersModel::AllOssStatusSql[1] 进行判断
        // ((os=1 and order_status=3) or (os=2 and order_status=4) or (os=3 and order_status=1))
        return (in_array($order->os, [1, 7]) && $order->order_status == 3)
            || ($order->os == 2 && $order->order_status == 4)
            || (in_array($order->os, [3, 5]) && in_array($order->order_status, [1, 2, 5]))
            || (in_array($order->os, [6, 8]) && in_array($order->order_status, [200, 205, 210, 300, 310]));
    }

    /**
     * @params []OrdersModel $order
     * @return array
     */
    public static function reminderOrders(OrdersModel ...$orders)
    {
        $admin_ids = [];
        $sns = [];

        foreach ($orders as $order) {
            $admin_ids[] = $order->admin_id;
            $sns[] = $order->sn;
        }

        $admins = AdminsModel::whereIn('id', $admin_ids)->select()->column('mobile', 'id');

        if (empty($admins)) {
            return array_fill_keys($sns, Error::undefined('admin check error'));
        }

        $ttl    = 86400;
        $result = [];

        $admins = array_column($admins, null, 'id');
        foreach($orders as $order) {
            $admin_mobile = $admins[$order->admin_id] ?? '';

            if (empty($order->mobile) || empty($admin_mobile)) {
                $result[$order->sn] = Error::undefined('client mobile or admin mobile invalid');
                continue;
            }
            if (FilterMobiles::isFilterMobile($order->mobile)) {
                $result[$order->sn] = Error::undefined('刷单账单');
                continue;
            }
            $admin_mobile = $admin_mobile['mobile'] ?? '';

            if (Blacks::CExists($order->mobile)) {
                $result[$order->sn] = Error::ERR_SMS_BLACKS;
                Log::info(__METHOD__ . " blacks", [$order->mobile, $order->sn]);
                continue;
            }

            $key = sprintf("CRM:%s:%s_%s", 'reminderOrders', $order->sn, $order->mobile);
            $ok  = Redis::set($key, time(), 'EX', $ttl, 'NX');

            if (!$ok) {
                $result[$order->sn] = Error::undefined('reminder cooldown');
                continue;
            }

            $res = SMS::juhe_sms_send($order->mobile, SMS::JUHE_TMP_REMINDER_ORDER, ['title' => $order->product_name, 'mobile' => $admin_mobile]);
            $err_code = $res['error_code'] ?? 0;

            if ($err_code != 0) {
                Log::error(__METHOD__ . " send error", [$res, $order->mobile, $order->sn]);
                $result[$order->sn] = Error::ERR_SMS_SEND_FAIL;
                continue;
            }

            $result[$order->sn] = [];
        }

        return count($orders) > 1 ? $result : reset($result);
    }

    public static function syncFromThird(OrdersModel $order)
    {
        $redisKey = 'sync_order:' . $order->sn;
        if (Redis::exists($redisKey)) {
            return 1;
        }
        Redis::setEx($redisKey, mt_rand(300, 600), 1);
        $got = null;

        switch ($order->os) {
            case 1:
            case 7:
                $mt = new Meituan($order->os);
                $it = $mt->get(1, null, null, $order->sn);

                if ($it) {
                    $got = $it[0];
                }

                break;
            case 3:
            case 5:
                $dy = new Douyin($order->os);
                $it = $dy->get(1, null, null, $order->sn);
                if ($it) {
                    $got = $it[0];

                    // 查询未预约状态
                    if ($order->appointment_status == 0) {
                        $appointment = $dy->_orderMakeAppointmentStatus($order->sn);
                        if ($appointment !== null) {
                            $got['appointment_status'] = 1;
                        }
                    }
                }

                break;
            case 6:
            case 8:
                $tc = new Tongcheng($order->os);
                $it = $tc->get(1, null, null, $order->sn);

                if ($it) {
                    $got = $it[0];
                }
                break;
        }

        if (is_null($got)) {
            Log::info(__METHOD__ . ": get from os is null", [$order]);
            return;
        }

        $back = $order->save($got);

        if ($back) {
            self::finance(0, $order->id, $order->asset_price);
        }

        return 0;
    }

    public static function finance($type = 1, $order_id = 0, $price = 0)
    {
        //总的关于这个订单的金额
        $total = FinancesModel::where('order_id', $order_id)->sum('total');
        //如果总金额大于提交上来的核销金额,那就是退费的
        //如果提交上来的金额小于总金额,那就是核销的

        if ($total > $price) {
            $type = 2;
            $fee = -($total - $price);
        } elseif ($total < $price) {
            $type = 1;
            $fee = $price - $total;
        } else {
            return;
        }

        FinancesModel::create([
            'order_id' => $order_id,
            'type' => $type,
            'total' => $fee,
            'status' => 1
        ]);

        return;
    }

    /**
     * 同步第三方返回
     * @param OrdersModel $order
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function syncFromThirdV2(OrdersModel $order)
    {
        $got = null;
        switch ($order->os) {
            case 1:
            case 7:
                $mt = new Meituan($order->os);
                $it = $mt->get(1, null, null, $order->sn);

                if ($it) {
                    $got = $it[0];
                }

                break;
            case 3:
            case 5:
                $dy = new Douyin($order->os);
                $it = $dy->get(1, null, null, $order->sn);
                if ($it) {
                    $got = $it[0];
                }

                break;
        }

        if (is_null($got)) {
            Log::info(__METHOD__ . ": get from os is null", [$order]);
            return $order;
        }

        $back = $order->save($got);

        if ($back) {
            self::finance(0, $order->id, $order->asset_price);
        }

        return $order;
    }

    /**
     * @param int $orderCreateTime
     * @param     $productId
     * @return array|mixed|\think\Model
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getLiveRoomWork(int $orderCreateTime, $productId) {
        $orderCreateDate = date('Y-m-d H:i:s', $orderCreateTime/1000);
        $roomWork = LiveRoomWorks::join('live_room', 'live_room_works.live_room_id=live_room.id')
            ->where('live_room_works.start', '<', $orderCreateDate)
            ->where('live_room_works.end', '>', $orderCreateDate)
            ->whereRaw(sprintf('find_in_set(%s, live_room.`product_ids`)', $productId))
            ->field(['live_room_works.id'])
            ->find();

        return $roomWork;
    }

    /**
     * 分配用户
     * @param int    $status
     * @param string $categoryDesc
     * @return int|string
     * @throws \Exception
     */
    public static function poolUser($status, $thirdProductId) {
        $status .= $thirdProductId;
//        if (empty(self::$redisPool[$status])) {
        self::$redisPool[$status] = Redis::hGetAll('CRM:Pool:' . $status);
        $users = self::users($thirdProductId);
        shuffle($users);
        $_users = [];
        if (empty(self::$redisPool[$status])) {
            foreach ($users as $user) {
                $_users[$user->username] = 0;
                Redis::hSet('CRM:Pool:' . $status, $user->username, 0);
            }
            self::$redisPool[$status] = $_users;
        } else {
            asort(self::$redisPool[$status]);
            $key_users = array_keys(self::$redisPool[$status]);
            $username = $key_users[0];
            $max = self::$redisPool[$status][$username];
            $_users = [];
            foreach ($users as $user) {
                $_users[] = $user->username;
                if (!in_array($user->username, $key_users)) {
                    self::$redisPool[$status][$username] = $max;
                    Redis::hSet('CRM:Pool:' . $status, $user->username, $max);
                }
            }
            foreach (self::$redisPool[$status] as $username => $val) {
                if (!in_array($username, $_users)) {
                    unset(self::$redisPool[$status][$username]);
                    Redis::hDel('CRM:Pool:' . $status, $username);
                }
            }
        }
//        }

        $username = null;
        try {
            $pool = self::$redisPool[$status];
            // 根据今日分配数量再排序
            if (empty($pool)) $pool = [];
            asort($pool);
            $keys = array_keys($pool);
            if (empty($keys)) {
                throw new \Exception('没有可以分配的用户');
            }
            $username = $keys[0];
            self::$redisPool[$status][$username] += 1;
            Redis::hIncrBy('CRM:Pool:' . $status, $username, 1);
        } catch (\Exception $e) {
            Log::error(dirname(__FILE__) . __LINE__ . '没有可以分配的用户', func_get_args());
            throw new \Exception('没有可以分配的用户');
        }
        return $username;
    }

    /**
     * 记录订单分配日志
     * @param string $orderSn 订单号
     * @param int $adminId 客服ID
     * @param int $shopId 门店ID
     * @param string $allocateMethod 分配方式
     * @param array $extraData 额外数据
     * @return bool
     */
    public static function recordAllocateLog($orderSn, $adminId, $shopId, $allocateMethod = '', $extraData = [])
    {
        try {
            // 获取管理员的线路权限信息
            $admin = Admins::where('id', $adminId)->field(['route_type', 'product_xs_ids'])->find();
            $routePermission = '';

            if ($admin) {
                $routePermission = sprintf('线路组:%s,私域产品权限:%s',
                    $admin->route_group_ids ?? '未设置',
                    $admin->product_xs_ids ?? '未设置'
                );
            }

            // 添加分配方式到额外数据
            $extraData['allocate_method'] = $allocateMethod;
            $extraData['allocate_timestamp'] = time();
            $extraData['order_type'] = 'xs'; // 标记为私域订单

            return OrderAllocateLogs::recordAllocateLog(
                $orderSn,
                $adminId,
                $shopId,
                $routePermission,
                $extraData
            );
        } catch (\Exception $e) {
            Log::error('记录私域订单分配日志失败', [
                'order_sn' => $orderSn,
                'admin_id' => $adminId,
                'shop_id' => $shopId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function softBytoday($status) {
        $pools = self::$redisPool[$status];
        $newUsers = [];
        foreach ($pools as $key => $pool) {
            array_push($newUsers, [
                'name' => $key,
                'num' => $pool,
                'today_num' => 11
            ]);
        }
        $data = [
            ['name' => 'Tom', 'age' => 25, 'score' => 80],
            ['name' => 'Amy', 'age' => 30, 'score' => 90],
            ['name' => 'Tom', 'age' => 20, 'score' => 85]
        ];

        // 提取排序字段
        $names = array_column($data, 'name');
        $ages = array_column($data, 'age');
        $scores = array_column($data, 'score');

        // 按 name升序 → age降序 → score升序
        array_multisort(
            $names, SORT_ASC,
            $ages, SORT_DESC,
            $scores, SORT_ASC,
            $data
        );
        return $data;
    }

    /**
     * @param $thirdProductId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    protected static function users($thirdProductId) {
        $product = Products::query()->where('third_product_id', $thirdProductId)->find();
        if (empty($product)) {
            return [];
        }

        $users = Admins::where('status', 1)->where('is_private', 1)->whereFindInSet('product_xs_ids', $product->id)->select();
        $us = [];
        foreach ($users as $u) {
            $ru = Redis::get('CRM:USER:ONLINE:' . $u->id);
            if (empty($ru)) continue;

            if (!self::checkWorkTimeCondition($u->start_work_time)) {
                continue;
            }

            $_u = new \stdClass();
            $_u->username = $u->id;
            $us[] = $_u;
        };

        return $us;
    }

    /**
     * 判断工作时间是否在上午，且当前时间早于当日13:30
     * @param int $workStartTimestamp 精确到秒的时间戳
     * @return bool
     */
    private static function checkWorkTimeCondition(int  $workStartTimestamp): bool {
        $isLegal = false;
        try {
            date_default_timezone_set('Asia/Shanghai');
            if ($workStartTimestamp < 0) {
                throw new InvalidArgumentException("时间戳不能为负数");
            }
            $now = Carbon::now();
            $startToday = Carbon::now()->setTime(9, 10, 0);
            $deadlineToday = Carbon::now()->setTime(22, 0);
            if ($now > $startToday && $now < $deadlineToday) {
                $isLegal = true;
            }

            //            $workStart = (new \DateTime())->setTimestamp($workStartTimestamp);
            //            $isMorning = (int)$workStart->format('H') < 12; // 是否是早班
            //            $currentTime = new \DateTime('now');
            //            $startToday = (clone $currentTime)->setTime(9, 10, 0);
            //            $deadlineToday = (clone $currentTime)->setTime(13, 30, 0);
            //
            //            if ($isMorning && $currentTime > $startToday && ($currentTime < $deadlineToday)) {
            //                $isLegal = true;
            //            }
            //
            //            // 下午上线的用户，2.10开始分单
            //            $startAfternoon = (new $currentTime)->setTime(14, 10);
            //            if (!$isMorning && $currentTime > $startAfternoon) {
            //                $isLegal = true;
            //            }
        } catch (\Exception $e) {
            throw new InvalidArgumentException("时间处理错误: " . $e->getMessage());
        }

        return $isLegal;
    }

    /**
     * 发送订单短信
     * @param $admin_id
     * @param $order
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function sendOrderSms($admin_id, $order)
    {
        return;
        $user = Admins::cache(true)->where('id', $admin_id)->find();
        if ((!config('app.debug', true) || config('app.debug', true) === 'false') && (time() * 1000 - $order->create_at) / 1000 < 2 * 24 * 3600) {
            $has = Blacks::where('mobile', $order->mobile)->find();
            if (empty($has) && !empty($order->mobile)) {
                if (env('SMS_JUMP_URL')) {
                    SMS::juhe_sms_send($order->mobile, 265867, ['title' => $order->product_name, 'mobile' => $user->mobile, 'url' => env('SMS_JUMP_URL')]);
                } else {
                    SMS::juhe_sms_send($order->mobile, 261607, ['title' => $order->product_name, 'mobile' => $user->mobile]);
                }
            } else {
                sleep(10);
            }
        } else {
            Log::info('订单未发送短息：' . json_encode([$order->mobile, 261607, ['title' => $order->product_name, 'mobile' => $user->mobile]]));
        }
    }

    /**
     * 0 - 私域线路, 1 - 手机号码, 2 - 微信号 3 - 等级, 4 - 来源, 5 - 备注
     * @param array $exportData
     */
    public static function batchImportOrder($admin, array &$exportData) {
        $faild = [];
        $sources = array_flip(Line::SOURCE_MAP);
        $leveles = array_flip(Line::LEVELS_MAP);
        $adminId = 0;
        if ($admin->is_super != 1 && $admin->is_private == 1) {
            $adminId = $admin->id;
        }
        foreach ($exportData as $k => $datum) {
            if (!$k) {
                unset($exportData[$k]);
                continue;
            }
            list($productName, $mobile, $wechat, $level, $sourceName, $remark) = $datum;
            if (!$productName) {
                unset($exportData[$k]);
                continue;
            }
            $product = Products::where(['product_name' => $productName])->where('is_private', 1)->find();
            if (empty($product)) {
                $error = compact('productName', 'mobile', 'wechat', 'remark');
                $error['error_msg'] = '私域线路未找到';
                $error['line'] = $k+1;
                $faild[] = $error;
                continue;
            }
            $order = new \app\model\OrdersXs();
            $order->sn = getTradeNo('XS');
            $order->product_id = $product->third_product_id;
            $order->product_name = $product->product_name;
            $order->os = $sources[$sourceName] ?? 9;
            $order->wechat = $wechat;
            $order->create_at = time().'000';
            $order->mobile = $mobile;
            $order->remark = $remark;
            $order->level = $leveles[$level] ?? 1;
            $order->fans_status = 1;
            $order->shop_id = $admin->shop_id;
            $order->admin_id = $adminId;
            if (!$order->save()) {
                $error = compact('productName', 'mobile', 'wechat', 'remark');
                $error['error_msg'] = '操作失败';
                $error['line'] = $k+1;
                $faild[] = $error;
            }
        }

        return $faild;
    }
}
