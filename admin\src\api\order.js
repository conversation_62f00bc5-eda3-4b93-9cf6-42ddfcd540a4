import request from '@/utils/request'

export function orderBack(data) {
    return request({
        url: '/admin/order/back',
        method: 'post',
        data
    })
}
export function orderbackBatch(data) {
    return request({
        url: 'admin/order/backBatch',
        method: 'post',
        data
    })
}
export function orderBackXs(data) {
    return request({
        url: '/admin/order-xs/back',
        method: 'post',
        data
    })
}
export function orderbackBatchXs(data) {
    return request({
        url: 'admin/order-xs/backBatch',
        method: 'post',
        data
    })
}
// 获取qa详情
export function getQaDetail(city_id) {
    return request({
        url: 'admin/qa/getQaDetail',
        method: 'get',
        params: {
            city_id
        }
    })
}
