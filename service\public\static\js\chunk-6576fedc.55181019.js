(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6576fedc"],{"2b83":function(t,e,i){},"83ef":function(t,e,i){"use strict";i("2b83")},e132:function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"problem"},[i("el-row",[i("el-col",{attrs:{span:24}},[i("div",{staticClass:"problem_form"},[i("el-form",{ref:"form",attrs:{inline:!0,model:t.dataForm,"label-width":"60px"}},[i("el-form-item",{attrs:{label:"关键字:"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"400px"},attrs:{placeholder:"请输入搜索关键字"},model:{value:t.dataForm.keyword,callback:function(e){t.$set(t.dataForm,"keyword",e)},expression:"dataForm.keyword"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"success"},on:{click:t.onSubmit}},[t._v("查询")])],1)],1)],1)])],1),i("div",{staticClass:"problem_container"},[i("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.load,expression:"load"}],staticClass:"problem_left",staticStyle:{overflow:"auto",padding:"10px"},attrs:{"infinite-scroll-immediate":!1}},t._l(t.getQaCityList,(function(e){return i("div",{staticClass:"btn",on:{click:function(i){return t.handleQacityl(e.city_id)}}},[t._v(" "+t._s(e.city_name)+" ")])})),0),i("div",{staticClass:"problem_right"},[t.isWideScreen?i("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.load,expression:"load"}],staticClass:"qa-wide-layout",attrs:{"infinite-scroll-immediate":!1}},[i("div",{staticClass:"qa-column"},[t._m(0),i("div",{staticClass:"qa-content"},t._l(t.liveQaList,(function(e){return i("div",{key:e.id,staticClass:"problem_right_container"},[i("div",{staticClass:"title"},[i("span",{domProps:{innerHTML:t._s(t.handleprant(e.title))}}),i("el-button",{attrs:{type:"primary"},on:{click:function(i){return t.showImgDialog(e.img_zip)}}},[t._v("下载图片")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(i){return t.showFileList(e.trip_zip)}}},[t._v("下载行程")])],1),t._l(e.qaQuestions,(function(e){return i("div",{key:e.id,staticClass:"desc_container"},[i("span",{staticClass:"desc",staticStyle:{"font-weight":"700",color:"#46a6ff"},domProps:{innerHTML:t._s(t.handleprant(e.title))}}),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(i){return t.copyToClipboard(e.content)}}},[t._v("复制")]),i("div",{staticClass:"desc",domProps:{innerHTML:t._s(t.handleprantHtml(e.content))}})],1)}))],2)})),0)]),i("div",{staticClass:"qa-column"},[t._m(1),i("div",{staticClass:"qa-content"},t._l(t.privateQaList,(function(e){return i("div",{key:e.id,staticClass:"problem_right_container"},[i("div",{staticClass:"title"},[i("span",{domProps:{innerHTML:t._s(t.handleprant(e.title))}}),i("el-button",{attrs:{type:"primary"},on:{click:function(i){return t.showImgDialog(e.img_zip)}}},[t._v("下载图片")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(i){return t.showFileList(e.trip_zip)}}},[t._v("下载行程")])],1),t._l(e.qaQuestions,(function(e){return i("div",{key:e.id,staticClass:"desc_container"},[i("span",{staticClass:"desc",staticStyle:{"font-weight":"700",color:"#46a6ff"},domProps:{innerHTML:t._s(t.handleprant(e.title))}}),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(i){return t.copyToClipboard(e.content)}}},[t._v("复制")]),i("div",{staticClass:"desc",domProps:{innerHTML:t._s(t.handleprantHtml(e.content))}})],1)}))],2)})),0)])]):i("el-tabs",{attrs:{type:"card"},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[i("el-tab-pane",{attrs:{label:"直播QA",name:"live"}},[i("ul",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.load,expression:"load"}],staticClass:"infinite-list",staticStyle:{overflow:"auto"},attrs:{"infinite-scroll-immediate":!1}},t._l(t.liveQaList,(function(e){return i("li",{key:e.id,staticClass:"problem_right_container"},[i("div",{staticClass:"title"},[i("span",{domProps:{innerHTML:t._s(t.handleprant(e.title))}}),i("el-button",{attrs:{type:"primary"},on:{click:function(i){return t.showImgDialog(e.img_zip)}}},[t._v("下载图片")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(i){return t.showFileList(e.trip_zip)}}},[t._v("下载行程")])],1),t._l(e.qaQuestions,(function(e){return i("div",{key:e.id,staticClass:"desc_container"},[i("span",{staticClass:"desc",staticStyle:{"font-weight":"700",color:"#46a6ff"},domProps:{innerHTML:t._s(t.handleprant(e.title))}}),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(i){return t.copyToClipboard(e.content)}}},[t._v("复制")]),i("div",{staticClass:"desc",domProps:{innerHTML:t._s(t.handleprantHtml(e.content))}})],1)}))],2)})),0)]),i("el-tab-pane",{attrs:{label:"私域QA",name:"private"}},[i("ul",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.load,expression:"load"}],staticClass:"infinite-list",staticStyle:{overflow:"auto"},attrs:{"infinite-scroll-immediate":!1}},t._l(t.privateQaList,(function(e){return i("li",{key:e.id,staticClass:"problem_right_container"},[i("div",{staticClass:"title"},[i("span",{domProps:{innerHTML:t._s(t.handleprant(e.title))}}),i("el-button",{attrs:{type:"primary"},on:{click:function(i){return t.showImgDialog(e.img_zip)}}},[t._v("下载图片")]),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:function(i){return t.showFileList(e.trip_zip)}}},[t._v("下载行程")])],1),t._l(e.qaQuestions,(function(e){return i("div",{key:e.id,staticClass:"desc_container"},[i("span",{staticClass:"desc",staticStyle:{"font-weight":"700",color:"#46a6ff"},domProps:{innerHTML:t._s(t.handleprant(e.title))}}),i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(i){return t.copyToClipboard(e.content)}}},[t._v("复制")]),i("div",{staticClass:"desc",domProps:{innerHTML:t._s(t.handleprantHtml(e.content))}})],1)}))],2)})),0)])],1)],1)]),i("el-dialog",{attrs:{title:"图片列表",visible:t.dialogImage,width:"80%"},on:{"update:visible":function(e){t.dialogImage=e}}},[i("div",{staticClass:"image-list"},t._l(t.imageList,(function(e,n){return i("el-card",{key:n,staticClass:"image-card",attrs:{"body-style":{padding:"10px"}}},[i("div",{staticStyle:{"text-align":"center"}},[t._v(t._s(e.desc))]),i("img",{staticClass:"image-preview",attrs:{src:e.file}}),i("div",{staticClass:"image-footer"},[i("el-button",{attrs:{size:"mini"},on:{click:function(i){return t.handlePreview(e.file)}}},[t._v("查看")]),i("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(i){return t.handleDownload(e.file)}}},[t._v("下载")])],1)])})),1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.dialogImage=!1}}},[t._v("关闭")])],1)]),i("el-dialog",{attrs:{title:"文件列表",visible:t.dialogFile,width:"80%"},on:{"update:visible":function(e){t.dialogFile=e}}},[i("div",{staticClass:"file-list-horizontal"},t._l(t.fileList,(function(e,n){return i("el-card",{key:n,staticClass:"file-card",attrs:{"body-style":{padding:"10px"}}},[i("div",{staticClass:"file-info"},[i("el-icon",{staticClass:"file-icon"},[i("i",{class:t.getFileIcon(e.file)})]),i("span",{staticClass:"file-name",staticStyle:{display:"block"}},[t._v(t._s(t.getFileName(e.file)))])],1),i("div",{staticClass:"file-info"},[i("span",{staticClass:"file-name"},[t._v(t._s(e.desc))])]),i("div",{staticClass:"file-footer"},[i("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(i){return t.handleDownload(e.file)}}},[t._v("下载")])],1)])})),1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.dialogFile=!1}}},[t._v("关闭")])],1)]),i("el-dialog",{attrs:{visible:t.previewVisible,title:t.previewTitle,width:"60%"},on:{"update:visible":function(e){t.previewVisible=e}}},[i("img",{staticClass:"image-preview-full",attrs:{src:t.previewImageUrl}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.previewVisible=!1}}},[t._v("关闭")])],1)])],1)},a=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"qa-column-header"},[i("h3",{staticClass:"qa-title"},[t._v("直播QA")])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"qa-column-header"},[i("h3",{staticClass:"qa-title"},[t._v("私域QA")])])}],s=(i("3dd5"),i("485c"),i("90c8"),i("fe02"),i("8d8a"),i("1b55"),i("fa87"),i("3363"),i("9d08"),i("3399"),i("10de"),i("d5ad"),i("042e"),i("85a8")),l={data:function(){return{getQaCityList:[],getQaLists:[],liveQaList:[],privateQaList:[],activeTab:"live",isWideScreen:!1,screenWidth:document.documentElement.clientWidth,dataForm:{keyword:"",city_id:""},dialogImage:!1,previewVisible:!1,previewImageUrl:"",previewTitle:"",imageList:[],fileList:[],dialogFile:!1}},created:function(){var t=this;Object(s["a"])().then((function(e){t.getQaCityList=e.data})),this.checkScreenWidth(),window.addEventListener("resize",this.checkScreenWidth)},beforeDestroy:function(){window.removeEventListener("resize",this.checkScreenWidth)},watch:{"dataForm.keyword":function(t){t&&this.onSubmit()}},methods:{checkScreenWidth:function(){this.screenWidth=document.documentElement.clientWidth,this.isWideScreen=this.screenWidth>1200},showFileList:function(t){t.length&&(this.fileList=t),this.dialogFile=!0},getFileIcon:function(t){var e=t.split(".").pop().toLowerCase();switch(console.log("ext:"+e),e){case"pdf":return"el-icon-file-pdf";case"docx":return"el-icon-file-word";case"pptx":return"el-icon-file-ppt";case"xlsx":return"el-icon-file-excel";default:return"el-icon-file"}},getFileName:function(t){return t.substring(t.lastIndexOf("/")+1)},handleQacityl:function(t){var e=this;Object(s["b"])({city_id:t}).then((function(t){e.getQaLists=t.data.data,e.filterQaLists()}))},handleZip:function(t){var e=this;t?fetch(t).then((function(t){return t.blob()})).then((function(e){var i=document.createElement("a"),n=URL.createObjectURL(e);i.href=n,i.download=t.split("/").pop(),document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n)})).catch((function(t){e.$message({showClose:!0,message:"下载失败",type:"error"}),console.error("Download error:",t)})):this.$message({showClose:!0,message:"暂无下载链接",type:"warning"})},load:function(){console.log("load")},handleprant:function(t){if(!t)return"";var e=new RegExp(this.dataForm.keyword,"ig"),i='<span style="color: #fff;background-color: #FC0421FF;">'.concat(this.dataForm.keyword,"</span>");return t.replace(e,i)},handleprantHtml:function(t){if(!t)return"";var e=this.dataForm.keyword,i=new RegExp(e,"g"),n=t.replace(/(?<=>)[^>]+(?=<[/]?\w+.*>)/g,(function(t){return t.replace(i,"<span style='color: #fff;background-color: #FC0421FF;'>".concat(e,"</span>"))}));return n===t?this.handleprant(t):n},onSubmit:function(){var t=this;Object(s["b"])(this.dataForm).then((function(e){t.getQaLists=e.data.data,t.filterQaLists()}))},copyToClipboard:function(t){var e=this.stripHtml(t),i=document.createElement("textarea");i.value=e,document.body.appendChild(i),i.select(),document.execCommand("copy"),document.body.removeChild(i),this.$message({showClose:!0,message:"内容已复制",type:"success"})},stripHtml:function(t){var e=document.createElement("div");return e.innerHTML=t,e.textContent||e.innerText||""},showImgDialog:function(t){t.length&&(this.imageList=t),this.dialogImage=!0},handlePreview:function(t){this.previewImageUrl=t,this.previewTitle="图片预览",this.previewVisible=!0},handleRemove:function(t){this.imageList.splice(t,1)},handleDownload:function(t){var e=this;t?fetch(t).then((function(t){return t.blob()})).then((function(e){var i=document.createElement("a"),n=URL.createObjectURL(e);i.href=n,i.download=t.split("/").pop(),document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n)})).catch((function(t){e.$message({showClose:!0,message:"下载失败",type:"error"}),console.error("Download error:",t)})):this.$message({showClose:!0,message:"暂无下载链接",type:"warning"})},filterQaLists:function(){if(!this.getQaLists||0===this.getQaLists.length)return this.liveQaList=[],void(this.privateQaList=[]);this.liveQaList=this.getQaLists.filter((function(t){return 0===t.is_private||!t.is_private})),this.privateQaList=this.getQaLists.filter((function(t){return 1===t.is_private}))}}},o=l,r=(i("83ef"),i("8a34")),c=Object(r["a"])(o,n,a,!1,null,"4806da42",null);e["default"]=c.exports}}]);