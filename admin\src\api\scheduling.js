import request from '@/utils/request'

//直播间列表
export function liveroom() {
    return request({
        url: '/admin/liveroom/index',
        method: 'get'
    })
}
// 排班列表
export function roomWorks(live_room_id) {
    return request({
        url: '/admin/liveroom/roomWorks',
        method: 'get',
        params: { live_room_id }
    })
}
// 可排班主播列表
export function availableZhubo(params) {
    return request({
        url: '/admin/liveroom/availableZhubo',
        method: 'get',
        params: params
    })
}
// 编辑直播间
export function saveProducts(data) {
    return request({
        url: '/admin/liveroom/saveProducts',
        method: 'post',
        data
    })
}
// 保存排班信息
export function saveRoomWorks(data) {
    return request({
        url: '/admin/liveroom/saveRoomWorks',
        method: 'post',
        data
    })
}
