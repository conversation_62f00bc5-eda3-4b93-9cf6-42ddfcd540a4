<?php

namespace app\admin\controller;

use app\model\QimoAgents;
use app\model\QimoCallLogs;
use app\server\QimoService;
use support\Request;

class QimoController extends base
{
    /**
     * 获取坐席列表
     */
    public function agents(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 20);
        $status = $request->input('status', '');

        $query = QimoAgents::query();

        if ($status !== '') {
            $query->where('status', $status);
        }

        $result = $query->paginate($pageSize, ['*'], 'page', $page);

        return $this->success([
            'list' => $result->items(),
            'total' => $result->total(),
            'page' => $page,
            'pageSize' => $pageSize,
        ]);
    }

    /**
     * 创建坐席
     */
    public function createAgent(Request $request)
    {
        $data = $request->post();

        $validator = validator($data, [
            'name' => 'required|string|max:50',
            'account_id' => 'required|string|max:50',
            'username' => 'required|string|max:50',
            'password' => 'required|string|max:100',
            'pbx_url' => 'required|url',
            'login_type' => 'string|in:Local,Remote',
            'description' => 'string|max:200',
        ]);

        if ($validator->fails()) {
            return $this->error(400, $validator->errors()->first());
        }

        $agent = QimoAgents::create([
            'name' => $data['name'],
            'account_id' => $data['account_id'],
            'username' => $data['username'],
            'password' => $data['password'],
            'pbx_url' => $data['pbx_url'],
            'login_type' => $data['login_type'] ?? 'Local',
            'description' => $data['description'] ?? '',
            'status' => QimoAgents::STATUS_ENABLED,
            'create_time' => time(),
        ]);

        return $this->success($agent);
    }

    /**
     * 更新坐席
     */
    public function updateAgent(Request $request, $id)
    {
        $agent = QimoAgents::find($id);
        if (!$agent) {
            return $this->error(404, '坐席不存在');
        }

        $data = $request->post();

        $validator = validator($data, [
            'name' => 'string|max:50',
            'account_id' => 'string|max:50',
            'username' => 'string|max:50',
            'password' => 'string|max:100',
            'pbx_url' => 'url',
            'login_type' => 'string|in:Local,Remote',
            'description' => 'string|max:200',
            'status' => 'integer|in:0,1,2,3',
        ]);

        if ($validator->fails()) {
            return $this->error(400, $validator->errors()->first());
        }

        $agent->update(array_filter($data));

        return $this->success($agent);
    }

    /**
     * 删除坐席
     */
    public function deleteAgent(Request $request, $id)
    {
        $agent = QimoAgents::find($id);
        if (!$agent) {
            return $this->error(404, '坐席不存在');
        }

        $agent->delete();

        return $this->success('删除成功');
    }

    /**
     * 获取空闲坐席配置
     */
    public function getAvailableAgent(Request $request)
    {
        $qimoService = new QimoService();
        $result = $qimoService->getAvailableAgent();

        if ($result['code'] !== 0) {
            return $this->error(500, $result['message']);
        }

        return $this->success($result['data']);
    }

    /**
     * 发起外呼
     */
    public function makeCall(Request $request)
    {
        $data = $request->post();

//        $validator = validator($data, [
//            'callee_number' => 'required|string|min:11|max:11',
//            'agent_id' => 'integer|exists:qimo_agents,id',
//        ]);
//
//        if ($validator->fails()) {
//            return $this->error($validator->errors()->first());
//        }

        $qimoService = new QimoService();
        $result = $qimoService->makeCall(
            $data['callee_number'],
            $request->admin->id,
            $data['agent_id'] ?? null
        );

        if ($result['code'] !== 0) {
            return $this->error(500, $result['message']);
        }

        return $this->success($result['data']);
    }

    /**
     * 获取通话记录
     */
    public function callLogs(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 20);
        $adminId = $request->input('admin_id', '');
        $callStatus = $request->input('call_status', '');
        $startTime = $request->input('start_time', '');
        $endTime = $request->input('end_time', '');

        $query = QimoCallLogs::with(['admin', 'agent']);

        if ($adminId) {
            $query->where('admin_id', $adminId);
        }

        if ($callStatus !== '') {
            $query->where('call_status', $callStatus);
        }

        if ($startTime) {
            $query->where('start_time', '>=', strtotime($startTime));
        }

        if ($endTime) {
            $query->where('start_time', '<=', strtotime($endTime));
        }

        $result = $query->orderBy('id', 'desc')
            ->paginate($pageSize, ['*'], 'page', $page);

        return $this->success([
            'list' => $result->items(),
            'total' => $result->total(),
            'page' => $page,
            'pageSize' => $pageSize,
        ]);
    }

    /**
     * 处理七陌事件推送
     */
    public function webhook(Request $request)
    {
        $eventData = $request->post();

        $qimoService = new QimoService();
        $result = $qimoService->handleEvent($eventData);

        return response()->json($result);
    }

    /**
     * 获取SDK配置
     */
    public function getSdkConfig(Request $request)
    {
        return $this->success([
            'sdk_url' => config('cloudcall.qimo.sdk_url'),
            'app_id' => config('cloudcall.qimo.app_id'),
        ]);
    }
}
