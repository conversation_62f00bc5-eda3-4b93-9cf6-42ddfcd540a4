<template>
  <div class="createPost-container">
    <el-form ref="postForm" :model="postForm" :rules="rules" class="form-container">

      <sticky :z-index="10" :class-name="'sub-navbar '">
        <el-button v-loading="loading" style="margin-left: 10px;" type="success" @click="submitForm">
          提交
        </el-button>
      </sticky>

      <div class="createPost-main-container">
        <el-form-item label-width="70px" label="用户名：">
          <el-input v-model="postForm.username" placeholder="Please enter the content" />
        </el-form-item>

        <el-form-item label-width="70px" label="姓名：">
          <el-input v-model="postForm.name" placeholder="Please enter the content" />
        </el-form-item>

        <el-form-item label-width="70px" label="密码:">
          <el-input v-model="postForm.password" placeholder="Please enter the content" />
        </el-form-item>

        <el-form-item prop="image_uri" label="头像:">
          <Upload v-model="postForm.avatar" />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import Upload from '@/components/Upload/SingleImage3'
import Sticky from '@/components/Sticky' // 粘性header组件

export default {
  name: 'AdminEdit',
  components: { Upload, Sticky},
  data() {
    return {
      postForm: {},
      loading: false,
      tempRoute: {},
      rules: {}
    }
  },
  computed: {
    displayTime: {
      get() {
        return (+new Date(this.postForm.display_time))
      },
      set(val) {
        this.postForm.display_time = new Date(val)
      }
    }
  },
  created() {
    let id = this.$route.query.id
    this.fetchData(id)
    this.tempRoute = Object.assign({}, this.$route)
  },
  methods: {
    fetchData(id) {
      this.$axios.get('/admin/user/edit', { params: {id: id}}).then(response => {
        this.postForm = response.data
        this.postForm.avatar = '';
        this.loading = false
      }).catch(err => {
        console.log(err)
      })
    },
    submitForm() {
      console.log(this.postForm)
      this.$refs.postForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$axios.post('/admin/user/save',this.postForm).then(res=>{
            this.$notify({
              title: '成功',
              message: '修改成功',
              type: 'success',
              duration: 2000
            })
            this.postForm.status = 'published'
            this.loading = false
          }).catch(err=>{
            this.$notify({
              title: '成功',
              message: '修改失败',
              type: 'error',
              duration: 2000
            })
            this.loading = false
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.createPost-container {
  position: relative;

  .createPost-main-container {
    padding: 40px 45px 20px 50px;

    .postInfo-container {
      position: relative;
      @include clearfix;
      margin-bottom: 10px;

      .postInfo-container-item {
        float: left;
      }
    }
  }

  .word-counter {
    width: 40px;
    position: absolute;
    right: 10px;
    top: 0px;
  }
}

.article-textarea ::v-deep {
  textarea {
    padding-right: 40px;
    resize: none;
    border: none;
    border-radius: 0px;
    border-bottom: 1px solid #bfcbd9;
  }
}
</style>
