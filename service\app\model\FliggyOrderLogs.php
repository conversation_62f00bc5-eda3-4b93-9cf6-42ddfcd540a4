<?php
declare(strict_types=1);

namespace app\model;

/**
 * 飞猪订单日志模型
 */
final class FliggyOrderLogs extends base
{
    protected $table = 'fliggy_order_logs';
    
    // 操作类型常量
    const ACTION_CREATE = 'create';
    const ACTION_QUERY = 'query';
    const ACTION_REFUND = 'refund';
    const ACTION_UPDATE = 'update';
    
    // 状态常量
    const STATUS_SUCCESS = 1;
    const STATUS_FAILED = 2;
    
    protected $json = [
        'request_data',
        'response_data'
    ];
    
    /**
     * 记录操作日志
     */
    public static function log(
        string $fliggyOrderId,
        string $actionType,
        array $requestData,
        array $responseData = null,
        int $status = self::STATUS_SUCCESS,
        string $errorMessage = '',
        string $orderId = '',
        string $ipAddress = '',
        string $userAgent = ''
    ): self {
        $log = new self();
        $log->fliggy_order_id = $fliggyOrderId;
        $log->order_id = $orderId;
        $log->action_type = $actionType;
        $log->request_data = $requestData;
        $log->response_data = $responseData;
        $log->status = $status;
        $log->error_message = $errorMessage;
        $log->ip_address = $ipAddress;
        $log->user_agent = $userAgent;
        $log->create_at = time();
        $log->save();
        
        return $log;
    }
    
    /**
     * 获取订单的操作历史
     */
    public static function getOrderHistory(string $fliggyOrderId): array
    {
        return self::where('fliggy_order_id', $fliggyOrderId)
            ->order('create_at', 'desc')
            ->select()
            ->toArray();
    }
    
    /**
     * 获取指定时间范围内的日志
     */
    public static function getLogsByDateRange(int $startTime, int $endTime, string $actionType = ''): array
    {
        $query = self::where('create_at', '>=', $startTime)
            ->where('create_at', '<=', $endTime);
            
        if (!empty($actionType)) {
            $query->where('action_type', $actionType);
        }
        
        return $query->order('create_at', 'desc')
            ->select()
            ->toArray();
    }
    
    /**
     * 获取失败的操作日志
     */
    public static function getFailedLogs(int $limit = 100): array
    {
        return self::where('status', self::STATUS_FAILED)
            ->order('create_at', 'desc')
            ->limit($limit)
            ->select()
            ->toArray();
    }
} 