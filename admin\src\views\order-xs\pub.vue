<template>
  <div class="app-container">

    <div class="filter-container">
      <el-input v-model="listQuery.sn" placeholder="订单号" style="width: 300px;" class="filter-item" />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getList">
        搜索
      </el-button>
    </div>

    <el-table 
      v-loading="listLoading" 
      :data="list" 
      border 
      fit 
      highlight-current-row 
      style="width: 100%"
      :height="tableMaxHeight"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      
      <el-table-column align="center" fixed width="120" label="操作">
        <template slot-scope="scope">
          <el-button type="primary" size="small" icon="el-icon-magic-stick" @click="onFish(scope.row)">
            领取
          </el-button>
        </template>
      </el-table-column>
      
      <el-table-column align="center" fixed label="电话" width="140">
        <template slot-scope="scope">
          <el-button type="text" @click="copyToClipboard(scope.row.mobile)">{{ scope.row.mobile }}</el-button>
          <span style="display: block; font-size: 12px" v-if="scope.row.mobileInfo">
            {{ scope.row.mobileInfo.area }}-{{ scope.row.mobileInfo.originalIsp }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        fixed
        label="来源"
        width="80"
        prop="source_name"
      />

      <el-table-column
        align="center"
        fixed
        label="客服"
        width="80"
        prop="admin.username"
      />

      <el-table-column
        align="center"
        width="150px"
        label="标题"
        prop="product_name"
      >
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.product_name" placement="top">
            <span 
              @click="copyToClipboard(scope.row.product_name)"
              class="ellipsis-text"
            >
              {{ scope.row.product_name }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      
      <el-table-column
        align="center"
        width="100px"
        label="等级"
        prop="level_name"
      />
      
      <el-table-column
        width="200px"
        align="center"
        label="跟进备注"
        prop="remark"
      />

      <!-- <el-table-column align="center" label="跟进状态" width="90">
        <template slot-scope="scope">
          <div
            style="padding: 1px 5px; border-radius: 3px"
            :style="{
              color: follow_status[scope.row.status],
              border: `1px solid ${follow_status[scope.row.status]}`,
            }"
            type="primary"
          >
            {{ scope.row.status_name }}
          </div>
        </template>
      </el-table-column> -->
      
      <el-table-column align="center" label="粉丝状态" width="90">
        <template slot-scope="scope">
          <div
            style="padding: 1px 5px; border-radius: 3px"
            :style="{
              color: follow_status[scope.row.fans_status],
              border: `1px solid ${follow_status[scope.row.fans_status]}`,
            }"
            type="primary"
          >
            {{ scope.row.fans_status ? '有效' : '无效' }}
          </div>
        </template>
      </el-table-column>
      
      <el-table-column width="138px" align="center" label="入粉时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.create_at | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
      
      <el-table-column width="160px" align="center" label="派单时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.give_time | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
      
      <el-table-column width="160px" align="center" label="计划出行日期">
        <template slot-scope="scope">
          <span>{{
            scope.row.travel_date
          }}</span>
        </template>
      </el-table-column>

      <el-table-column width="138px" align="center" label="计划出行时间">
        <template slot-scope="scope">
          <span>{{ scope.row.travel_date | parseTime("{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>

      <el-table-column width="138px" align="center" label="最后跟进时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.last_follow | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="总金额" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.total_price / 100 }}</span>
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        width="80px"
        label="人数"
        prop="quantity"
      />

      <el-table-column width="138px" align="center" label="修改时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.update_time | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

  </div>
</template>

<script>
import Pagination from '@/components/PaginationFixed'

export default {
  name: 'Publist',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10
      },
      order_status: [
        "#9e9f9c",
        "#04bcd9",
        "#fc9904",
        "#1193f4",
        "#48b14b",
        "#eb1662",
        "#9d1cb5",
      ],
      follow_status: [
        "#9e9f9c",
        "#04bcd9",
        "#fc9904",
        "#1193f4",
        "#48b14b",
        "#eb1662",
      ]
    }
  },
  computed: {
    tableMaxHeight() {
      return window.innerHeight - 320 + "px";
    },
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$axios.get('/admin/order-xs/pub', { params: this.listQuery }).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.listLoading = false
      })
    },
    onFish(item) {
      this.$axios.post('/admin/order-xs/fish', { id: item.id }).then(res => {
        this.dialogVisible = false
        this.item = {}
        this.getList()
      }).catch(err => {

      })
    },
    stripHtml(html) {
      const div = document.createElement("div");
      div.innerHTML = html;
      return div.textContent || div.innerText || "";
    },
    copyToClipboard(html) {
      const text = this.stripHtml(html);
      const input = document.createElement("textarea");
      input.value = text;
      document.body.appendChild(input);
      input.select();
      document.execCommand("copy");
      document.body.removeChild(input);
      this.$message({
        showClose: true,
        message: "内容已复制",
        type: "success",
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 5px; /* 分页条的高度，以避免内容重叠 */
}

.ellipsis-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  cursor: pointer;
}

/* 紧凑型表格样式 */
::v-deep .el-table {
  font-size: 15px;
  color: #333;
}

::v-deep .el-table thead {
  font-weight: 500;
  color: #2c3e50;
}

::v-deep .el-table td {
  padding: 4px 0;
}

::v-deep .el-table .cell {
  line-height: 1.4;
  padding-left: 6px;
  padding-right: 6px;
}

::v-deep .el-button--mini, ::v-deep .el-button--small {
  padding: 6px 9px;
  font-size: 12.5px;
}

::v-deep .el-tag {
  padding: 0 6px;
  height: 22px;
  line-height: 22px;
}

::v-deep .el-button-group .el-button--small {
  padding: 6px 9px;
}

/* 改善阅读体验 */
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #f8f9fa;
}

::v-deep .el-table__row:hover > td {
  background-color: #ecf5ff !important;
}
</style>
