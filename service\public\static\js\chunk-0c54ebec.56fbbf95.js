(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0c54ebec"],{9911:function(t,a,s){"use strict";s.r(a);var e=function(){var t=this,a=t.$createElement,s=t._self._c||a;return s("div",{staticClass:"app-container"},[s("div",{staticClass:"filter-container"},[s("el-date-picker",{staticClass:"filter-item",attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":["00:00:00","23:59:59"]},model:{value:t.listQuery.times,callback:function(a){t.$set(t.listQuery,"times",a)},expression:"listQuery.times"}}),s("el-select",{staticClass:"filter-item",staticStyle:{width:"140px"},attrs:{placeholder:"来源",clearable:""},model:{value:t.listQuery.os,callback:function(a){t.$set(t.listQuery,"os",a)},expression:"listQuery.os"}},t._l(t.osOptions,(function(t){return s("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),s("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v(" 查询 ")])],1),s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:24}},[s("el-card",{staticClass:"total-stats",attrs:{shadow:"hover"}},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("总体统计")])]),s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:6}},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-item-title"},[t._v("客户总数")]),s("div",{staticClass:"stat-item-value"},[t._v(t._s(t.totalStats.orders_num||0))])])]),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-item-title"},[t._v("已加微信数")]),s("div",{staticClass:"stat-item-value"},[t._v(t._s(t.totalStats.is_wechat_num||0))])])]),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-item-title"},[t._v("有效粉丝数")]),s("div",{staticClass:"stat-item-value"},[t._v(t._s(t.totalStats.fans_status_num||0))])])]),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-item-title"},[t._v("出行数")]),s("div",{staticClass:"stat-item-value"},[t._v(t._s(t.totalStats.chuxing_num||0))])])])],1),s("el-row",{staticStyle:{"margin-top":"20px"},attrs:{gutter:20}},[s("el-col",{attrs:{span:6}},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-item-title"},[t._v("微信转化率")]),s("div",{staticClass:"stat-item-value"},[t._v(t._s(t.totalStats.is_wechat_radio||0)+"%")])])]),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"stat-item"},[s("div",{staticClass:"stat-item-title"},[t._v("有效粉丝率")]),s("div",{staticClass:"stat-item-value"},[t._v(t._s(t.totalStats.fans_status_radio||0)+"%")])])])],1)],1)],1)],1),s("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[s("el-table-column",{attrs:{align:"center",label:"客服",prop:"admin.username"}}),s("el-table-column",{attrs:{align:"center",label:"客户数量",prop:"orders_num"}}),s("el-table-column",{attrs:{align:"center",label:"已加微信数量",prop:"is_wechat_num"}}),s("el-table-column",{attrs:{align:"center",label:"微信转化率",prop:"is_wechat_radio"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(" "+t._s(a.row.is_wechat_radio)+"% ")]}}])}),s("el-table-column",{attrs:{align:"center",label:"有效粉丝数",prop:"fans_status_num"}}),s("el-table-column",{attrs:{align:"center",label:"有效粉丝率",prop:"fans_status_radio"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(" "+t._s(a.row.fans_status_radio)+"% ")]}}])}),s("el-table-column",{attrs:{align:"center",label:"出行数",prop:"chuxing_num"}})],1)],1)},i=[],l=(s("e168"),s("4cc3"),s("37fe"),s("5227"),s("90c8"),s("04b0"),s("f2e9"),{name:"PrivateStats",data:function(){return{list:[],listLoading:!0,osOptions:[],totalStats:{orders_num:0,is_wechat_num:0,fans_status_num:0,chuxing_num:0,is_wechat_radio:0,fans_status_radio:0},listQuery:{times:[this.getStartDateOfMonth(),this.getEndDateOfMonth()],os:""}}},created:function(){this.getOsOptions(),this.getList()},methods:{getList:function(){var t=this;this.listLoading=!0;var a={};this.listQuery.times&&2===this.listQuery.times.length&&(a.times=this.listQuery.times),this.listQuery.os&&(a.os=this.listQuery.os),this.$axios.get("/admin/data/indexXs",{params:a}).then((function(a){t.list=a.data,t.calculateTotalStats(),t.listLoading=!1})).catch((function(){t.listLoading=!1}))},getOsOptions:function(){var t=this;this.$axios.get("/admin/line/parameter").then((function(a){a&&a.data&&a.data.source&&(t.osOptions=Object.keys(a.data.source).map((function(t){return{label:a.data.source[t],value:t}})))})).catch((function(t){console.error("获取来源选项失败",t)}))},calculateTotalStats:function(){if(this.totalStats={orders_num:0,is_wechat_num:0,fans_status_num:0,chuxing_num:0,is_wechat_radio:0,fans_status_radio:0},this.list&&0!==this.list.length){var t=0,a=0,s=0,e=0;this.list.forEach((function(i){t+=parseInt(i.orders_num||0),a+=parseInt(i.is_wechat_num||0),s+=parseInt(i.fans_status_num||0),e+=parseInt(i.chuxing_num||0)})),this.totalStats.orders_num=t,this.totalStats.is_wechat_num=a,this.totalStats.fans_status_num=s,this.totalStats.chuxing_num=e,t>0&&(this.totalStats.is_wechat_radio=(a/t*100).toFixed(2),this.totalStats.fans_status_radio=(s/t*100).toFixed(2))}},getStartDateOfMonth:function(){var t=new Date;return"".concat(t.getFullYear(),"-").concat(String(t.getMonth()+1).padStart(2,"0"),"-01")+" 00:00:00"},getEndDateOfMonth:function(){var t=new Date,a=t.getFullYear(),s=t.getMonth()+1,e=new Date(a,s,0).getDate();return"".concat(a,"-").concat(String(s).padStart(2,"0"),"-").concat(e)+" 23:59:59"}}}),n=l,r=(s("e136"),s("8a34")),o=Object(r["a"])(n,e,i,!1,null,"56ade9a1",null);a["default"]=o.exports},e136:function(t,a,s){"use strict";s("e544")},e544:function(t,a,s){}}]);