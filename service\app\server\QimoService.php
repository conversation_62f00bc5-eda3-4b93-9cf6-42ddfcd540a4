<?php
namespace app\server;

use app\model\QimoAgents;
use app\model\QimoCallLogs;
use support\Log;

/**
 * 七陌云呼服务类
 */
class QimoService
{
    private $accountSid;
    private $authToken;
    private $apiUrl;
    private $appId;

    public function __construct()
    {
        $this->accountSid = config('cloudcall.qimo.account_sid');
        $this->authToken = config('cloudcall.qimo.auth_token');
        $this->apiUrl = config('cloudcall.qimo.api_url');
        $this->appId = config('cloudcall.qimo.app_id');
    }

    /**
     * 获取空闲坐席配置
     * @return array
     */
    public function getAvailableAgent()
    {
        $agent = QimoAgents::getAvailableAgent();

        if (!$agent) {
            return [
                'code' => 1001,
                'message' => '暂无空闲坐席',
                'data' => null
            ];
        }

        return [
            'code' => 0,
            'message' => '获取成功',
            'data' => [
                'agent_id' => $agent->id,
                'account_id' => $agent->account_id,
                'username' => $agent->username,
                'password' => $agent->password,
                'pbx_url' => $agent->pbx_url,
                'login_type' => $agent->login_type ?? 'Local',
            ]
        ];
    }

    /**
     * 发起外呼
     * @param string $calleeNumber 被叫号码
     * @param int $adminId 管理员ID
     * @param int $agentId 坐席ID
     * @return array
     */
    public function makeCall($calleeNumber, $adminId, $agentId = null)
    {
        // 如果没有指定坐席，自动分配
        if (!$agentId) {
            $agentResult = $this->getAvailableAgent();
            if ($agentResult['code'] !== 0) {
                return $agentResult;
            }
            $agentId = $agentResult['data']['agent_id'];
        }

        // 更新坐席状态为通话中
        // QimoAgents::updateStatus($agentId, QimoAgents::STATUS_CALLING);

        // 创建通话记录
        $callId = $this->generateCallId();
        QimoCallLogs::createCallLog([
            'admin_id' => $adminId,
            'agent_id' => $agentId,
            'callee_number' => $calleeNumber,
            'call_id' => $callId,
            'direction' => QimoCallLogs::DIRECTION_OUTBOUND,
        ]);

        return [
            'code' => 0,
            'message' => '外呼发起成功',
            'data' => [
                'call_id' => $callId,
                'agent_id' => $agentId,
            ]
        ];
    }

    /**
     * 处理七陌事件推送
     * @param array $eventData
     * @return array
     */
    public function handleEvent($eventData)
    {
        Log::info('七陌事件推送', $eventData);

        $eventType = $eventData['event_type'] ?? '';
        $callId = $eventData['call_id'] ?? '';

        try {
            switch ($eventType) {
                case 'call_start':
                    return $this->handleCallStart($eventData);
                case 'call_answer':
                    return $this->handleCallAnswer($eventData);
                case 'call_end':
                    return $this->handleCallEnd($eventData);
                case 'agent_status_change':
                    return $this->handleAgentStatusChange($eventData);
                default:
                    Log::warning('未知事件类型', $eventData);
                    return ['code' => 0, 'message' => '事件处理成功'];
            }
        } catch (\Exception $e) {
            Log::error('处理七陌事件失败', [
                'error' => $e->getMessage(),
                'event_data' => $eventData
            ]);
            return ['code' => 500, 'message' => '事件处理失败'];
        }
    }

    /**
     * 处理呼叫开始事件
     */
    private function handleCallStart($eventData)
    {
        $callId = $eventData['call_id'] ?? '';
        QimoCallLogs::updateCallStatus($callId, [
            'call_status' => QimoCallLogs::CALL_STATUS_CALLING,
            'event_data' => json_encode($eventData),
        ]);

        return ['code' => 0, 'message' => '呼叫开始事件处理成功'];
    }

    /**
     * 处理呼叫接听事件
     */
    private function handleCallAnswer($eventData)
    {
        $callId = $eventData['call_id'] ?? '';
        QimoCallLogs::updateCallStatus($callId, [
            'call_status' => QimoCallLogs::CALL_STATUS_ANSWERED,
            'answer_time' => time(),
            'event_data' => json_encode($eventData),
        ]);

        return ['code' => 0, 'message' => '呼叫接听事件处理成功'];
    }

    /**
     * 处理呼叫结束事件
     */
    private function handleCallEnd($eventData)
    {
        $callId = $eventData['call_id'] ?? '';
        $duration = $eventData['duration'] ?? 0;
        $recordUrl = $eventData['record_url'] ?? '';
        $agentId = $eventData['agent_id'] ?? 0;

        // 更新通话记录
        QimoCallLogs::updateCallStatus($callId, [
            'call_status' => QimoCallLogs::CALL_STATUS_HANGUP,
            'end_time' => time(),
            'duration' => $duration,
            'record_url' => $recordUrl,
            'event_data' => json_encode($eventData),
        ]);

        // 更新坐席状态为空闲
        if ($agentId) {
            QimoAgents::updateStatus($agentId, QimoAgents::STATUS_ENABLED);
        }

        return ['code' => 0, 'message' => '呼叫结束事件处理成功'];
    }

    /**
     * 处理坐席状态变更事件
     */
    private function handleAgentStatusChange($eventData)
    {
        $agentId = $eventData['agent_id'] ?? 0;
        $status = $eventData['status'] ?? QimoAgents::STATUS_ENABLED;

        if ($agentId) {
            QimoAgents::updateStatus($agentId, $status);
        }

        return ['code' => 0, 'message' => '坐席状态变更事件处理成功'];
    }

    /**
     * 生成呼叫ID
     * @return string
     */
    private function generateCallId()
    {
        return 'call_' . date('YmdHis') . '_' . mt_rand(1000, 9999);
    }

    /**
     * 发送HTTP请求到七陌API
     * @param string $endpoint
     * @param array $data
     * @param string $method
     * @return array
     */
    private function request($endpoint, $data = [], $method = 'POST')
    {
        $url = $this->apiUrl . $endpoint;

        $headers = [
            'Content-Type: application/json',
            'Authorization: Basic ' . base64_encode($this->accountSid . ':' . $this->authToken)
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            Log::error('七陌API请求失败', [
                'url' => $url,
                'http_code' => $httpCode,
                'response' => $response
            ]);
            return ['code' => $httpCode, 'message' => '请求失败'];
        }

        return json_decode($response, true) ?: ['code' => 500, 'message' => '响应解析失败'];
    }
}
