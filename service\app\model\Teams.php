<?php
namespace app\model;

class Teams extends base
{
    protected $json = ['admin_ids','rules'];

    public function teams($pid = 0) {
        return $this->_teams($pid);
    }

    protected function _teams($pid = 0) {
        $ls = $this->where('pid', $pid)->field('id,pid,name,admin_ids,rules')->where('status', 1)->order('sort','asc')->select();
        foreach($ls as $k=>$l) {
            $c = $this->_teams($l->id);
            $ls[$k]->label = $l->getData('name');
            if(count($c)) {
                $ls[$k]->children = $c;
            }
        }

        return $ls;
    }
}