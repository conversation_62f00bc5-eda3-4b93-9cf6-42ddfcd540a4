.pagination-container[data-v-28fdfbeb]{padding:32px 16px;position:fixed;bottom:0;left:0;width:100%;background:#fff;padding:40px 280px;-webkit-box-shadow:0 -2px 10px rgba(0,0,0,.1);box-shadow:0 -2px 10px rgba(0,0,0,.1);z-index:100}.pagination-container.hidden[data-v-28fdfbeb]{display:none}.softphone-container[data-v-02143e16]{position:fixed;top:10px;right:10px;width:300px;background:#fff;border:1px solid #dcdfe6;border-radius:8px;-webkit-box-shadow:0 2px 12px 0 rgba(0,0,0,.1);box-shadow:0 2px 12px 0 rgba(0,0,0,.1);z-index:9999;font-size:14px}.softphone-header[data-v-02143e16]{background:#2c5aa0;color:#fff;padding:10px 15px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-ms-flex-align:center;align-items:center;border-radius:8px 8px 0 0}.status-info[data-v-02143e16]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-flex:1;-ms-flex:1;flex:1}.status-text[data-v-02143e16]{font-weight:700;font-size:14px}.call-time[data-v-02143e16]{font-size:12px;opacity:.9;margin-top:2px}.header-actions[data-v-02143e16]{display:-webkit-box;display:-ms-flexbox;display:flex;gap:5px}.header-actions .el-button[data-v-02143e16]{padding:5px 8px;color:#fff;border-color:hsla(0,0%,100%,.3);background-color:hsla(0,0%,100%,.1)}.header-actions .el-button[data-v-02143e16]:hover{background-color:hsla(0,0%,100%,.2);border-color:hsla(0,0%,100%,.5)}.header-actions .el-button--danger[data-v-02143e16]{background-color:#f56c6c;border-color:#f56c6c;color:#fff}.header-actions .el-button--danger[data-v-02143e16]:hover{background-color:#f78989;border-color:#f78989}.header-actions .el-button.calling[data-v-02143e16]{background:#f56c6c;border-color:#f56c6c;-webkit-animation:pulse-data-v-02143e16 1s infinite;animation:pulse-data-v-02143e16 1s infinite}@-webkit-keyframes pulse-data-v-02143e16{0%{opacity:1}50%{opacity:.7}to{opacity:1}}@keyframes pulse-data-v-02143e16{0%{opacity:1}50%{opacity:.7}to{opacity:1}}.softphone-body[data-v-02143e16]{padding:15px}.settings-section[data-v-02143e16]{margin-bottom:15px}.setting-title[data-v-02143e16]{font-weight:700;color:#303133;margin-bottom:10px;font-size:13px}.setting-item[data-v-02143e16]{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;-webkit-box-align:center;-ms-flex-align:center;align-items:center;margin-bottom:10px}.setting-item label[data-v-02143e16]{font-size:12px;color:#606266;width:60px}.setting-item .el-select[data-v-02143e16]{width:120px}.phone-display[data-v-02143e16]{font-size:12px;color:#409eff;font-weight:700;background:#f0f9ff;padding:4px 8px;border-radius:4px;border:1px solid #d4edda}.dialer-section[data-v-02143e16]{border-top:1px solid #ebeef5;padding-top:15px;margin-top:15px}.dialer-input[data-v-02143e16]{margin-bottom:10px}.number-pad[data-v-02143e16]{display:grid;grid-template-columns:repeat(3,1fr);gap:5px;margin-bottom:10px}.number-pad .el-button[data-v-02143e16]{height:35px;font-weight:700}.call-actions[data-v-02143e16]{display:-webkit-box;display:-ms-flexbox;display:flex;gap:10px}.call-actions .el-button[data-v-02143e16]{-webkit-box-flex:1;-ms-flex:1;flex:1}.call-controls[data-v-02143e16]{display:-webkit-box;display:-ms-flexbox;display:flex;gap:10px;margin:15px 0;padding:10px;background:#f8f9fa;border-radius:4px}.call-controls .el-button[data-v-02143e16]{-webkit-box-flex:1;-ms-flex:1;flex:1}.action-buttons[data-v-02143e16]{margin-top:15px;text-align:center}[data-v-02143e16] .el-select-dropdown,[data-v-02143e16] .softphone-select-dropdown{z-index:10000!important}.setting-item .el-select[data-v-02143e16]{position:relative}[data-v-02143e16] .el-select .el-select-dropdown{position:absolute!important;z-index:10000!important}@media (max-width:768px){.softphone-container[data-v-02143e16]{width:280px;right:5px;top:5px}}