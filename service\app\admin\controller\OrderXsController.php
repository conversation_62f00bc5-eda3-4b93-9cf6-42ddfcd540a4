<?php
namespace app\admin\controller;
require_once(__DIR__.'/xlsxwriter.class.php');

use app\model\Admins;
use app\model\Backs;
use app\model\BacksXs;
use app\model\DyOrderProductAppointments;
use app\model\DyOrderProducts;
use app\model\Follows;
use app\model\Line;
use app\model\LogsXs;
use app\model\OrderAfterSales;
use app\model\Logs;
use app\model\OrdersXs;
use app\server\Douyin;
use app\server\DyApiService;
use app\server\ThirdApiService;
use app\server\OrdersXs as ServerOrdersXs;
use stdClass;
use support\Log;
use support\Redis;
use support\Request;
use think\facade\Db;

class OrderXsController extends base
{

    public function index(Request $request) {
        $sn = $request->get('sn');
        $timetype = $request->get('timetype');
        $times = $request->get('times');
        $os_status = $request->get('os_status');
        $isRefunded = $request->get('is_refunded', 0);

        $where = [];
        if(!$request->admin->is_super) {
            $where = [['admin_id','=', $request->admin->id]];
        }
        $where[] = ['is_refunded', '=', $isRefunded];
        $where[] = ['shop_id', '=', $request->admin->shop_id];

        if($sn) {
            $where[] = ['sn','=', $sn];
        }
        if (is_numeric($request->get('levels'))) {
            $where['level'] = $request->get('levels');
        }
        if (is_numeric($request->get('source'))) {
            $where['os'] = $request->get('source');
        }
        if (!empty($request->get('product_id'))) {
            $where['product_id'] = $request->get('product_id');
        }

        switch ($request->get('order_by')) {
            case 'verify_date_asc':
                $orderBy['verification_date'] = 'asc';
                break;
            case 'verify_date_desc':
                $orderBy['verification_date'] = 'desc';
                break;
            default:
                $orderBy['create_at'] = 'desc';
                break;
        }

        $status = $request->get('status', null);

        $query = OrdersXs::attimes($timetype, $times)->with(['admin','anchor','backs', 'mobileInfo'])->where($where)
            ->order($orderBy);

        if($os_status) {
            if ($os_status[0] == array_search(OrdersXs::OSS[4],OrdersXs::OSS)){
                if (isset($os_status[1]) && is_numeric($os_status[1])) {
                    $query->whereRaw(OrdersXs::AllOssStatusSql[$os_status[1]]);
                }
            }else{
                if (isset($os_status[0]) && is_numeric($os_status[0])) {
                    $query->where('os', $os_status[0]);
                }
                if (isset($os_status[1]) && is_numeric($os_status[1])) {
                    $query->where('order_status', $os_status[1]);
                }
            }
        }
        if ($unUseType = $request->get('un_use_type', 0)) {
            switch ($unUseType) {
                case 1:
                    list($start, $end) = calculateDateRange(30, 60);
                    break;
                case 2:
                    list($start, $end) = calculateDateRange(60, 80);
                    break;
                case 3:
                    list($start, $end) = calculateDateRange(80, 365);
                    break;
                default:
                    list($start, $end) = calculateDateRange(30, 365);
                    break;
            }
            $query->whereBetweenTime('give_time', $start, $end);
            $query->whereRaw(OrdersXs::AllOssStatusSql[1]);
        }

        if($status!=null && $status >= 0) {
            $query->where('status', $status);
        }
        if ($status != 4) {
            $query->where('status', '!=', 4);
        }

        $appointment_status = $request->get('appointment_status', null);
        if($appointment_status!=null && $appointment_status >= 0) {
            $query->where('appointment_status', $appointment_status);
            $query->where('is_refunded', 0);
        }

        $mobile = $request->get('mobile');
        if($mobile){
            $query->where('mobile|wechat|product_name','like', '%'.$mobile.'%');
        }

        $productName = $request->get('product_name');
        if($productName){
            $query->where('product_name','like', '%'.$productName.'%');
        }

        $zhubo = $request->get('zhubo');
        if($zhubo){
            $zhubo_id = Admins::where('username', $zhubo)->value('id');
            $query->where('zhubo', $zhubo_id);
        }
        try {
            $admin = $request->get('admin');
            if ($request->admin->is_franchisee == 1) {
                $query->whereRaw(sprintf('admin_id in (select id from admins where pid = "%s")', $request->admin->id));
            }
            if($admin){
                $admin_id = Admins::whereLike('username', '%' . $admin . '%')->value('id');
                $query->where('admin_id', $admin_id);
            }
        } catch (\Exception $exception) {

        }

        $excel = $request->get('excel');
        if($excel == 1) {
            $orders = $query->select();

            $writer = new \XLSXWriter();
            $writer->writeSheetHeader('Sheet1', ['电话'=>'string','平台'=>'string','直播'=>'string','订单号'=>'string','下单时间'=>'string','状态'=>'string','跟进状态'=>'string',
                '标题'=>'string','跟进备注'=>'string','联系人'=>'string','出行时间'=>'string','回来时间'=>'string',
                '跟进时间'=>'string',
                '总金额'=>'price','支付金额'=>'price','核销金额'=>'price','人数'=>'integer','跟单人'=>'string','主播'=>'string']);

            foreach($orders as $order) {
                $writer->writeSheetRow('Sheet1', [$order->mobile, $order->os_name, $order->is_zhibo, "$order->sn", date('Y-m-d H:i:s', intval($order->create_at/1000)),$order->order_status_name, $order->status_name,
                    $order->product_name, $order->remark, $order->contact, $order->travel_date, $order->travel_end,
                    $order->last_follow ? date('Y-m-d H:i:s', intval($order->last_follow/1000)):'',
                    $order->total_price/100, $order->actual_price/100, $order->asset_price/100, $order->quantity, $order->admin->username ?? '', $order->anchor->username ?? '']);
            }

            $file_name = "订单导出-".date('Ymd-His').".xlsx";

            $response = response();
            $c = $writer->writeToString();
            $response->withHeaders([
                'Content-Type' => 'application/force-download',
                'Content-Disposition' => 'attachment; filename="'.$file_name.'"',
                'Content-Transfer-Encoding' => 'binary',
                'Cache-Control' => 'max-age=0',
            ])->withBody($c);
            return $response;
            // return;
        }else{
            $orders = $query->paginate($request->get('limit',10));
        }
        if ($unUseType > 0) {
            Log::info('sql:' . $query->getLastSql());
        }

        foreach($orders as &$order) {
            if(empty($order->personnel)) {
                $order->personnel = new stdClass();
            }
        }

        $list = $orders->visible(['admin' => ['username','name','avatar']])->hidden(['check_sn'])->append(['order_status_name','status_name']);

        $localMobiles = [];
        foreach ($list as &$value) {
            $mobileInfo = $value['mobileInfo'] ?? '';
            if (empty($value['mobileInfo'])) {
                try {
                    if (isset($localMobiles[$value['mobile']])) {
                        // 防止重复查询第三方接口
                        $mobileInfo = $localMobiles[$value['mobile']];
                    } else {
                        $mobileInfo = (new ThirdApiService())->getMobileArea($value['mobile']);
                        $localMobiles[$value['mobile']] = $mobileInfo;
                    }
                } catch (\Exception $exception) {
                    $mobileInfo = [];
                    Log::info('查询手机归属地失败：' . $exception->getMessage());
                }
            }
            $value['mobileInfo'] = $mobileInfo;
            $value['source_name'] = isset(Line::SOURCE_MAP[$value['os']])?Line::SOURCE_MAP[$value['os']]:$value['os'];
            $value['level_name'] = isset(Line::LEVELS_MAP[$value['level']])?Line::LEVELS_MAP[$value['level']]:$value['level'];
        }

        $levels = Line::LEVELS_MAP;
        $source = Line::SOURCE_MAP;
        return $this->success($list,null,['timetype'=>  OrdersXs::timeType, 'oss' => [], 'levels' => $levels, 'source' => $source]);
    }

    public function all(Request $request) {
        $sn = $request->get('sn');

        if($sn) {
            $where[] = ['sn','=', $sn];
        }
        $orders = OrdersXs::with(['admin'])->where($where)->order('update_time','desc')->order('create_at','desc')->order('id','desc')->paginate($request->get('limit',10));

        return $this->success($orders->visible(['admin' => ['username','name','avatar']])->hidden(['mobile','check_sn'])->append(['order_status_name','status_name','os_name']));
    }

    public function pub(Request $request) {
        $orders = OrdersXs::where('admin_id', 0)
            ->where('is_public_pool', 1)
            ->where('shop_id', $request->admin->shop_id)
            ->order('create_at','desc')
            ->order('id','desc')
            ->paginate($request->get('limit',10));

        $list = $orders->hidden(['mobile','check_sn'])->append(['order_status_name','os_name']);

        foreach ($list as &$value) {

            $value['source_name'] = isset(Line::SOURCE_MAP[$value['os']])?Line::SOURCE_MAP[$value['os']]:$value['os'];
            $value['level_name'] = isset(Line::LEVELS_MAP[$value['level']])?Line::LEVELS_MAP[$value['level']]:$value['level'];

            // $value['mobile'] = substr_replace($value['mobile'], '****', 3, 4);
        }


        return $this->success($list);
    }

    //公海领取订单
    public function fish(Request $request) {
        $id = $request->post('id');
        if(empty($id) || intval($id) <=0) return $this->error(2001, '请提交id');

        $order = OrdersXs::find($id);
        if($order->admin_id > 0 ) return $this->error(2002, '订单没有找到或者已经转移到其他人');

        try{
            OrdersXs::fish($order->id, $request->admin->id);
            return $this->success(null);
        }catch(\Exception $e){
            return $this->error(2003, '公海获取订单失败,订单没有找到或者已经转移到其他人');
        }
    }

    public function info(Request $request) {
        $id = $request->get('id');

        if(empty($id) || intval($id) <=0) return $this->error(2001, '请提交id');

        $where[] = ['id','=',$id];

        if($request->admin->is_super == 0) {
            $where[] = ['admin_id','=',$request->admin->id];
        }
        $item = OrdersXs::where($where)->with(['follow.admin','finance', 'dyOrderAppointments'])->find();

        if(empty($item)) {
            return $this->error(2002, '订单没有找到或者已经转移到其他人');
        }
        Logs::see($item->id, $request->admin->id);

        if(empty($item->personnel)) {
            $item->personnel = new stdClass();
        }

        return $this->success($item->append(['order_status_name','os_name']));
    }

    //核销,跟单
    public function save(Request $request) {
        $id = $request->post('id');
        $next_follow = $request->post('next_follow');
        $check_sn = $request->post('check_sn');
        $desc = $request->post('desc');
        $status = $request->post('status');
//        $is_wechat = $request->post('is_wechat');
        $personnel = $request->post('personnel');
        $travel_date = $request->post('travel_date');
        $travel_end = $request->post('travel_end');
        $post = $request->post();
        unset($post['is_wechat']);

        if(empty($desc) || empty($status)) return $this->error(2004, '跟进说明需要填写！');
        if(empty($personnel)) return $this->error(2004, '人员情况需要填写！');
        if(empty($travel_date)) return $this->error(2004, '出游时间需要填写！');
        if (in_array($status, [0, 1])){
            if(empty($next_follow)) return $this->error(2004, '下次跟进时间需要填写！');
        }
        $is_wechat = 0;
        if ($status == 2){
            $next_follow = '';
//            if(empty($travel_end)) return $this->error(2004, '返回日期需要填写！');
//            if(empty($check_sn)) return $this->error(2004, '核销码需要填写！');

            $is_wechat = 1;
        }

        $lock = 'Travel:Order:'.$id.':lock';
        $back = Redis::set($lock, $request->admin->id, 'EX', 6, 'NX');
        if(!$back) return $this->error(2005, '锁定失败');

        $item = OrdersXs::where('id', $id)->where('admin_id', $request->admin->id)->find();
        if(empty($item)) return $this->error(2006, '订单不存在');
        if($item->admin_id != $request->admin->id) return $this->error(2007, '订单已经发生转移');

        //if(!empty($travel_date) && empty($travel_end)) return $this->error(2008, '核销的订单请填写出行和返回时间');

        Follows::create([
            'admin_id' => $request->admin->id,
            'order_id' => $item->id,
            'status'   => $status,
            'desc'     => $desc,
            'is_private' => 1,
        ]);

        // 加入公海
        if($status == 5) {
            if ($item->is_direct_mode == 1) {
                return $this->error(2007, '直连订单不可加入公海');
            }
            $item->status = 0;
            $item->admin_id = 0;
            $item->is_public_pool = 1;
            $item->last_pool_date = date('Y-m-d H:i:s');
            $item->remark = $desc;
            $item->save();
            return $this->success($item);
        }else{
            // 记录核销日期
            if ($status == 2 && $item->status !=2) {
                $item->verification_date = date('Y-m-d H:i:s');
            }
            //修改状态
            $item->check_sn = $check_sn;
            $item->status = $status;
            if($is_wechat){
                $item->is_wechat = 1;
            }
            if(isset($post['wechat'])){
                $item->wechat = $post['wechat'];
            }

            if(isset($post['fans_status'])){
                $item->fans_status = $post['fans_status'];
            }

            !empty($travel_date) && empty($item->travel_date) ? $item->travel_date = date('Y-m-d', strtotime($travel_date)) : null;
            !empty($travel_end) && empty($item->travel_end)  ? $item->travel_end = date('Y-m-d', strtotime($travel_end)) : null;
            $item->personnel = json_encode($personnel);

            $item->last_follow = microtime(true) * 1000;
            $item->next_follow = strtotime($next_follow??'') * 1000;
            $item->remark = $desc;

            $back = $item->save();
        }

        if($back) {
            if ($item->check_sn) {
                //通过命令行核销订单
                Redis::LPUSH('Travel:Order:check:lits',json_encode([
                    'id' => $item->id,
                    'check_sn' => $item->check_sn
                ]));

                // 限制只被录入一次
                $has = Logs::where('admin_id', $request->admin->id)->whereIN('action',[2,3])->where('check_sn',$check_sn)->find();
                if(empty($has)) {
                    Logs::pass($item->id, $request->admin->id, $check_sn);
                }
            }
            if($item->is_wechat > 0 ) {
                $has = Logs::where('admin_id', $request->admin->id)->where('order_id', $item->id)->where('action', 5)->find();
                if(empty($has)) {
                    Logs::wechat($item->id, $request->admin->id);
                }
            }

            return $this->success($item);
        }else{
            return $this->error(2008, '保存订单失败');
        }
    }

    public function pass(Request $request) {
        $check_sn = $request->post('check_sn');
        if(!is_numeric($check_sn)) return $this->error(2008, '格式错误');

        $has = OrdersXs::where('check_sn', $check_sn)->find();
        if($has) {
            return $this->error(2008, '核销码已经被使用了');
        }

        //限制只被录入一次
        $has = Logs::where('admin_id', $request->admin->id)->whereIN('action',[2,3])->where('check_sn', $check_sn)->find();
        if(empty($has)) {
            Logs::pass2($request->admin->id, $check_sn);
        }

        //通过命令行核销订单
        Redis::LPUSH('Travel:Order:check:lits',json_encode([
            'admin_id' => $request->admin->id,
            'check_sn' => $check_sn
        ]));

        return $this->success(null,'提交成功');
    }

    //把订单拉回
    public function backlist(Request $request) {
        $sn = $request->get('sn');
        $status = $request->get('status',null);
        $mobile = $request->get('mobile');

        $query = BacksXs::with(['into','outto','orders','apply'])
            ->where('is_private',1)
            ->where('shop_id', $request->admin->shop_id)
            ->where(function($query) use($request) {
            $query->where('admin_id', $request->admin->id)->WhereOr('admin', $request->admin->id);
        })->order('update_time','desc')->order('id','desc');

        if($sn) {
            $order_id = OrdersXs::where('sn', $sn)->value('id');
            $query->where('order_id', $order_id);
        }
        if ($mobile) {
            $orderIds = OrdersXs::where('mobile|wechat', $mobile)->column('id');
            $query->whereIn('order_id', $orderIds);
        }

        if($status != null) {
            $query->where('status', $status);
        }

        $backs = $query->paginate($request->get('limit',10));
        foreach($backs as &$back){
            if ($back->admin_id == $request->admin->id || $back->admin == $request->admin->id){
                $back->self = 0;
                if($back->apply_id == $request->admin->id) {
                    $back->self = 3;
                }
            }else{
                $back->self = 1;
            }
        }

        return $this->success($backs->append(['order_status_name','os_name']),null, OrdersXs::OSS);
    }

    // 批量流转出
    public function backBatch(Request $request) {
        $sn = $request->post('sn');
        if (empty($sn) || !is_array($sn)) {
            return $this->error(2001, '请选择流转的订单');
        }
        $toAdminId = $request->post('to_admin_id');
        if (!$toAdminId) {
            return $this->error(2001, '请选择流转的客服');
        }

        // OrdersXs::whereIn('sn', $sn)->update(['admin_id'=> $toAdminId]);

        foreach ($sn as $value) {
            $item = OrdersXs::where('sn', $value)->find();
            if($item->admin_id == $toAdminId) {
                continue;
            }

            BacksXs::create([
                'order_id' => $item->id,
                'admin_id' => $toAdminId,
                'is_private' => 1,
                'admin' => $item->admin_id,
                'apply_id' => $request->admin->id,
                'shop_id' => $request->admin->shop_id,
                'status' => 1
            ]);
            OrdersXs::where('sn', $value)->update(['admin_id'=> $toAdminId, 'update_time' => time()]);
            // 发送短信
            \app\server\OrdersXs::sendOrderSms($toAdminId, $item);
        }

        return $this->success(true);
    }

    //把订单拉回
    public function back(Request $request) {
        $sn = $request->post('sn');
//        $os = $request->post('os');
        $to_admin_id = $request->post('to_admin_id');
        $admin_id = empty($to_admin_id) ? $request->admin->id : $to_admin_id;

        if(empty($sn)) return $this->error(2001, '提交单号');

        $item = OrdersXs::where('sn', $sn)->find();
        if(empty($item)) return $this->error(2002, '订单没有找到');
        if($item->admin_id == $admin_id) return $this->error(2002, '订单已经在账号上,不需要再拉取!');

        //管理员不用审批
        $status = 0;
        if ($request->admin->is_super == 1 || $request->admin->id == 1){
            $status = 1;
        }

        $backs = [
            'order_id' => $item->id,
            'admin_id' => $admin_id,
            'admin' => $item->admin_id,
            'is_private' => 1,
            'apply_id' => $request->admin->id,
            'shop_id' => $request->admin->shop_id,
            'status' => $status
        ];
        try {
            Db::transaction(function() use ($backs,$item) {
                BacksXs::create($backs);
                LogsXs::todo($backs['order_id'], $backs['apply_id'], $backs['status'] == 1 ? 12 : 7,$item->sn);

                if ($backs['status'] == 1){//管理员审批处理
                    OrdersXs::where('id', $backs['order_id'])->where('admin_id', $backs['admin'])->update(['admin_id'=> $backs['admin_id']]);
                    $other = BacksXs::where('order_id', $backs['order_id'])->where('status', 0)->lock()->select();
                    foreach($other as $o) {
                        BacksXs::where('id', $o->id)->where('status', 0)->update(['status' => 3]);
                        LogsXs::todo($o->order_id, $o->admin, 10); //取消其他转单需求
                    }
                }
            });
        }catch (\Exception $e){
            return $this->error(2003, '创建拉回记录失败了');
        }

        return $this->success(true);
    }

    public function toBack(Request $request) {
        $sn = $request->post('sn');
        $os = $request->post('os');
        $to_admin_id = $request->post('to_admin_id');
        $admin_id = empty($to_admin_id) ? $request->admin->id : $to_admin_id;

        if(empty($sn)) return $this->error(2001, '提交单号');

        $item = OrdersXs::where('sn', $sn)->where('os', $os)->find();
        if(empty($item)) return $this->error(2002, '订单没有找到');
        if($item->admin_id == $admin_id) return $this->error(2002, '订单已经在账号上,不需要再拉取!');

        //管理员不用审批
        $status = 0;
        if ($request->admin->is_super == 1 || $request->admin->id == 1){
            $status = 1;
        }

        $backs = [
            'order_id' => $item->id,
            'admin_id' => $admin_id,
            'admin' => $item->admin_id,
            'apply_id' => $request->admin->id,
            'status' => $status
        ];
        try {
            Db::transaction(function() use ($backs,$item) {
                Backs::create($backs);
                Logs::todo($backs['order_id'], $backs['apply_id'], $backs['status'] == 1 ? 12 : 7,$item->sn);

                if ($backs['status'] == 1){//管理员审批处理
                    OrdersXs::where('id', $backs['order_id'])->where('admin_id', $backs['admin'])->update(['admin_id'=> $backs['admin_id']]);
                    $other = Backs::where('order_id', $backs['order_id'])->where('status', 0)->lock()->select();
                    foreach($other as $o) {
                        Backs::where('id', $o->id)->where('status', 0)->update(['status' => 3]);
                        Logs::todo($o->order_id, $o->admin, 10); //取消其他转单需求
                    }
                }
            });
        }catch (\Exception $e){
            return $this->error(2003, '创建拉回记录失败了');
        }

        return $this->success(true);
    }

    /**
     * 同意操作
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function backpass(Request $request) {
        $id = $request->post('id', 0);

        $item = Backs::where('id', $id)->find();
        if($item->status != 0) return $this->error(2004, '记录已经处理了.');
        if($item->admin != $request->admin->id && $item->admin != $item->apply_id) return $this->error(2005, '管理员错误');

        try {
            Backs::change($item);
            return $this->success('');
        }catch(\Exception $e) {
            return $this->error(2006, '确认失败了');
        }
    }

    /**
     * 拒绝操作
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function backrefuse(Request $request) {
        $id = $request->post('id', 0);

        $item = Backs::where('id', $id)->find();
        if(empty($item)) return $this->error(2004, '记录没有找到.');
        if($item->status != 0) return $this->error(2004, '记录已经处理了.');
        if($item->admin != $request->admin->id && $item->admin != $item->apply_id) return $this->error(2005, '管理员错误');

        try {
            Backs::refuse($item);
            return $this->success('');
        }catch(\Exception $e) {
            return $this->error(2006, '确认失败了');
        }
    }

    /**
     * 取消操作
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function backcancel(Request $request)
    {
        $id = $request->post('id', 0);

        $item = Backs::where('id', $id)->find();
        if(empty($item)) return $this->error(2004, '记录没有找到.');
        if($item->status != 0) return $this->error(2004, '记录已经处理了.');
        if($item->admin != $request->admin->id && $item->admin == $item->apply_id) return $this->error(2005, '管理员错误');

        try {
            Backs::cancel($item);
            return $this->success('');
        }catch(\Exception $e) {
            return $this->error(2006, '确认失败了');
        }
    }

    /**
     * 一键修复订单问题
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function oneClickRepair(Request $request)
    {
        try {
            $ids = $request->post('id', 0);

            $idArr = explode(',', $ids);
            // 批量同步暂时关闭
            if (count($idArr) > 1) {
                return $this->success([]);
            }

            $flow = [];
            foreach ($idArr as $id) {
                Log::info('同步订单ID：' . $id);

                $order = OrdersXs::where('id', $id)->find();

                if (empty($order)) {
                    return $this->success('同步成功');
                }

                // 订单是待使用状态，先同步第三方状态
                if (ServerOrdersXs::isDaishiyong($order)) {
                    ServerOrdersXs::syncFromThird($order);
                    $flow[] = "订单为待使用状态，已请求第三方进行同步。";
                }
            }

            return $this->success(implode("\n", $flow));
        }catch(\Exception $e) {
            Log::info(sprintf('oneClickRepair error:%s, %s, %s', $e->getMessage(), $e->getFile(), $e->getLine()) );
            return $this->success([]);
        }
    }

    /**
     * 预约处理
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function changeAppointmentStatus(Request $request)
    {
        try {
            $ids = $request->post('id', 0);
            $idArr = explode(',', $ids);

            $flow = [];
            foreach ($idArr as $id) {
                Log::info('同步订单ID：' . $id);

                $order = OrdersXs::where('id', $id)->find();

                if (empty($order)) {
                    return $this->error(2004, '记录没有找到.');
                }

                // 订单是待使用状态，先同步第三方状态
                $order['appointment_status'] = 2;
                $order->save();
            }

            return $this->success(implode("\n", $flow));
        }catch(\Exception $e) {
            return $this->error(2006, '出错了：' . $e->getMessage());
        }
    }

    /**
     * 获取预约信息
     * @param Request $request
     * @return \support\Response
     */
    public function getAppointInfo(Request $request) {
        try {
            $id = $request->get('id', 0);
            $order = OrdersXs::where('id', $id)->find();
            if (empty($order)) {
                return $this->error(2004, '记录没有找到.');
            }

            // 抖音预约信息
            $appoint = DyOrderProductAppointments::query()->where(['dy_order_id' => $order['sn']])->find();
            return $this->success($appoint);
        }catch(\Exception $e) {
            return $this->error(2006, '出错了：' . $e->getMessage());
        }
    }

    /**
     * 抖音确认订单
     * @param Request $request
     * @return \support\Response
     */
    public function dyOrderConfirm(Request $request) {
        try {
            $id = $request->post('id', 0);
            $order = OrdersXs::where('id', $id)->find();
            if (empty($order)) {
                return $this->error(2004, '记录没有找到.');
            }
            // 1：接单 2：拒单
            if (!$request->post('confirm_result') || !in_array($request->post('confirm_result'), [1, 2])) {
                return $this->error(2005, '确认状态错误');
            }
            $confirmResult = $request->post('confirm_result', 1);

            // 抖音预约信息z
            $appoint = DyOrderProductAppointments::query()->where(['source_order_id' => $order['sn']])->order('id desc')->find();
            if (empty($appoint)) {
                return $this->error(2004, '该订单暂未预约，不可接单');
            }
            $dyOrderProduct = DyOrderProducts::query()->where(['dy_order_id' => $order['sn']])->find();
            if (empty($dyOrderProduct)) {
                return $this->error(2004, '订单商品未找到，不可接单');
            }

            $jnCategoryIds = [
                "18004001", "18004003", "18004004", "18004005", "18004006", "18004007", "18004008", "18004009",
                "18004010", "18003004", "4015004", "18003001", "18003002", "18003003", "18010001", "32001001",
                "18002001", "18011001", "18011002", "18012001", "18012002", "18010001", "32001001", "32006002",
                "32007001", "32007002", "32007003",
            ];
            $zyxCategoryIds = ['32002001', '18010002', '32006001'];

            $confirmData = [
                'order_id' => $appoint->dy_order_id,
                'source_order_id' => $appoint->source_order_id,
                'confirm_info' => [
                    'confirm_result' => $confirmResult,
                    'reject_code' => $request->post('reject_code'), // 1: 库存已约满  2：商品需加价  3：无法满足顾客需求
//                    'hotel_info' => [], // 境内住宿类目/酒景套餐 必填
//                    'play_info' => [], // 境内游玩类目 必填
//                    'free_travel_info' => $freeTravelInfo, // 自由行类目 必填
                ],
            ];
            // 自由行必填参数
            $dyApiService = new DyApiService();
            if ($confirmResult == 1 && in_array($dyOrderProduct->category_id, $zyxCategoryIds)) {
                $onedayTourList = [];
                foreach ($dyOrderProduct->travel_details as $k => $travel_detail) {
                    array_push($onedayTourList, [
                        'sequence' => $k+1,
                        'hotel_info_list' => [],
                        'play_info_list' => []
                    ]);
                }
                $confirmData['confirm_info']['free_travel_info']['oneday_tour_list'] = $onedayTourList;
            }
            if ($confirmResult == 1 && in_array($dyOrderProduct->category_id, $jnCategoryIds)) {
                // 获取poi信息
                $poiRes = $dyApiService->send(DyApiService::POI_QUERY, ['order_id' => $order->sn, 'account_id' => env('DY_ACCOUNT_ID')]);
                $payInfo = [
                    'entrance_types' => [3],
                    'poi_info' => $poiRes['data']['poi_list'] ?? []
                ];
                $showCerts = [];
                if (isset($appoint['book_info'])) {
                    $bookInfo = json_decode(json_encode($appoint['book_info']), true);
                    foreach ($bookInfo['occupancies'] as $info) {
                        array_push($showCerts, [
                            'name' => $info['name'],
                            'card_no' => $info['license_id'] ?? '',
                            'cert_no' => $info['cert_no'] ?? '',
                        ]);
                    }
                }
                $payInfo['show_certs'] = $showCerts;
                $confirmData['confirm_info']['play_info'] = $payInfo;
            }

            $res = $dyApiService->send(DyApiService::ORDER_CONFIRM, $confirmData);
            if ($res['flag'] == true) {
                // 预约成功，更新预约状态
                $order->appointment_status = 2;
                $order->save();

                return $this->success($res);
            }
            return $this->error(2005, $res['data']['description']);
        }catch(\Exception $e) {
            return $this->error(2006, '出错了：' . $e->getMessage());
        }
    }

    /**
     * 抖音取消订单
     * @param Request $request
     * @return \support\Response
     */
    public function dyOrderCancel(Request $request) {
        try {
            $id = $request->post('id', 0);
            $order = OrdersXs::where('id', $id)->find();
            if (empty($order)) {
                return $this->error(2004, '记录没有找到.');
            }
            // 类型 1：取消预约并退款，2：取消预约
            if (!$request->post('cancel_type') || !in_array($request->post('cancel_type'), [1, 2])) {
                return $this->error(2005, '确认状态错误');
            }
            $cancelType = $request->post('cancel_type', 1);

            $bookInfo = DyOrderProductAppointments::query()->where(['source_order_id' => $order->sn])->order('id desc')->find();
            if (empty($bookInfo)) {
                return $this->error(2006, '预约记录未找到');
            }
            $douyinService = new Douyin(5);
            $res = $douyinService->bookCancelSmsSend($cancelType, $bookInfo->dy_order_id, $bookInfo->source_order_id);
            Log::info('dyOrderCancel:' . json_encode($res));
            if ($res->status_code == 0) {
                return $this->success($res);
            }
            return $this->error(2005, $res->status_msg ?? '取消失败');
        }catch(\Exception $e) {
            return $this->error(2006, '出错了：' . $e->getMessage());
        }
    }

    /**
     * 抖音售后处理
     * @param Request $request
     * @return \support\Response
     */
    public function dyAfterSaleOrderHandle(Request $request) {
        try {
            $id = $request->post('id', 0);
            $order = OrderAfterSales::where('id', $id)->find();
            if (empty($order)) {
                return $this->error(2004, '记录没有找到.');
            }
            if (!in_array($request->post('audit_result'), [1, 2])) {
                return $this->error(2004, '审核状态异常.');
            }
            $auditResult = $request->post('audit_result');

            $douyinService = new Douyin(5);
            // $auditOrderId, $auditResult, $bookId, $bookOrderId
            $res = $douyinService->audit($order->audit_order_id, $auditResult, $order->book_id, $order->order_id);
            if ($res->status_code == 0) {
                OrderAfterSales::query()->where('order_id', $order->order_id)->update(['status' => $auditResult]);
                return $this->success($res);
            }
            return $this->error(2005, $res->status_msg ?? '操作失败');
        }catch(\Exception $e) {
            return $this->error(2006, '出错了：' . $e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DbException
     */
    public function lstForPrivate(Request $request) {
        $sn = $request->get('sn');
        $where = [
            ['create_admin_id', '=', $request->admin->id],
        ];
        if($sn) {
            $where[] = ['sn','=', $sn];
        }

        $orderBy['create_at'] = 'desc';
        $query = OrdersXs::with(['admin','anchor','backs', 'mobileInfo'])->where($where)
            ->order($orderBy);

        $mobile = $request->get('mobile');
        if($mobile){
            $query->where('mobile','like', '%'.$mobile.'%');
        }

        $productName = $request->get('keyword');
        if($productName){
            $query->where('product_name|mobile|contact','like', '%'.$productName.'%');
        }
        if (is_numeric($request->get('status'))) {
            $query->where('status', '=', $request->get('status'));
        }

        $orders = $query->paginate($request->get('limit',10));
        $list = $orders->visible(['admin' => ['username','name','avatar']])->hidden(['check_sn'])->append(['order_status_name','status_name']);
        foreach ($list as &$value) {
            $mobileInfo = $value['mobileInfo'] ?? '';
            $value['mobileInfo'] = $mobileInfo;
            $value['source_name'] = isset(Line::SOURCE_MAP[$value['os']])?Line::SOURCE_MAP[$value['os']]:$value['os'];
            $value['level_name'] = isset(Line::LEVELS_MAP[$value['level']])?Line::LEVELS_MAP[$value['level']]:$value['level'];
        }

        return $this->success($list);
    }

}
