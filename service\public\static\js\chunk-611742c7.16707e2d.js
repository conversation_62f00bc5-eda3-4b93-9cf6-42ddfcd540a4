(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-611742c7"],{"579b":function(t,e,i){"use strict";i("ede9")},"96e9":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"contract-browse"},[i("el-row",[i("el-col",{attrs:{span:24}},[i("div",{staticClass:"search-form"},[i("el-form",{ref:"form",attrs:{inline:!0,model:t.dataForm,"label-width":"60px"}},[i("el-form-item",{attrs:{label:"关键字:"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"400px"},attrs:{placeholder:"请输入搜索关键字"},model:{value:t.dataForm.title,callback:function(e){t.$set(t.dataForm,"title",e)},expression:"dataForm.title"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"success"},on:{click:t.onSubmit}},[t._v("查询")])],1)],1)],1)])],1),i("div",{staticClass:"browse-container"},[i("div",{staticClass:"browse-left",staticStyle:{overflow:"auto",padding:"10px"}},t._l(t.productList,(function(e){return i("div",{key:e.id,staticClass:"btn",class:{active:t.currentProductId===e.id},on:{click:function(i){return t.handleSelectProduct(e.id)}}},[t._v(" "+t._s(e.product_name)+" ")])})),0),i("div",{staticClass:"browse-right"},[i("ul",{staticClass:"infinite-list",staticStyle:{overflow:"auto"}},t._l(t.contractList,(function(e){return i("li",{key:e.id,staticClass:"browse-right-container"},[i("div",{staticClass:"title"},[i("span",[t._v(t._s(e.title))])]),i("div",{staticClass:"images-container"},[e.ye_license&&t.getImages(e.ye_license).length>0?i("div",{staticClass:"image-section"},[i("h3",[t._v("营业执照")]),i("div",{staticClass:"image-grid"},t._l(t.getImages(e.ye_license),(function(a,s){return i("div",{key:"ye_"+s,staticClass:"image-item"},[i("el-image",{staticClass:"grid-image",attrs:{src:a,fit:"cover","preview-src-list":t.getImages(e.ye_license)}}),i("div",{staticClass:"image-actions"},[i("el-tooltip",{attrs:{content:"复制链接",placement:"top"}},[i("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-link",circle:""},on:{click:function(e){return e.stopPropagation(),t.copyImageUrl(a)}}})],1),i("el-tooltip",{attrs:{content:"下载图片",placement:"top"}},[i("el-button",{attrs:{size:"mini",type:"success",icon:"el-icon-download",circle:""},on:{click:function(e){return e.stopPropagation(),t.downloadImage(a)}}})],1)],1)],1)})),0)]):t._e(),e.jy_license&&t.getImages(e.jy_license).length>0?i("div",{staticClass:"image-section"},[i("h3",[t._v("经营许可证")]),i("div",{staticClass:"image-grid"},t._l(t.getImages(e.jy_license),(function(a,s){return i("div",{key:"jy_"+s,staticClass:"image-item"},[i("el-image",{staticClass:"grid-image",attrs:{src:a,fit:"cover","preview-src-list":t.getImages(e.jy_license)}}),i("div",{staticClass:"image-actions"},[i("el-tooltip",{attrs:{content:"复制链接",placement:"top"}},[i("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-link",circle:""},on:{click:function(e){return e.stopPropagation(),t.copyImageUrl(a)}}})],1),i("el-tooltip",{attrs:{content:"下载图片",placement:"top"}},[i("el-button",{attrs:{size:"mini",type:"success",icon:"el-icon-download",circle:""},on:{click:function(e){return e.stopPropagation(),t.downloadImage(a)}}})],1)],1)],1)})),0)]):t._e(),e.pay_qr&&t.getImages(e.pay_qr).length>0?i("div",{staticClass:"image-section"},[i("h3",[t._v("收款二维码")]),i("div",{staticClass:"image-grid"},t._l(t.getImages(e.pay_qr),(function(a,s){return i("div",{key:"pay_"+s,staticClass:"image-item"},[i("el-image",{staticClass:"grid-image",attrs:{src:a,fit:"cover","preview-src-list":t.getImages(e.pay_qr)}}),i("div",{staticClass:"image-actions"},[i("el-tooltip",{attrs:{content:"复制链接",placement:"top"}},[i("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-link",circle:""},on:{click:function(e){return e.stopPropagation(),t.copyImageUrl(a)}}})],1),i("el-tooltip",{attrs:{content:"下载图片",placement:"top"}},[i("el-button",{attrs:{size:"mini",type:"success",icon:"el-icon-download",circle:""},on:{click:function(e){return e.stopPropagation(),t.downloadImage(a)}}})],1)],1)],1)})),0)]):t._e(),e.hotel_pictures&&t.getImages(e.hotel_pictures).length>0?i("div",{staticClass:"image-section"},[i("h3",[t._v("酒店图片")]),i("div",{staticClass:"image-grid"},t._l(t.getImages(e.hotel_pictures),(function(a,s){return i("div",{key:"hotel_"+s,staticClass:"image-item"},[i("el-image",{staticClass:"grid-image",attrs:{src:a,fit:"cover","preview-src-list":t.getImages(e.hotel_pictures)}}),i("div",{staticClass:"image-actions"},[i("el-tooltip",{attrs:{content:"复制链接",placement:"top"}},[i("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-link",circle:""},on:{click:function(e){return e.stopPropagation(),t.copyImageUrl(a)}}})],1),i("el-tooltip",{attrs:{content:"下载图片",placement:"top"}},[i("el-button",{attrs:{size:"mini",type:"success",icon:"el-icon-download",circle:""},on:{click:function(e){return e.stopPropagation(),t.downloadImage(a)}}})],1)],1)],1)})),0)]):t._e(),e.restaurant_picture&&t.getImages(e.restaurant_picture).length>0?i("div",{staticClass:"image-section"},[i("h3",[t._v("餐厅图片")]),i("div",{staticClass:"image-grid"},t._l(t.getImages(e.restaurant_picture),(function(a,s){return i("div",{key:"rest_"+s,staticClass:"image-item"},[i("el-image",{staticClass:"grid-image",attrs:{src:a,fit:"cover","preview-src-list":t.getImages(e.restaurant_picture)}}),i("div",{staticClass:"image-actions"},[i("el-tooltip",{attrs:{content:"复制链接",placement:"top"}},[i("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-link",circle:""},on:{click:function(e){return e.stopPropagation(),t.copyImageUrl(a)}}})],1),i("el-tooltip",{attrs:{content:"下载图片",placement:"top"}},[i("el-button",{attrs:{size:"mini",type:"success",icon:"el-icon-download",circle:""},on:{click:function(e){return e.stopPropagation(),t.downloadImage(a)}}})],1)],1)],1)})),0)]):t._e()])])})),0)])])],1)},s=[],n=(i("3dd5"),i("1652"),i("9919"),i("90c8"),i("3363"),i("bd1a"),i("f2e9"),i("3399"),{name:"ContractBrowse",data:function(){return{productList:[],allContractList:[],contractList:[],currentProductId:null,dataForm:{title:""},loading:!1}},created:function(){this.getContractList()},methods:{getContractList:function(){var t=this;this.loading=!0;var e={limit:1e3,title:this.dataForm.title};this.$axios.get("/admin/line/contract",{params:e}).then((function(e){t.allContractList=e.data.data||[];var i=new Map;t.allContractList.forEach((function(t){t.product&&t.product.id&&!i.has(t.product.id)&&i.set(t.product.id,{id:t.product.id,product_name:t.product.product_name})})),t.productList=Array.from(i.values()),t.loading=!1,t.productList.length>0?t.handleSelectProduct(t.productList[0].id):t.contractList=[]})).catch((function(){t.loading=!1}))},getImages:function(t){return t?t.split(",").filter((function(t){return""!==t.trim()})):[]},handleSelectProduct:function(t){this.currentProductId=t,this.contractList=this.allContractList.filter((function(e){return e.product&&e.product.id==t}))},onSubmit:function(){this.getContractList()},copyImageUrl:function(t){var e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select(),document.execCommand("copy"),document.body.removeChild(e),this.$message({message:"图片链接已复制到剪贴板",type:"success"})},downloadImage:function(t){try{var e=document.createElement("a");e.href=t,e.target="_blank";var i=t.substring(t.lastIndexOf("/")+1);e.download=i||"image.png",document.body.appendChild(e),e.click(),document.body.removeChild(e),this.$message.success("下载已开始")}catch(a){this.$message.error("下载失败"),console.error("Download error:",a)}}}}),c=n,o=(i("579b"),i("8a34")),r=Object(o["a"])(c,a,s,!1,null,"6f880ecc",null);e["default"]=r.exports},9919:function(t,e,i){"use strict";var a=i("054c"),s=i("6665");t.exports=a("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),s)},ede9:function(t,e,i){}}]);