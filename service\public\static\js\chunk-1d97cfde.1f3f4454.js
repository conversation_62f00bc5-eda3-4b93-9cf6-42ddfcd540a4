(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1d97cfde"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),Math.easeInOutQuad=function(t,e,a,o){return t/=o/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var o=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function i(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(t,e,a){var r=n(),s=t-r,l=20,c=0;e="undefined"===typeof e?500:e;var u=function(){c+=l;var t=Math.easeInOutQuad(c,r,s,e);i(t),c<e?o(u):a&&"function"===typeof a&&a()};u()}},2953:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"app-container"},[o("div",{staticClass:"filter-container"},[o("el-input",{staticClass:"filter-item",staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"用户名"},model:{value:t.listQuery.username,callback:function(e){t.$set(t.listQuery,"username",e)},expression:"listQuery.username"}}),o("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v(" 搜索 ")]),o("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-circle-plus"},on:{click:function(e){return t.handleCreate({routes:[]})}}},[t._v(" 添加 ")])],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[o("el-table-column",{attrs:{align:"center",label:"操作",width:"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(a){return t.handleCreate(e.row)}}},[t._v(" 修改 ")]),o("el-button",{staticStyle:{"margin-top":"5px"},attrs:{type:"success",size:"small",icon:"el-icon-map-location"},on:{click:function(a){return t.handleUpdateRoutes(e.row)}}},[t._v(" 更新线路 ")]),o("el-button",{staticStyle:{"margin-top":"5px"},attrs:{type:"info",size:"small",icon:"el-icon-document"},on:{click:function(a){return t.handleViewLogs(e.row)}}},[t._v(" 操作日志 ")])]}}])}),o("el-table-column",{attrs:{align:"center",label:"ID",width:"80",prop:"id"}}),o("el-table-column",{attrs:{align:"center",label:"用户名",width:"160",prop:"username"}}),o("el-table-column",{attrs:{align:"center",label:"姓名",width:"160",prop:"name"}}),o("el-table-column",{attrs:{align:"center",label:"手机",width:"160",prop:"mobile"}}),o("el-table-column",{attrs:{"class-name":"status-col",label:"状态",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){var a=e.row;return[o("el-switch",{attrs:{"active-text":"启用","active-value":1,"inactive-value":0,"inactive-text":"禁用"},on:{change:function(e){return t.setStatus(a)}},model:{value:a.status,callback:function(e){t.$set(a,"status",e)},expression:"row.status"}})]}}])}),o("el-table-column",{attrs:{width:"180px",align:"center",label:"创建时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("span",[t._v(t._s(t._f("parseTime")(e.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),o("el-table-column",{attrs:{width:"180px",align:"center",label:"修改时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("span",[t._v(t._s(t._f("parseTime")(e.row.update_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),o("el-table-column",{attrs:{width:"180px",align:"center",label:"最后登陆地址"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("span",[t._v(t._s(e.row.ip_address))])]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}),o("el-dialog",{attrs:{title:"修改管理员",visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e}}},[o("el-form",{attrs:{"label-width":"120px",model:t.item}},[o("el-form-item",{attrs:{label:"用户名"}},[o("el-input",{attrs:{name:"username",placeholder:"管理员的用户名"},model:{value:t.item.username,callback:function(e){t.$set(t.item,"username",e)},expression:"item.username"}})],1),o("el-form-item",{attrs:{label:"姓名"}},[o("el-input",{attrs:{name:"name",placeholder:"管理员的姓名"},model:{value:t.item.name,callback:function(e){t.$set(t.item,"name",e)},expression:"item.name"}})],1),o("el-form-item",{attrs:{label:"手机"}},[o("el-input",{attrs:{name:"name",placeholder:"发送短时的时候会用到这个手机"},model:{value:t.item.mobile,callback:function(e){t.$set(t.item,"mobile",e)},expression:"item.mobile"}})],1),o("el-form-item",{attrs:{label:"微信"}},[o("el-input",{attrs:{name:"wechat",placeholder:"微信号"},model:{value:t.item.wechat,callback:function(e){t.$set(t.item,"wechat",e)},expression:"item.wechat"}})],1),o("el-form-item",{attrs:{label:"微信图片"}},[o("el-upload",{attrs:{action:"","list-type":"picture-card","multiple:false":"","show-file-list":!1,"http-request":t.handlesAvatarSuccess,"on-success":function(e,a,o){return t.handleSuccess(e,a,o,1)}}},[t.item.wechat_pic?o("img",{staticStyle:{width:"120px",height:"120px","margin-top":"14px"},attrs:{src:t.item.wechat_pic}}):t._e(),t.item.wechat_pic?t._e():o("img",{staticStyle:{width:"120px",height:"120px","margin-top":"14px"},attrs:{src:a("a137")}})])],1),o("el-form-item",{attrs:{label:"是否分配"}},[o("el-switch",{attrs:{"active-text":"分配","active-value":1,"inactive-text":"不分配","inactive-value":0},model:{value:t.item.is_order,callback:function(e){t.$set(t.item,"is_order",e)},expression:"item.is_order"}})],1),o("el-form-item",{attrs:{label:"是否主播"}},[o("el-switch",{attrs:{"active-text":"主播","active-value":1,"inactive-text":"","inactive-value":0},model:{value:t.item.is_anchor,callback:function(e){t.$set(t.item,"is_anchor",e)},expression:"item.is_anchor"}})],1),t.checkPermission(["franchisee"])?t._e():o("el-form-item",{attrs:{label:"是否加盟商"}},[o("el-switch",{attrs:{"active-text":"加盟商","active-value":1,"inactive-text":"","inactive-value":0},model:{value:t.item.is_franchisee,callback:function(e){t.$set(t.item,"is_franchisee",e)},expression:"item.is_franchisee"}})],1),o("el-form-item",{attrs:{label:"是否私域"}},[o("el-switch",{attrs:{"active-text":"私域","active-value":1,"inactive-text":"","inactive-value":0},model:{value:t.item.is_xs,callback:function(e){t.$set(t.item,"is_xs",e)},expression:"item.is_xs"}})],1),o("el-form-item",{attrs:{label:"抖音昵称"}},[o("el-input",{attrs:{name:"dy_nickname",placeholder:"抖音昵称"},model:{value:t.item.dy_nickname,callback:function(e){t.$set(t.item,"dy_nickname",e)},expression:"item.dy_nickname"}})],1),o("el-form-item",{attrs:{label:"重置密码"}},[o("el-switch",{attrs:{"active-value":1,"inactive-text":"","inactive-value":0},model:{value:t.item.reset_passwd,callback:function(e){t.$set(t.item,"reset_passwd",e)},expression:"item.reset_passwd"}})],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSave(t.item)}}},[t._v("保 存")])],1)],1),o("el-dialog",{attrs:{title:"添加排班",visible:t.dialogWork},on:{"update:visible":function(e){t.dialogWork=e}}},[o("el-form",{attrs:{"label-width":"120px",model:t.from}},[o("el-form-item",{attrs:{label:"上班日期"}},[o("el-date-picker",{staticStyle:{"margin-right":"10px"},attrs:{type:"dates",placeholder:"选择一个或多个日期"},model:{value:t.from.dates,callback:function(e){t.$set(t.from,"dates",e)},expression:"from.dates"}})],1),o("el-form-item",{attrs:{label:"上班时间段"}},[o("el-time-picker",{attrs:{"is-range":"","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围"},model:{value:t.from.time,callback:function(e){t.$set(t.from,"time",e)},expression:"from.time"}})],1),o("el-form-item",{attrs:{label:"渠道"}},[o("el-checkbox-group",{model:{value:t.from.oss,callback:function(e){t.$set(t.from,"oss",e)},expression:"from.oss"}},t._l(t.oss,(function(e,a,i){return o("el-checkbox",{key:i,attrs:{label:a}},[t._v(t._s(e))])})),1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onWork()}}},[t._v("保 存")])],1)],1),o("el-dialog",{attrs:{title:"添加线路",visible:t.isAddRouters,width:"30%"},on:{"update:visible":function(e){t.isAddRouters=e}}},[o("el-form",{ref:"AddRoutersForm",attrs:{rules:t.addRoutes,model:t.AddRoutersForm,"label-width":"80px"}},[o("el-form-item",{attrs:{label:"平台",prop:"os"}},[o("el-select",{attrs:{placeholder:"请选择平台"},model:{value:t.AddRoutersForm.os,callback:function(e){t.$set(t.AddRoutersForm,"os",e)},expression:"AddRoutersForm.os"}},t._l(t.platformList,(function(t){return o("el-option",{attrs:{label:t.os,value:t.id}})})),1)],1),o("el-form-item",{attrs:{label:"线路名称",prop:"product_name"}},[o("el-input",{attrs:{placeholder:"请输入线路名称"},model:{value:t.AddRoutersForm.product_name,callback:function(e){t.$set(t.AddRoutersForm,"product_name",e)},expression:"AddRoutersForm.product_name"}})],1),o("el-form-item",{attrs:{label:"线路id",prop:"third_product_id"}},[o("el-input",{attrs:{placeholder:"请输入线路id"},model:{value:t.AddRoutersForm.third_product_id,callback:function(e){t.$set(t.AddRoutersForm,"third_product_id",e)},expression:"AddRoutersForm.third_product_id"}})],1)],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.isAddRouters=!1}}},[t._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:t.handleAddRouters}},[t._v("确 定")])],1)],1),o("el-dialog",{attrs:{title:"更新线路",visible:t.dialogRoutes,width:"50%"},on:{"update:visible":function(e){t.dialogRoutes=e}}},[o("el-form",{attrs:{model:t.routesItem,"label-width":"120px"}},[o("el-form-item",{attrs:{label:"管理员"}},[o("span",[t._v(t._s(t.routesItem.username)+" ("+t._s(t.routesItem.name)+")")])]),o("el-form-item",{attrs:{label:"线路"}},[o("el-select",{attrs:{filterable:"",multiple:"",placeholder:"请选择"},model:{value:t.routesItem.product_ids,callback:function(e){t.$set(t.routesItem,"product_ids",e)},expression:"routesItem.product_ids"}},t._l(t.options,(function(t){return o("el-option",{key:t.id,attrs:{label:t.product_name,value:t.id}})})),1)],1),o("el-form-item",{attrs:{label:"私域线路"}},[o("el-select",{attrs:{filterable:"",multiple:"",placeholder:"请选择私域线路"},model:{value:t.routesItem.product_xs_ids,callback:function(e){t.$set(t.routesItem,"product_xs_ids",e)},expression:"routesItem.product_xs_ids"}},t._l(t.optionsXs,(function(t){return o("el-option",{key:t.id,attrs:{label:t.product_name,value:t.id}})})),1)],1),o("el-form-item",{attrs:{label:"OTA线路分组"}},[o("el-select",{attrs:{filterable:"",multiple:"",placeholder:"请选择OTA线路分组"},model:{value:t.routesItem.route_group_ids,callback:function(e){t.$set(t.routesItem,"route_group_ids",e)},expression:"routesItem.route_group_ids"}},t._l(t.routeGroups,(function(t){return o("el-option",{key:t.id,attrs:{label:t.group_name,value:t.id}})})),1),o("div",{staticStyle:{color:"#909399","font-size":"12px","margin-top":"5px"}},[t._v(" 选择线路分组后，该管理员将获得分组中所有线路的权限 ")])],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.dialogRoutes=!1}}},[t._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:t.saveRoutes}},[t._v("保 存")])],1)],1),o("el-dialog",{attrs:{title:"操作日志",visible:t.operationLogDialog,width:"80%"},on:{"update:visible":function(e){t.operationLogDialog=e}}},[o("div",{staticClass:"operation-log-container"},[o("div",{staticClass:"filter-container"},[o("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.logQuery}},[o("el-form-item",{attrs:{label:"操作类型"}},[o("el-select",{attrs:{placeholder:"请选择操作类型",clearable:""},model:{value:t.logQuery.operation_type,callback:function(e){t.$set(t.logQuery,"operation_type",e)},expression:"logQuery.operation_type"}},[o("el-option",{attrs:{label:"创建",value:"create"}}),o("el-option",{attrs:{label:"更新",value:"update"}}),o("el-option",{attrs:{label:"启用",value:"enable"}}),o("el-option",{attrs:{label:"禁用",value:"disable"}}),o("el-option",{attrs:{label:"重置密码",value:"reset_password"}})],1)],1),o("el-form-item",{attrs:{label:"时间范围"}},[o("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:t.logDateRange,callback:function(e){t.logDateRange=e},expression:"logDateRange"}})],1),o("el-form-item",[o("el-button",{attrs:{type:"primary"},on:{click:t.getOperationLogs}},[t._v("搜索")]),o("el-button",{on:{click:t.resetLogFilter}},[t._v("重置")])],1)],1)],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.logLoading,expression:"logLoading"}],staticStyle:{width:"100%"},attrs:{data:t.operationLogs,border:"",fit:"","highlight-current-row":""}},[o("el-table-column",{attrs:{label:"操作时间",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.parseTime(1e3*e.row.operation_time,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")]}}])}),o("el-table-column",{attrs:{label:"操作者",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.operator&&e.row.operator.name||"--")+" ")]}}])}),o("el-table-column",{attrs:{label:"操作类型",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-tag",{attrs:{type:t.getOperationTypeTag(e.row.operation_type)}},[t._v(" "+t._s(e.row.operation_type_name)+" ")])]}}])}),o("el-table-column",{attrs:{label:"变更内容","min-width":"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.field_changes&&Object.keys(e.row.field_changes).length>0?o("div",[t._l(t.getLimitedChanges(e.row.field_changes),(function(e,a,i){return o("div",{key:a,staticClass:"change-item"},[o("span",{staticClass:"field-label"},[t._v(t._s(e.label)+":")]),o("span",{staticClass:"old-value"},[t._v(t._s(e.old_value||"--"))]),o("span",{staticClass:"arrow"},[t._v(" → ")]),o("span",{staticClass:"new-value"},[t._v(t._s(e.new_value||"--"))])])})),Object.keys(e.row.field_changes).length>2?o("div",{staticClass:"more-changes"},[o("el-button",{staticStyle:{color:"#409EFF",padding:"0"},attrs:{type:"text",size:"mini"},on:{click:function(a){return t.handleLogDetail(e.row)}}},[t._v(" 还有 "+t._s(Object.keys(e.row.field_changes).length-2)+" 项变更，查看详情 ")])],1):t._e()],2):o("span",{staticClass:"text-muted"},[t._v(t._s(e.row.remark||"--"))])]}}])}),o("el-table-column",{attrs:{label:"IP地址",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.ip_address||"--")+" ")]}}])}),o("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(a){return t.handleLogDetail(e.row)}}},[t._v(" 详情 ")])]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:t.logTotal>0,expression:"logTotal > 0"}],attrs:{total:t.logTotal,page:t.logQuery.page,limit:t.logQuery.limit},on:{"update:page":function(e){return t.$set(t.logQuery,"page",e)},"update:limit":function(e){return t.$set(t.logQuery,"limit",e)},pagination:t.getOperationLogs}})],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.operationLogDialog=!1}}},[t._v("关闭")])],1)]),o("el-dialog",{attrs:{title:"操作日志详情",visible:t.logDetailDialog,width:"60%"},on:{"update:visible":function(e){t.logDetailDialog=e}}},[t.currentLogRecord?o("div",[o("el-card",{staticClass:"detail-card"},[o("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[o("span",[t._v("基本信息")])]),o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:12}},[o("div",{staticClass:"detail-item"},[o("span",{staticClass:"detail-label"},[t._v("操作时间：")]),o("span",{staticClass:"detail-value"},[t._v(" "+t._s(t.parseTime(1e3*t.currentLogRecord.operation_time,"{y}-{m}-{d} {h}:{i}:{s}"))+" ")])])]),o("el-col",{attrs:{span:12}},[o("div",{staticClass:"detail-item"},[o("span",{staticClass:"detail-label"},[t._v("操作类型：")]),o("span",{staticClass:"detail-value"},[t._v(t._s(t.currentLogRecord.operation_type_name))])])])],1),o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:12}},[o("div",{staticClass:"detail-item"},[o("span",{staticClass:"detail-label"},[t._v("操作者：")]),o("span",{staticClass:"detail-value"},[t._v(" "+t._s(t.currentLogRecord.operator&&t.currentLogRecord.operator.name||"--")+" ("+t._s(t.currentLogRecord.operator&&t.currentLogRecord.operator.username||"--")+") ")])])]),o("el-col",{attrs:{span:12}},[o("div",{staticClass:"detail-item"},[o("span",{staticClass:"detail-label"},[t._v("IP地址：")]),o("span",{staticClass:"detail-value"},[t._v(t._s(t.currentLogRecord.ip_address||"--"))])])])],1),o("el-row",[o("el-col",{attrs:{span:24}},[o("div",{staticClass:"detail-item"},[o("span",{staticClass:"detail-label"},[t._v("备注：")]),o("span",{staticClass:"detail-value"},[t._v(t._s(t.currentLogRecord.remark||"--"))])])])],1)],1),t.currentLogRecord.field_changes&&Object.keys(t.currentLogRecord.field_changes).length>0?o("el-card",{staticClass:"detail-card",staticStyle:{"margin-top":"15px"}},[o("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[o("span",[t._v("变更详情")])]),o("el-table",{attrs:{data:t.getFormattedChanges(t.currentLogRecord.field_changes),border:""}},[o("el-table-column",{attrs:{label:"字段",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.label)+" ")]}}],null,!1,3382659611)}),o("el-table-column",{attrs:{label:"变更前",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.old_value||"--")+" ")]}}],null,!1,2948110094)}),o("el-table-column",{attrs:{label:"变更后",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.new_value||"--")+" ")]}}],null,!1,3423505013)})],1)],1):t._e()],1):t._e(),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.logDetailDialog=!1}}},[t._v("关闭")])],1)])],1)},i=[],n=a("d09a"),r=a("0fc4"),s=a("7921"),l=(a("e168"),a("e224"),a("4cc3"),a("452e"),a("374d"),a("5227"),a("90c8"),a("bf58"),a("333d")),c=a("50fc"),u=a("b775");function d(t){return Object(u["a"])({url:"/admin/admin-operation-log/index",method:"get",params:t})}function p(t){return Object(u["a"])({url:"/admin/admin-operation-log/show",method:"get",params:t})}var m=a("3cef"),f=a("5f87"),g=a("ed08"),h=a("e350"),b={name:"Adminlist",components:{Pagination:l["a"]},data:function(){return{list:[],ids:"",oss:[],platformList:[],isAddRouters:!1,AddRoutersForm:{third_product_id:"",product_name:"",os:""},from:{oss:[]},total:0,listLoading:!0,listQuery:{page:1,limit:10},dialogVisible:!1,dialogWork:!1,item:{btn:[],product_ids:[],product_xs_ids:[]},route_type:"",options:[],optionsXs:[],routeGroups:[],addRoutes:{third_product_id:[{required:!0,message:"请输入线路id",trigger:"blur"}],os:[{required:!0,message:"请选择平台",trigger:"change"}],product_name:[{required:!0,message:"请输入线路名称",trigger:"blur"}]},dialogRoutes:!1,routesItem:{id:null,username:"",name:"",product_ids:[],product_xs_ids:[],route_group_ids:[]},operationLogDialog:!1,logDetailDialog:!1,currentAdmin:null,currentLogRecord:null,operationLogs:[],logTotal:0,logLoading:!1,logDateRange:[],logQuery:{page:1,limit:20,admin_id:"",operation_type:"",start_time:"",end_time:""}}},created:function(){this.getList()},methods:{parseTime:g["d"],checkPermission:h["a"],getList:function(){var t=this;this.$axios.get("/admin/admin/index",{params:this.listQuery}).then((function(e){t.list=e.data.data,t.total=e.data.total,t.oss=e.ext.oss,console.log(t.oss),t.listLoading=!1})).catch((function(t){}))},checkIfUrlContainsImage:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=[".jpg",".jpeg",".png",".gif",".bmp",".svg",".webp"];return e.some((function(e){return t.toLowerCase().endsWith(e)}))},handleAddRoutes:function(){this.isAddRouters=!0},handlesAvatarSuccess:function(t){var e=this;return Object(s["a"])(Object(r["a"])().mark((function a(){var o,i;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,o=new FormData,o.append("file",t.file),e.upLoading=!0,e,a.next=7,e.$axios.post("/admin/upload/index",o,{headers:{"Content-type":"multipart/form-data","X-Token":Object(f["a"])()}});case 7:i=a.sent,e.item.wechat_pic="".concat(window.location.protocol,"//").concat(window.location.host).concat(i.data),t.onSuccess(i),a.next=15;break;case 12:a.prev=12,a.t0=a["catch"](0),console.error("error:",a.t0);case 15:case"end":return a.stop()}}),a,null,[[0,12]])})))()},handleSuccess:function(t,e,a){console.log(t,e,a),t.data&&(this.item.wechat_pic="".concat(window.location.protocol,"//").concat(window.location.host).concat(t.data))},handleAddRouters:function(){var t=this;this.$refs["AddRoutersForm"].validate(function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(a){var o,i;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a){e.next=14;break}return e.next=3,Object(c["b"])(t.AddRoutersForm);case 3:if(o=e.sent,t.isAddRouters=!1,"ok"!=o.msg){e.next=12;break}return t.$message({message:"线路添加成功",type:"success"}),e.next=9,Object(c["f"])();case 9:i=e.sent,t.options=i.data.data,t.platformList=i.ext.oss;case 12:e.next=15;break;case 14:return e.abrupt("return",!1);case 15:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},setStatus:function(t){var e=this;console.log(t),this.$axios.post("/admin/admin/disabled",t).then((function(t){e.$notify({title:"成功",message:"修改成功",type:"success",duration:2e3})})).catch((function(t){e.$notify({title:"成功",message:"修改失败",type:"error",duration:2e3})}))},handleCreate:function(t){var e=this;return Object(s["a"])(Object(r["a"])().mark((function a(){return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:e.dialogVisible=!0,t.reset_passwd=0,e.item=Object(n["a"])({},t);case 3:case"end":return a.stop()}}),a)})))()},onSave:function(t){var e=this;this.$axios.post("/admin/admin/save",Object(n["a"])({},t)).then((function(t){e.$message({message:"成功",type:"success"}),e.item={},e.dialogVisible=!1,e.getList()})).catch((function(t){}))},onWork:function(t){var e=this;if(t)return this.item=t,this.from={oss:[]},void(this.dialogWork=!0);this.from.admin_id=this.item.id,this.$axios.post("/admin/work/saves",this.from).then((function(t){e.item={},e.dialogWork=!1})).catch((function(t){}))},setTime:function(t){console.log(t)},handleUpdateRoutes:function(t){var e=this;return Object(s["a"])(Object(r["a"])().mark((function a(){var o,i;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(c["f"])();case 2:return o=a.sent,a.next=5,Object(c["g"])();case 5:return i=a.sent,a.next=8,e.getRouteGroups();case 8:e.options=o.data.data,e.optionsXs=i.data.data,e.routesItem={id:t.id,username:t.username,name:t.name,product_ids:t.product_ids?t.product_ids.split(",").map((function(t){return Number(t)})):[],product_xs_ids:t.product_xs_ids?t.product_xs_ids.split(",").map((function(t){return Number(t)})):[],route_group_ids:t.route_group_ids?t.route_group_ids.split(",").map((function(t){return Number(t)})):[]},e.dialogRoutes=!0;case 12:case"end":return a.stop()}}),a)})))()},getRouteGroups:function(){var t=this;return Object(s["a"])(Object(r["a"])().mark((function e(){var a;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(m["b"])();case 3:a=e.sent,t.routeGroups=a.data||[],e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](0),console.error("获取线路组失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})))()},saveRoutes:function(){var t=this,e={id:this.routesItem.id,product_ids:this.routesItem.product_ids.join(),product_xs_ids:this.routesItem.product_xs_ids.join(),route_group_ids:this.routesItem.route_group_ids.join()};this.$axios.post("/admin/admin/save",e).then((function(e){t.$message({message:"线路更新成功",type:"success"}),t.dialogRoutes=!1,t.getList()})).catch((function(e){t.$message.error("线路更新失败")}))},handleViewLogs:function(t){this.currentAdmin=t,this.logQuery.admin_id=t.id,this.operationLogDialog=!0,this.getOperationLogs()},getOperationLogs:function(){var t=this;this.logLoading=!0;var e=Object(n["a"])({},this.logQuery);this.logDateRange&&2===this.logDateRange.length&&(e.start_time=this.logDateRange[0],e.end_time=this.logDateRange[1]),d(e).then((function(e){t.operationLogs=e.data.data||[],t.logTotal=e.data.total||0,t.logLoading=!1})).catch((function(e){console.error("获取操作日志失败:",e),t.logLoading=!1}))},resetLogFilter:function(){this.logQuery={page:1,limit:20,admin_id:this.currentAdmin?this.currentAdmin.id:"",operation_type:"",start_time:"",end_time:""},this.logDateRange=[],this.getOperationLogs()},handleLogDetail:function(t){var e=this;p({id:t.id}).then((function(t){e.currentLogRecord=t.data,e.logDetailDialog=!0})).catch((function(t){console.error("获取日志详情失败:",t),e.$message.error("获取日志详情失败")}))},getOperationTypeTag:function(t){var e={create:"success",update:"primary",enable:"success",disable:"warning",reset_password:"danger"};return e[t]||"info"},getFormattedChanges:function(t){return Object.keys(t).map((function(e){return{field:e,label:t[e].label,old_value:t[e].old_value,new_value:t[e].new_value}}))},getLimitedChanges:function(t){for(var e={},a=Object.keys(t),o=0;o<Math.min(2,a.length);o++){var i=a[o];e[i]=t[i]}return e}}},_=b,v=(a("d70a"),a("8a34")),w=Object(v["a"])(_,o,i,!1,null,"578ea199",null);e["default"]=w.exports},"2cbf":function(t,e,a){"use strict";a("bac3")},"333d":function(t,e,a){"use strict";var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[a("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},i=[],n=(a("374d"),a("09f4")),r={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(n["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(n["a"])(0,800)}}},s=r,l=(a("2cbf"),a("8a34")),c=Object(l["a"])(s,o,i,!1,null,"6af373ef",null);e["a"]=c.exports},"3cef":function(t,e,a){"use strict";a.d(e,"d",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"g",(function(){return r})),a.d(e,"a",(function(){return s})),a.d(e,"h",(function(){return l})),a.d(e,"b",(function(){return c})),a.d(e,"f",(function(){return u})),a.d(e,"e",(function(){return d}));var o=a("b775");function i(t){return Object(o["a"])({url:"/admin/ota-route-group/index",method:"get",params:t})}function n(t){return Object(o["a"])({url:"/admin/ota-route-group/show",method:"get",params:t})}function r(t){return Object(o["a"])({url:"/admin/ota-route-group/save",method:"post",data:t})}function s(t){return Object(o["a"])({url:"/admin/ota-route-group/delete",method:"post",data:t})}function l(t){return Object(o["a"])({url:"/admin/ota-route-group/updateStatus",method:"post",data:t})}function c(){return Object(o["a"])({url:"/admin/ota-route-group/getActiveGroups",method:"get"})}function u(){return Object(o["a"])({url:"/admin/ota-route-group/getProducts",method:"get"})}function d(t){return Object(o["a"])({url:"/admin/ota-route-group/logs",method:"get",params:t})}},"50fc":function(t,e,a){"use strict";a.d(e,"f",(function(){return i})),a.d(e,"b",(function(){return n})),a.d(e,"h",(function(){return r})),a.d(e,"k",(function(){return s})),a.d(e,"e",(function(){return l})),a.d(e,"a",(function(){return c})),a.d(e,"j",(function(){return u})),a.d(e,"m",(function(){return d})),a.d(e,"g",(function(){return p})),a.d(e,"i",(function(){return m})),a.d(e,"l",(function(){return f})),a.d(e,"d",(function(){return g})),a.d(e,"n",(function(){return h})),a.d(e,"c",(function(){return b}));a("e168");var o=a("b775");function i(){return Object(o["a"])({url:"admin/products/list",method:"get"})}function n(t){return Object(o["a"])({url:"/admin/products/add",method:"post",data:t})}function r(t){var e,a;return Object(o["a"])({url:"/admin/products/list?page=".concat(t.page,"&limit=").concat(t.limit,"&product_name=").concat(null!==(e=t.product_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(a=t.third_product_id)&&void 0!==a?a:""),method:"get"})}function s(t){return Object(o["a"])({url:"/admin/products/add",method:"post",data:t})}function l(t){return Object(o["a"])({url:"/admin/products/productschedules?id=".concat(t.id,"&date=").concat(t.date),method:"get"})}function c(t){return Object(o["a"])({url:"/admin/products/addproductschedules",method:"post",data:t})}function u(t){var e,a;return Object(o["a"])({url:"/admin/routes/list?page=".concat(t.page,"&limit=").concat(t.limit,"&route_name=").concat(null!==(e=t.route_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(a=t.third_product_id)&&void 0!==a?a:""),method:"get"})}function d(t){return Object(o["a"])({url:"/admin/routes/add",method:"post",data:t})}function p(){return Object(o["a"])({url:"admin/products-xs/list",method:"get"})}function m(t){var e,a;return Object(o["a"])({url:"/admin/products-xs/list?page=".concat(t.page,"&limit=").concat(t.limit,"&product_name=").concat(null!==(e=t.product_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(a=t.third_product_id)&&void 0!==a?a:""),method:"get"})}function f(t){return Object(o["a"])({url:"/admin/products-xs/add",method:"post",data:t})}function g(){return Object(o["a"])({url:"/admin/setting/getContractSetting",method:"get"})}function h(t){return Object(o["a"])({url:"/admin/setting/savecontractsetting",method:"post",data:t})}function b(t){return Object(o["a"])({url:"/admin/admin/index",method:"get",params:t})}},5201:function(t,e,a){},a137:function(t,e,a){t.exports=a.p+"static/img/wechat.5e3ebdac.png"},bac3:function(t,e,a){},bf58:function(t,e,a){"use strict";var o=a("38e0"),i=a("eacb").f,n=a("ba98"),r=a("483d"),s=a("cf8d"),l=a("4f10e"),c=a("ab27"),u="".endsWith,d=Math.min,p=l("endsWith"),m=!c&&!p&&!!function(){var t=i(String.prototype,"endsWith");return t&&!t.writable}();o({target:"String",proto:!0,forced:!m&&!p},{endsWith:function(t){var e=String(s(this));r(t);var a=arguments.length>1?arguments[1]:void 0,o=n(e.length),i=void 0===a?o:d(n(a),o),l=String(t);return u?u.call(e,l,i):e.slice(i-l.length,i)===l}})},d70a:function(t,e,a){"use strict";a("5201")},e350:function(t,e,a){"use strict";a.d(e,"a",(function(){return i}));a("d987"),a("90c8"),a("6de1");var o=a("4360");function i(t){if(t&&t instanceof Array&&t.length>0){var e=o["a"].getters&&o["a"].getters.roles,a=t,i=e.some((function(t){return a.includes(t)}));return i}return console.error("need roles! Like v-permission=\"['admin','editor']\""),!1}}}]);