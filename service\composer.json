{"name": "workerman/webman", "type": "project", "keywords": ["high performance", "http service"], "homepage": "https://www.workerman.net", "license": "MIT", "description": "High performance HTTP Service Framework.", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/webman/issues", "forum": "https://wenda.workerman.net/", "wiki": "https://workerman.net/doc/webman", "source": "https://github.com/walkor/webman"}, "require": {"php": ">=7.2", "workerman/webman-framework": "^1.5.0", "monolog/monolog": "^2.0", "webman/think-orm": "^1.1", "firebase/php-jwt": "^6.10", "illuminate/redis": "^10.48", "illuminate/events": "^10.48", "vlucas/phpdotenv": "^5.5", "qiniu/php-sdk": "^7.12", "webman/console": "^1.3", "mk-j/php_xlsxwriter": "^0.39.0", "workerman/crontab": "^1.0", "workerman/validation": "^1.0", "guzzlehttp/guzzle": "^7.9"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"": "./", "app\\": "./app", "App\\": "./app", "app\\View\\Components\\": "./app/view/components"}, "files": ["./support/helpers.php"]}, "scripts": {"post-package-install": ["support\\Plugin::install"], "post-package-update": ["support\\Plugin::install"], "pre-package-uninstall": ["support\\Plugin::uninstall"]}}