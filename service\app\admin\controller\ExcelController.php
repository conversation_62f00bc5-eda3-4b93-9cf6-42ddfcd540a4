<?php
namespace app\admin\controller;

use app\model\Line;
use app\model\Products;
use app\server\OrdersXs;
use app\utils\ExcelUtil;
use support\Request;

class ExcelController extends base
{
    /**
     * 导出Excel示例
     * @param Request $request
     * @return \support\Response
     */
    public function export(Request $request)
    {
        /// 示例数据
        $data = [
            ['北京', '157119562352', 'wx65823414', '重点客户', '广点通', '6'],
        ];

        $products = Products::where('is_private', 1)->where('status', 1)->select()->column('product_name');
        // 定义表头和数据验证
        $header = [
            '私域线路' => ['type' => 'select', 'options' => $products],
            '手机号码' => ['type' => 'string'],
            '微信号' => ['type' => 'string'],
            '等级' => [
                'type' => 'select',
                'options' => array_values(Line::LEVELS_MAP)
            ],
            '来源' => [
                'type' => 'select',
                'options' => array_values(Line::SOURCE_MAP)
            ],
            '备注'  => ['type' => 'string']
        ];

        try {
            $filename = '私域模板导出_' . date('YmdHis') . '.xlsx';
            $filepath = ExcelUtil::writeToExcel($data, $header, $filename);

            $response = response();
            $c = file_get_contents($filepath);
            $response->withHeaders([
                'Content-Type' => 'application/force-download',
                'Content-Disposition' => 'attachment; filename="'.$filename.'"',
                'Content-Transfer-Encoding' => 'binary',
                'Cache-Control' => 'max-age=0',
            ])->withBody($c);
            return $response;
        } catch (\Exception $e) {
            return $this->error(500, $e->getMessage());
        }
    }

    /**
     * 导入Excel示例
     * @param Request $request
     * @return \support\Response
     */
    public function import(Request $request)
    {
        $file = $request->file('file');
        if (!$file || !$file->isValid()) {
            return $this->error(400, '请上传有效的Excel文件');
        }

        $ext = $file->getUploadExtension();
        if (!in_array(strtolower($ext), ['xlsx', 'xls'])) {
            return $this->error(400, '只支持Excel文件格式');
        }

        try {
            // 保存上传的文件
            $uploadDir = public_path() . '/uploads/excel/' . date('Ymd');
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $filename = uniqid() . '.' . $ext;
            $filepath = $uploadDir . '/' . $filename;
            $file->move($filepath);

            // 读取Excel内容
            $data = ExcelUtil::readFromExcel($filepath);

            $faileData = [];
            if ($request->post('type', 1) == 1) {
                 $faileData = OrdersXs::batchImportOrder($request->admin, $data);
            }
            // 删除临时文件
            unlink($filepath);

            $message = '导入成功';
            if (!empty($faileData)) {
                $message = '部分导入成功';
            }
            return $this->success([
                'total' => count($data),
                'data' => [
                    'post_data' => $data,
                    'fail_data' => $faileData
                ]
            ], $message);
        } catch (\Exception $e) {
            return $this->error(500, $e->getMessage(). $e->getFile() . $e->getLine());
        }
    }
}
