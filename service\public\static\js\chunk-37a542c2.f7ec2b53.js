(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-37a542c2"],{"634a":function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("SoftPhone",{ref:"softphone",attrs:{visible:e.softphoneVisible,softphoneInstance:e.moorSoftphone,initialNumber:e.currentPhoneNumber,currentLoginType:e.currentLoginType},on:{close:function(t){e.softphoneVisible=!1},"login-type-change":e.onLoginTypeChange,"reinit-softphone":e.onReinitSoftphone}}),n("div",{staticClass:"filter-container"},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"订单号"},model:{value:e.listQuery.sn,callback:function(t){e.$set(e.listQuery,"sn",t)},expression:"listQuery.sn"}}),n("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"标题"},model:{value:e.listQuery.product_name,callback:function(t){e.$set(e.listQuery,"product_name",t)},expression:"listQuery.product_name"}}),n("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"手机号"},model:{value:e.listQuery.mobile,callback:function(t){e.$set(e.listQuery,"mobile",t)},expression:"listQuery.mobile"}}),n("el-input",{staticClass:"filter-item",staticStyle:{width:"100px"},attrs:{placeholder:"主播"},model:{value:e.listQuery.zhubo,callback:function(t){e.$set(e.listQuery,"zhubo",t)},expression:"listQuery.zhubo"}}),n("el-input",{staticClass:"filter-item",staticStyle:{width:"100px"},attrs:{placeholder:"客服"},model:{value:e.listQuery.admin,callback:function(t){e.$set(e.listQuery,"admin",t)},expression:"listQuery.admin"}}),n("el-cascader",{staticClass:"filter-item",attrs:{placeholder:"平台状态",options:e.oss,clearable:"",props:{checkStrictly:!0}},on:{change:e.handleChange},model:{value:e.listQuery.os_status,callback:function(t){e.$set(e.listQuery,"os_status",t)},expression:"listQuery.os_status"}}),n("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"跟进状态"},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},[n("el-option",{key:"",attrs:{label:"请选择",value:""}}),e._l(e.status_arr,(function(e,t){return n("el-option",{key:t,attrs:{label:e,value:t}})}))],2),n("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"时间"},model:{value:e.listQuery.timetype,callback:function(t){e.$set(e.listQuery,"timetype",t)},expression:"listQuery.timetype"}},[n("el-option",{key:"",attrs:{label:"请选择",value:""}}),e._l(e.timetype_arr,(function(e,t){return n("el-option",{key:t,attrs:{label:e,value:t}})}))],2),n("el-date-picker",{staticClass:"filter-item",attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","default-time":["00:00:00","23:59:59"],"end-placeholder":"结束日期"},model:{value:e.listQuery.times,callback:function(t){e.$set(e.listQuery,"times",t)},expression:"listQuery.times"}}),n("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",clearable:"",placeholder:"预约状态"},model:{value:e.listQuery.appointment_status,callback:function(t){e.$set(e.listQuery,"appointment_status",t)},expression:"listQuery.appointment_status"}},[n("el-option",{key:"0",attrs:{label:"未预约",value:"0"}}),n("el-option",{key:"1",attrs:{label:"已预约(未处理)",value:"1"}}),n("el-option",{key:"2",attrs:{label:"已预约(已处理)",value:"2"}})],1),n("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",clearable:"",placeholder:"待使用"},model:{value:e.listQuery.un_use_type,callback:function(t){e.$set(e.listQuery,"un_use_type",t)},expression:"listQuery.un_use_type"}},[n("el-option",{key:"1",attrs:{label:"30天待使用",value:"1"}}),n("el-option",{key:"2",attrs:{label:"60天待使用",value:"2"}}),n("el-option",{key:"3",attrs:{label:"80天待使用",value:"3"}})],1),n("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.getList}},[e._v(" 搜索 ")]),e.checkPermission(["admin","franchisee"])?n("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.getList(1)}}},[e._v(" 导出 ")]):e._e(),n("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){e.dialog2Visible=!0}}},[e._v(" 核单 ")]),n("el-button",{staticClass:"filter-item",attrs:{type:"primary",disabled:0==e.multipleSelection.length,icon:"el-icon-refresh"},on:{click:function(t){return e.onCirculationAll()}}},[e._v(" 批量流转 ")])],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.list,border:"",fit:"","highlight-current-row":"",height:e.tableMaxHeight},on:{"selection-change":e.handleSelectionChange,"sort-change":e.orderSort}},[n("el-table-column",{attrs:{type:"selection",width:"40"}}),n("el-table-column",{attrs:{align:"center",width:"160",fixed:"",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button-group",[n("el-button",{attrs:{type:t.row.backs&&0==t.row.backs.status?e.types[7]:e.types[4],size:"small",icon:"el-icon-refresh"},on:{click:function(n){return e.onCirculation(t.row)}}},[e._v(" "+e._s(t.row.backs&&0==t.row.backs.status?"流转中":"流转出")+" ")]),n("el-button",{attrs:{type:e.types[t.row.order_status],size:"small",icon:"el-icon-edit"},on:{click:function(n){return e.onInfo(t.row)}}},[e._v(" 跟进 ")]),n("el-button",{attrs:{size:"small",icon:"el-icon-thumb"},on:{click:function(n){return e.onOneClickRepair(t.row,!0)}}},[e._v(" 同步 ")]),t.row.is_direct_mode&&1==t.row.appointment_status?n("el-button",{attrs:{size:"small",icon:"el-icon-thumb"},on:{click:function(n){return e.confirmOrder(t.row)}}},[e._v(" 确认接单 ")]):e._e(),t.row.is_direct_mode&&2==t.row.appointment_status?n("el-button",{attrs:{size:"small",icon:"el-icon-thumb"},on:{click:function(n){return e.confirmOrder(t.row,2)}}},[e._v(" 协助取消/退款 ")]):e._e(),1!=t.row.appointment_status||t.row.is_direct_mode?e._e():n("el-button",{attrs:{size:"small",icon:"el-icon-edit"},on:{click:function(n){return e.onOneClickYyHandle(t.row)}}},[e._v(" 预约处理 ")])],1)]}}])}),n("el-table-column",{attrs:{align:"center",fixed:"",label:"电话",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",[n("el-button",{attrs:{type:"text"},on:{click:function(n){return e.copyToClipboard(t.row.mobile)}}},[e._v(e._s(t.row.mobile))])],1),n("span",{staticStyle:{display:"block","font-size":"8px"}},[e._v(e._s(t.row.mobileInfo.area)+"-"+e._s(t.row.mobileInfo.originalIsp))])]}}])}),n("el-table-column",{attrs:{align:"center",fixed:"",label:"平台",width:"80",prop:"os_name"}}),n("el-table-column",{attrs:{align:"center",fixed:"",label:"直播",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.is_zhibo?n("el-tag",[e._v("是")]):n("el-tag",{attrs:{type:"info"}},[e._v("否")])]}}])}),n("el-table-column",{attrs:{align:"center",fixed:"",label:"客服",width:"80",prop:"admin.username"}}),n("el-table-column",{attrs:{width:"300",align:"center",label:"跟进备注",prop:"remark"}}),n("el-table-column",{attrs:{align:"center",label:"订单号",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{attrs:{type:"text"},on:{click:function(n){return e.copyToClipboard(t.row.sn)}}},[e._v(e._s(t.row.sn))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"总金额",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.total_price/100))])]}}])}),n("el-table-column",{attrs:{align:"center",width:"80",label:"人数",prop:"quantity"}}),n("el-table-column",{attrs:{align:"center",label:"预约状态（抖音）",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticStyle:{padding:"1px 5px","border-radius":"3px"},style:{color:e.order_status[t.row.appointment_status],border:"1px solid "+e.order_status[t.row.appointment_status]},attrs:{type:"primary"}},[e._v(" "+e._s(1==t.row.appointment_status?"已预约(未处理)":2==t.row.appointment_status?"已预约(已处理)":"未预约")+" ")])]}}])}),n("el-table-column",{attrs:{align:"center",label:"状态",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticStyle:{padding:"1px 5px","border-radius":"3px"},style:{color:e.order_status[t.row.order_status],border:"1px solid "+e.order_status[t.row.order_status]},attrs:{type:"primary"}},[e._v(" "+e._s(t.row.order_status_name)+" ")])]}}])}),n("el-table-column",{attrs:{align:"center",label:"跟进状态",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticStyle:{padding:"1px 5px","border-radius":"3px"},style:{color:e.follow_status[t.row.status],border:"1px solid "+e.follow_status[t.row.status]},attrs:{type:"primary"}},[e._v(" "+e._s(t.row.status_name)+" ")])]}}])}),n("el-table-column",{attrs:{align:"center",width:"500px",label:"标题",prop:"product_name"}}),n("el-table-column",{attrs:{sortable:"custom",align:"center",label:"商品ID",width:"90",prop:"product_id"}}),n("el-table-column",{attrs:{sortable:"custom",align:"center",label:"核销日期",width:"90",prop:"verification_date"}}),n("el-table-column",{attrs:{width:"150",align:"center",label:"下单时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.create_at,"{y}-{m}-{d} {h}:{i}")))])]}}])}),n("el-table-column",{attrs:{width:"150",align:"center",label:"派单时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.give_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),n("el-table-column",{attrs:{width:"150",align:"center",label:"出行日期"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.travel_date))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"联系人",width:"120",prop:"contact"}}),n("el-table-column",{attrs:{width:"150",align:"center",label:"出行时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.travel_date,"{y}-{m}-{d}")))])]}}])}),n("el-table-column",{attrs:{width:"150",align:"center",label:"最后跟进时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.last_follow,"{y}-{m}-{d} {h}:{i}")))])]}}])}),n("el-table-column",{attrs:{align:"center",label:"核单",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.is_check?n("i",{staticClass:"el-icon-check"}):e._e(),2==t.row.is_check?n("i",{staticClass:"el-icon-close"}):e._e()]}}])}),n("el-table-column",{attrs:{align:"center",width:"138",label:"分类",prop:"category_desc"}}),n("el-table-column",{attrs:{align:"center",label:"主播",width:"80",prop:"anchor.username"}}),n("el-table-column",{attrs:{width:"138",align:"center",label:"修改时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.update_time,"{y}-{m}-{d} {h}:{i}")))])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){return e.$set(e.listQuery,"page",t)},"update:limit":function(t){return e.$set(e.listQuery,"limit",t)},pagination:e.setMode}}),n("el-dialog",{attrs:{title:"订单跟进",visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("el-form",{attrs:{"label-width":"130px",model:e.item}},[n("el-form-item",{attrs:{label:"产品名称"}},[e._v(" "+e._s(e.item.product_name)+" ")]),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"产品状态"}},[e._v(" "+e._s(e.item.order_status_name)+" ")]),n("el-form-item",{attrs:{label:"数量"}},[e._v(" "+e._s(e.item.quantity)+" ")]),n("el-form-item",{attrs:{label:"联系人"}},[e._v(" "+e._s(e.item.contact)+" ")]),n("el-form-item",{attrs:{label:"手机"}},[e._v(" "+e._s(e.item.mobile)+" ")]),n("el-form-item",{attrs:{label:"下单时间"}},[e._v(" "+e._s(e._f("parseTime")(e.item.create_at,"{y}-{m}-{d} {h}:{i}"))+" ")])],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"人员"}},[n("el-row",[n("el-col",{attrs:{span:3}},[e._v("大人")]),n("el-col",{attrs:{span:5}},[n("el-input",{attrs:{name:"adult",placeholder:"大人"},model:{value:e.item.personnel.adult,callback:function(t){e.$set(e.item.personnel,"adult",t)},expression:"item.personnel.adult"}})],1),n("el-col",{attrs:{span:3}},[e._v("老人")]),n("el-col",{attrs:{span:5}},[n("el-input",{attrs:{name:"old",placeholder:"老人"},model:{value:e.item.personnel.old,callback:function(t){e.$set(e.item.personnel,"old",t)},expression:"item.personnel.old"}})],1),n("el-col",{attrs:{span:3}},[e._v("小孩")]),n("el-col",{attrs:{span:5}},[n("el-input",{attrs:{name:"child",placeholder:"小孩"},model:{value:e.item.personnel.child,callback:function(t){e.$set(e.item.personnel,"child",t)},expression:"item.personnel.child"}})],1)],1)],1),1!==e.item.status?n("el-form-item",{attrs:{label:"核销码"}},[n("el-input",{attrs:{name:"check_sn",placeholder:"请输入平台核销码"},model:{value:e.item.check_sn,callback:function(t){e.$set(e.item,"check_sn",t)},expression:"item.check_sn"}})],1):e._e(),2!==e.item.status?n("el-form-item",{attrs:{label:"加微信"}},[n("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:e.item.is_wechat,callback:function(t){e.$set(e.item,"is_wechat",t)},expression:"item.is_wechat"}},[e._v("已加微信")])],1):e._e(),n("el-form-item",{attrs:{required:"",pros:"travel_date",label:"出游日期"}},[n("el-date-picker",{attrs:{type:"date",placeholder:"选择日期时间"},model:{value:e.item.travel_date,callback:function(t){e.$set(e.item,"travel_date",t)},expression:"item.travel_date"}})],1),1!==e.item.status?n("el-form-item",{attrs:{label:"返回日期"}},[n("el-date-picker",{attrs:{type:"date",placeholder:"选择日期时间"},model:{value:e.item.travel_end,callback:function(t){e.$set(e.item,"travel_end",t)},expression:"item.travel_end"}})],1):e._e(),2!==e.item.status?n("el-form-item",{attrs:{required:"",pros:"next_follow",label:"下次跟进时间"}},[n("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间"},model:{value:e.next_follow,callback:function(t){e.next_follow=t},expression:"next_follow"}})],1):e._e()],1)],1),n("el-form-item",{attrs:{label:"跟进状态"}},[e._l(e.status_arr,(function(t,o){return[o>0?n("el-radio",{attrs:{label:o,border:""},model:{value:e.item.status,callback:function(t){e.$set(e.item,"status",t)},expression:"item.status"}},[e._v(e._s(t))]):e._e()]}))],2),n("el-form-item",{attrs:{required:"",pros:"desc",label:"跟进说明"}},[n("el-input",{attrs:{type:"textarea"},model:{value:e.item.desc,callback:function(t){e.$set(e.item,"desc",t)},expression:"item.desc"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onSave(e.item)}}},[e._v("保 存")])],1),n("el-tabs",{attrs:{type:"border-card"},model:{value:e.active,callback:function(t){e.active=t},expression:"active"}},[n("el-tab-pane",{attrs:{name:"follow",label:"跟进记录"}},[n("el-table",{staticStyle:{width:"100%"},attrs:{data:e.item.follow}},[n("el-table-column",{attrs:{label:"日期",width:"138"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),n("el-table-column",{attrs:{label:"跟进人",width:"110",prop:"admin.username"}}),n("el-table-column",{attrs:{label:"状态",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.status_arr[t.row.status]))])]}}])}),n("el-table-column",{attrs:{prop:"desc",label:"跟进说明"}})],1)],1),n("el-tab-pane",{attrs:{name:"finance",label:"财务记录"}},[n("el-table",{staticStyle:{width:"100%"},attrs:{data:e.item.finance}},[n("el-table-column",{attrs:{label:"日期"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e._f("parseTime")(t.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),n("el-table-column",{attrs:{label:"类型",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.type_arr[t.row.type]))])]}}])}),n("el-table-column",{attrs:{label:"状态",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(t.row.total/100))])]}}])})],1)],1)],1)],1),n("el-dialog",{attrs:{title:e.confirmTitle,visible:e.orderConfirmDialogVisible},on:{"update:visible":function(t){e.orderConfirmDialogVisible=t}}},[n("el-form",{attrs:{"label-width":"130px",model:e.confirmItem}},[n("el-form-item",{attrs:{label:"产品名称"}},[e._v(" "+e._s(e.confirmItem.product_name)+" ")]),n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"产品状态"}},[e._v(" "+e._s(e.confirmItem.order_status_name)+" ")]),n("el-form-item",{attrs:{label:"数量"}},[e._v(" "+e._s(e.confirmItem.quantity)+" ")]),n("el-form-item",{attrs:{label:"手机"}},[e._v(" "+e._s(e.confirmItem.mobile)+" ")]),n("el-form-item",{attrs:{label:"下单时间"}},[e._v(" "+e._s(e._f("parseTime")(e.confirmItem.create_at,"{y}-{m}-{d} {h}:{i}"))+" ")]),n("el-form-item",{attrs:{label:"支付金额"}},[e._v(" "+e._s(e.confirmItem.actual_price/100)+" ")])],1),n("el-col",{attrs:{span:12}},[e.confirmItem.dyOrderAppointments.number_of_guests?n("el-form-item",{attrs:{label:"人员"}},[n("el-row",[n("el-col",{attrs:{span:3}},[e._v("大人")]),n("el-col",{attrs:{span:5}},[n("el-input",{attrs:{name:"adult",placeholder:"大人"},model:{value:e.confirmItem.dyOrderAppointments.number_of_guests.adult,callback:function(t){e.$set(e.confirmItem.dyOrderAppointments.number_of_guests,"adult",t)},expression:"confirmItem.dyOrderAppointments.number_of_guests.adult"}})],1),n("el-col",{attrs:{span:3}},[e._v("小孩")]),n("el-col",{attrs:{span:5}},[n("el-input",{attrs:{name:"child",placeholder:"小孩"},model:{value:e.confirmItem.dyOrderAppointments.number_of_guests.child,callback:function(t){e.$set(e.confirmItem.dyOrderAppointments.number_of_guests,"child",t)},expression:"confirmItem.dyOrderAppointments.number_of_guests.child"}})],1)],1)],1):e._e(),n("el-form-item",{attrs:{label:"出游日期"}},[e._v(" "+e._s(e.confirmItem.dyOrderAppointments.book_info.book_start_date)+" ")]),n("el-form-item",{attrs:{label:"返回日期"}},[e._v(" "+e._s(e.confirmItem.dyOrderAppointments.book_info.book_end_date)+" ")])],1)],1),n("el-form-item",{attrs:{label:"预约详情"}},[e.confirmItem.dyOrderAppointments.book_info.occupancies?n("el-table",{staticStyle:{width:"100%","margin-bottom":"0"},attrs:{data:e.confirmItem.dyOrderAppointments.book_info.occupancies}},[n("el-table-column",{attrs:{prop:"name",label:"出行人",width:"180"}}),n("el-table-column",{attrs:{prop:"license_id",label:"证件号",width:"180"}})],1):e._e()],1)],1),1==e.confirmItem.appointment_status?n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dyOrderConfirm(e.confirmItem,1)}}},[e._v("确认接单")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dyOrderConfirm(e.confirmItem,2)}}},[e._v("拒绝")])],1):e._e(),2==e.confirmItem.appointment_status?n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dyOrderCancel(e.confirmItem,2)}}},[e._v("仅取消预约")]),n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dyOrderCancel(e.confirmItem,1)}}},[e._v("取消预约并全额退款")])],1):e._e()],1),n("el-dialog",{attrs:{title:"纯核销",visible:e.dialog2Visible},on:{"update:visible":function(t){e.dialog2Visible=t}}},[n("el-form",{attrs:{"label-width":"160px",model:e.form}},[n("el-form-item",{attrs:{label:"平台"}},[n("el-radio",{attrs:{label:"1"},model:{value:e.form.os,callback:function(t){e.$set(e.form,"os",t)},expression:"form.os"}},[e._v("美团")])],1),n("el-form-item",{attrs:{label:"核销码"}},[n("el-input",{attrs:{placeholder:"请输入平台核销码"},model:{value:e.form.check_sn,callback:function(t){e.$set(e.form,"check_sn",t)},expression:"form.check_sn"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onPass(e.form)}}},[e._v("保 存")])],1)],1),n("el-dialog",{attrs:{title:"申请转出订单",visible:e.applyVisible},on:{"update:visible":function(t){e.applyVisible=t}}},[n("el-form",{ref:"ruleForm",attrs:{"label-width":"160px",model:e.item3,rules:e.rules}},[e.isAll?e._e():n("el-form-item",{attrs:{label:"标题:"}},[n("el-input",{attrs:{disabled:""},model:{value:e.item3.product_name,callback:function(t){e.$set(e.item3,"product_name",t)},expression:"item3.product_name"}})],1),e.isAll?e._e():n("el-form-item",{attrs:{label:"订单号:"}},[n("el-input",{attrs:{disabled:""},model:{value:e.item3.sn,callback:function(t){e.$set(e.item3,"sn",t)},expression:"item3.sn"}})],1),n("el-form-item",{staticStyle:{width:"600px"},attrs:{label:"流转对象:",prop:"flowObj"}},[n("el-select",{attrs:{placeholder:"请选择"},on:{change:e.onChange2},model:{value:e.item3.flowObj,callback:function(t){e.$set(e.item3,"flowObj",t)},expression:"item3.flowObj"}},[n("el-form-item",{staticStyle:{display:"inline-flex","text-align":"left",width:"770px"}},e._l(e.adminList,(function(e){return n("el-option",{key:e.value,staticStyle:{width:"250px",display:"inline-flex","word-break":"break-all"},attrs:{label:e.username,value:e.id}})})),1)],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.item3.backs&&0==e.item3.backs.status?n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onCancel(e.item3.flowObj)}}},[e._v("取 消")]):n("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onCirculationSave(e.item3.flowObj)}}},[e._v("确 认")])],1)],1)],1)},i=[],a=n("d00a"),r=n("d09a"),l=n("0fc4"),s=n("7921"),c=(n("3dd5"),n("e224"),n("4cc3"),n("374d"),n("5227"),n("90c8"),n("67f2")),u=n("f8b7"),m=n("e350"),d=n("5472"),p=n.n(d),f=n("ca9b"),b={name:"Orderlist",components:{Pagination:c["a"],SoftPhone:f["a"]},data:function(){return{active:"follow",types:{0:"",1:"",2:"",3:"primary",4:"success",5:"warning",6:"danger",7:"info"},types2:{1:"primary",2:"success",3:"warning"},status_arr:["待跟进","跟进中","已核销","核销失败","放弃跟单","加入公海"],type_arr:["-","收益","支出"],timetype_arr:{},order_status:["#9e9f9c","#04bcd9","#fc9904","#1193f4","#48b14b","#eb1662","#9d1cb5"],follow_status:["#9e9f9c","#04bcd9","#fc9904","#1193f4","#48b14b","#eb1662"],options:[],value:null,next_follow:null,list:[],total:0,listLoading:!0,listQuery:{page:1,limit:10,times:[],status:null,admin:null,zhubo:null,os_status:[],appointment_status:"",un_use_type:""},item:{next_follow:"",personnel:{}},confirmItem:{next_follow:"",personnel:{adult:""},dyOrderAppointments:{book_info:{},number_of_guests:{}}},follow:[],dialogVisible:!1,dialog2Visible:!1,applyVisible:!1,notice:!1,orderConfirmDialogVisible:!1,oss:[],isSynchronization:!1,item3:{sn:null,backs:null,flowObj:"",os:null},multipleSelection:[],sn:[],adminList:[],form:{},isAll:!1,rules:{flowObj:[{required:!0,message:"请选择流转对象",trigger:"change"}]},currentSort:{prop:null,order:null},confirmTitle:"确认接单",moorSoftphone:null,softphoneVisible:!1,currentPhoneNumber:"",currentLoginType:"Local"}},created:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.listQuery.zhubo=e.$route.query.zhubo||null,e.$route.query.start&&e.$route.query.end&&(e.listQuery.times=[e.$route.query.start,e.$route.query.end]),e.setQuery("status"),e.setQuery("os_status"),e.setQuery("times"),e.setQuery("appointment_status"),e.getShortcutContent(),e.getAdminList();case 8:case"end":return t.stop()}}),t)})))()},mounted:function(){this.setMode(),this.$route.query.id&&this.onInfo({id:this.$route.query.id})},computed:{tableMaxHeight:function(){return window.innerHeight-320+"px"}},watch:{$route:function(e,t){this.onInfo({id:this.$route.query.id})}},methods:{checkPermission:m["a"],handleSelectionChange:function(e){this.multipleSelection=e;var t=[];this.multipleSelection.map((function(e){t.push(e.sn)})),this.sn=t},setQuery:function(e){this.$route.query.hasOwnProperty(e)?this.listQuery[e]=this.$route.query[e]:this.listQuery[e]=""},setMode:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getList();case 2:case"end":return t.stop()}}),t)})))()},setOneClickRepair:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){var n;return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n=e.list.map((function(e){return e.id})),t.next=3,e.onOneClickRepair({id:n.join()});case 3:case"end":return t.stop()}}),t)})))()},orderSort:function(e){var t=e.column,n=e.prop,o=e.order;"ascending"==t.order&&(this.listQuery.order_by="verify_date_asc"),"descending"==t.order&&(this.listQuery.order_by="verify_date_desc"),this.currentSort.prop!==n?this.currentSort={prop:n,order:o}:this.currentSort.order="ascending"===this.currentSort.order?"descending":"ascending",console.log({column:t,prop:n,order:o}),this.getList()},getList:function(e){var t=arguments,n=this;return Object(s["a"])(Object(l["a"])().mark((function o(){var i,a,s;return Object(l["a"])().wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(i=t.length>1&&void 0!==t[1]?t[1]:1,n.listQuery.excel=null,1!=e){o.next=8;break}return n.listQuery.excel=1,a=n.listQuery.times[0]instanceof Date,s=Object(r["a"])(Object(r["a"])({},n.listQuery),{},{times:[a?n.listQuery.times[0].toISOString():"",a?n.listQuery.times[1].toISOString():""]}),window.open("/admin/order/index?"+n.objectToQuery(s)),o.abrupt("return");case 8:return o.next=10,n.$axios.get("/admin/order/index",{params:n.listQuery}).then((function(e){n.list=e.data.data,n.total=e.data.total,n.timetype_arr=e.ext.timetype,n.oss=e.ext.oss,n.listLoading=!1,n.isSynchronization=!0}));case 10:if(1!=i){o.next=13;break}return o.next=13,n.setOneClickRepair();case 13:case"end":return o.stop()}}),o)})))()},objectToQuery:function(e){return Object.keys(e).map((function(t){var n=e[t];return void 0==n||null==n?"":encodeURIComponent(t)+"="+encodeURIComponent(n)})).join("&")},onInfo:function(e){var t=this;this.value=null,this.next_follow=null,this.$set(e,"next_follow",null),this.active="follow",this.$axios.get("/admin/order/info",{params:{id:e.id}}).then((function(e){t.item=e.data,t.dialogVisible=!0})).catch((function(e){}))},confirmOrder:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;1==n&&(this.confirmTitle="确认接单"),2==n&&(this.confirmTitle="协助取消/退款"),this.$axios.get("/admin/order/info",{params:{id:e.id}}).then((function(e){t.confirmItem=e.data,t.orderConfirmDialogVisible=!0})).catch((function(e){}))},resetForm:function(e){this.$refs[e].resetFields()},getAdminList:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.$axios.get("/admin/admin/index",{params:{limit:100,status:1,is_order:1,type_desc:t}}).then((function(t){e.adminList=t.data.data,e.listLoading=!1})).catch((function(e){}))},onCirculation:function(e){this.getAdminList(e.category_desc),this.applyVisible=!0,this.isAll=!1,this.item3=Object(r["a"])(Object(r["a"])({},e),{},{os:Number(e.os)}),console.log(this.item3),this.item3.backs&&this.item3.backs.admin_id?this.item3.flowObj=this.item3.backs.admin_id:this.resetForm("ruleForm")},onCirculationAll:function(){this.applyVisible=!0,this.isAll=!0},onCirculationSave:function(e){var t=this;this.$refs.ruleForm.validate(function(){var n=Object(s["a"])(Object(l["a"])().mark((function n(o){var i;return Object(l["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!o){n.next=11;break}if(!t.isAll){n.next=5;break}t.$confirm("是否批量流转订单","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(s["a"])(Object(l["a"])().mark((function n(){return Object(l["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,Object(u["c"])({sn:t.sn,to_admin_id:e});case 2:i=n.sent,i.data&&(t.$message({message:"批量流转订单成功",type:"success"}),t.applyVisible=!1,t.isAll=!1,t.getList());case 4:case"end":return n.stop()}}),n)})))),n.next=9;break;case 5:return n.next=7,Object(u["a"])({sn:t.item3.sn,os:t.item3.os,to_admin_id:e});case 7:i=n.sent,i.data&&(t.$message({message:"流转订单成功",type:"success"}),t.applyVisible=!1,t.isAll=!1,t.getList());case 9:n.next=12;break;case 11:return n.abrupt("return",!1);case 12:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}())},onCancel:function(){var e=this;this.$refs.ruleForm.validate((function(t){if(!t)return!1;e.$axios.post("/admin/order/backcancel",{id:e.item3.id}).then((function(t){e.applyVisible=!1,e.isAll=!1,e.getList()})).catch((function(e){console.log(e)}))}))},onBack:function(){var e=this;this.$axios.post("/admin/order/back",this.item).then((function(t){e.dialogVisible=!1,e.item={},e.getList()})).catch((function(e){}))},onSave:function(e){var t=this;this.$axios.post("/admin/order/save",{id:e.id,check_sn:e.check_sn,is_wechat:e.is_wechat,travel_end:e.travel_end,travel_date:e.travel_date,desc:e.desc,status:e.status,next_follow:this.next_follow,personnel:this.item.personnel}).then((function(e){t.dialogVisible=!1,t.item={next_follow:"",personnel:{adult:""}},t.$router.push({path:"/order/index"})})).catch((function(e){}))},onPass:function(e){var t=this;this.$axios.post("/admin/order/pass",{check_sn:e.check_sn}).then((function(e){t.dialog2Visible=!1,t.form={}})).catch((function(e){}))},onChange:function(e){this.$set(this.item,"desc",e+(void 0!=this.item.desc?this.item.desc:""))},onChange2:function(e){this.$set(this.item,"to_admin_id",e+(void 0!=this.item.admin_id?this.item.admin_id:""))},handleChange:function(e){console.log(e)},getShortcutContent:function(){var e=this;this.listLoading=!0,this.$axios.get("/admin/shortcutContent/list",{params:{page:1,limit:50,status:1}}).then((function(t){var n,o=Object(a["a"])(t.data.data);try{for(o.s();!(n=o.n()).done;){var i=n.value;e.options.push({value:i.id,label:i.content})}}catch(r){o.e(r)}finally{o.f()}})).catch((function(){}))},onOneClickRepair:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.notice=n,this.$axios.post("/admin/order/oneClickRepair",{id:e.id}).then((function(e){t.notice&&t.$notify({title:"成功",message:"同步完成",type:"success"}),t.getList(0,0)})).catch((function(e){}))},dyOrderCancel:function(e,t){var n=this;this.$axios.post("/admin/order/dyOrderCancel",{id:e.id,cancel_type:t}).then((function(e){n.$notify({title:"成功",message:"短信已发送给客人",type:"success"}),n.orderConfirmDialogVisible=!1,n.getList()})).catch((function(e){n.$notify.error({title:"发送失败",message:e})}))},dyOrderConfirm:function(e,t){var n=this;this.$axios.post("/admin/order/dyOrderConfirm",{id:e.id,confirm_result:t}).then((function(e){n.$notify({title:"成功",message:"操作成功",type:"success"}),n.orderConfirmDialogVisible=!1,n.getList()})).catch((function(e){n.$notify.error({title:"接单失败",message:e})}))},onOneClickYyHandle:function(e){var t=this;this.$axios.post("/admin/order/changeAppointmentStatus",{id:e.id}).then((function(e){t.dialogVisible=!1,t.$notify({title:"成功",message:"已处理",type:"success"})})).catch((function(e){t.$notify.error({title:"错误",message:e})}))},stripHtml:function(e){var t=document.createElement("div");return t.innerHTML=e,t.textContent||t.innerText||""},copyToClipboard:function(e){var t=this.stripHtml(e),n=document.createElement("textarea");n.value=t,document.body.appendChild(n),n.select(),document.execCommand("copy"),document.body.removeChild(n),this.$message({showClose:!0,message:"内容已复制",type:"success"})},callPhone:function(e){var t=this;return Object(s["a"])(Object(l["a"])().mark((function n(){var o,i;return Object(l["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e){n.next=3;break}return t.$message.error("电话号码不能为空"),n.abrupt("return");case 3:if(11===e.length){n.next=6;break}return t.$message.error("请输入正确的手机号码"),n.abrupt("return");case 6:if(n.prev=6,t.currentPhoneNumber=e,t.softphoneVisible=!0,!t.moorSoftphone){n.next=11;break}return n.abrupt("return");case 11:return n.next=13,t.$axios.get("/admin/qimo/getAvailableAgent");case 13:if(o=n.sent,o.data){n.next=17;break}return t.$message.error("暂无空闲坐席，请稍后再试"),n.abrupt("return");case 17:o.data,console.log("创建Softphone实例..."),t.moorSoftphone&&console.log("销毁之前的Softphone实例"),i=new p.a({accountId:"T00000032238",agentNumber:"8000@xglgj",password:"PZy12jxT8000",loginType:"Local",proxy_url:"https://sh-hw-cc-v4.7moor.com",success:function(){console.log("Softphone初始化成功！"),t.moorSoftphone=i,t.initSoftphoneEvents(i,null)},error:function(e){console.log("Softphone初始化失败:",e),t.$message.error("软电话初始化失败")}}),console.log("Softphone实例创建完成，等待初始化..."),n.next=28;break;case 24:n.prev=24,n.t0=n["catch"](6),console.error("呼叫失败:",n.t0),t.$message.error(n.t0.message||"呼叫失败");case 28:case"end":return n.stop()}}),n,null,[[6,24]])})))()},initSoftphoneEvents:function(e,t){var n=this;return Object(s["a"])(Object(l["a"])().mark((function o(){return Object(l["a"])().wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.prev=0,e.attachEvent({success:function(){console.log("事件绑定成功")},message:function(e){if(console.log("通话事件:",e.event),e.event)switch(e.event.type){case"peerstate":console.log("座席状态变化:",e.event.typeValue);break;case"dialing":console.log("呼叫中..."),n.$message.info("正在拨号...");break;case"dialTalking":console.log("外呼通话中"),n.$message.success("通话已接通");break;case"innerTalking":console.log("呼入通话中");break;default:console.log("其他事件:",e.event.type)}},error:function(e){console.log("事件绑定异常:",e)}}),o.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:if(!t){o.next=9;break}return o.next=7,n.$axios.post("/admin/qimo/makeCall",{callee_number:t,agent_id:1});case 7:console.log("发起外呼:",t),e.callApi.dialout({calleeNumber:t,success:function(e){console.log("外呼成功:",e),n.$message.success("呼叫发起成功")},fail:function(e){console.log("外呼失败:",e),n.$message.error("呼叫失败: ".concat(e.message||"未知错误"))}});case 9:o.next=15;break;case 11:o.prev=11,o.t0=o["catch"](0),console.error("初始化事件失败:",o.t0),n.$message.error("软电话事件初始化失败");case 15:case"end":return o.stop()}}),o,null,[[0,11]])})))()},loadScript:function(e){return new Promise((function(t,n){if(window.moorSimpleSoftphone)t();else{var o=document.createElement("script");o.src=e,o.onload=t,o.onerror=n,document.head.appendChild(o)}}))},tryAlternativeCall:function(e){console.log("尝试备用呼叫方法");var t=window.moorSimpleSoftphone;if("function"===typeof t.call){console.log("尝试call方法");try{var n=t.call(e);if(console.log("call方法结果:",n),!1!==n&&null!==n)return void this.$message.success("呼叫发起成功(call方法)")}catch(o){console.log("call方法失败:",o)}}this.$message.error("呼叫功能暂不可用，请联系管理员"),console.log("所有呼叫方法都失败，SDK状态:",{initState:t.initState,isInit:t.isInit,isInitApp:t.isInitApp,availableMethods:Object.keys(t).filter((function(e){return"function"===typeof t[e]}))})},onLoginTypeChange:function(e){console.log("登录类型变化为:",e),this.currentLoginType=e},onReinitSoftphone:function(e){var t=this;return Object(s["a"])(Object(l["a"])().mark((function n(){var o,i;return Object(l["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(n.prev=0,console.log("重新初始化软电话，登录类型:",e),t.moorSoftphone){try{t.moorSoftphone.destroy&&"function"===typeof t.moorSoftphone.destroy&&t.moorSoftphone.destroy()}catch(a){console.log("销毁实例时出错:",a)}t.moorSoftphone=null}return n.next=5,new Promise((function(e){return setTimeout(e,500)}));case 5:return n.next=7,t.$axios.get("/admin/qimo/getAvailableAgent");case 7:if(o=n.sent,o.data){n.next=11;break}return t.$message.error("暂无空闲坐席，请稍后再试"),n.abrupt("return");case 11:console.log("创建新的Softphone实例，loginType:",e),i=new p.a({accountId:"T00000032238",agentNumber:"8000@xglgj",password:"PZy12jxT8000",loginType:e,proxy_url:"https://sh-hw-cc-v4.7moor.com",success:function(){console.log("重新初始化成功！loginType:",e),t.moorSoftphone=i,t.$message.success("已切换到".concat("Local"===e?"手机号":"sip"===e?"SIP话机":"WebRTC","模式")),t.$nextTick((function(){var e;null===(e=t.$refs.softphone)||void 0===e||e.updateStatus("空闲")})),t.initSoftphoneEvents(i,null)},error:function(e){console.log("重新初始化失败:",e),t.$message.error("切换登录方式失败: ".concat(e.message||"未知错误"))}}),console.log("Softphone实例创建调用完成，等待初始化回调..."),n.next=20;break;case 16:n.prev=16,n.t0=n["catch"](0),console.error("重新初始化异常:",n.t0),t.$message.error("切换登录方式异常: "+n.t0.message);case 20:case"end":return n.stop()}}),n,null,[[0,16]])})))()}}},h=b,_=(n("8aea"),n("8a34")),y=Object(_["a"])(h,o,i,!1,null,"43d6feed",null);t["default"]=y.exports},"867f":function(e,t,n){},"8aea":function(e,t,n){"use strict";n("867f")},e350:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));n("d987"),n("90c8"),n("6de1");var o=n("4360");function i(e){if(e&&e instanceof Array&&e.length>0){var t=o["a"].getters&&o["a"].getters.roles,n=e,i=t.some((function(e){return n.includes(e)}));return i}return console.error("need roles! Like v-permission=\"['admin','editor']\""),!1}}}]);