<?php
namespace app\admin\controller;

use app\model\OrderBooks;
use app\model\Orders;
use app\model\ProductSchedules;
use app\model\Routes;
use support\Log;
use support\Request;

class RoutesController extends base {
    /**
     * 线路列表
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DbException
     */
    public function list(Request $request) {
        $params = $request->all();
        $query = Routes::order('id', 'desc');
        $query->where('shop_id', $request->admin->shop_id ?? 1);
        if(isset($params['route_name']) && !empty($params['route_name'])) {
            $query->whereLike('route_name', "%{$params['route_name']}%");
        }

        $list = $query->append(['profit'])->paginate($request->get('limit',1000));
        return $this->success($list,null);
    }

    public function add(Request $request) {
        if (!$request->post('route_name')) {
            return $this->error(2001, '线路名称必填');
        }

        $where = ['id' => $request->post('id')];
        $route = (new Routes())->where($where)->find();
        if (empty($route)) {
            $route = new Routes();
        }

        $route->route_name = $request->post('route_name');
        $route->status = 1;
        $route->type = $request->post('type');
        $route->days = $request->post('days');
        $route->ground_price = $request->post('ground_price');
        $route->sale_price = $request->post('sale_price');
        $route->trip_info = $request->post('trip_info', '');
        $route->route_image = $request->post('route_image', '');
        $route->qualifications = $request->post('qualifications', '');
        $route->payment_code = $request->post('payment_code', '');
        $route->shop_id = $request->admin->shop_id;
        $route->save();
        Log::info('route:' . json_encode($route));
        return $this->success([]);
    }

//    /**
//     * 商品排期列表
//     * @param Request $request
//     * @return \support\Response
//     */
//    public function productSchedules(Request $request) {
//        if (!$request->get('id')) {
//            return $this->error(2001, '请选择商品');
//        }
//        $date = $request->get('date', date('Y-m'));
//        $firstDay = date('Y-m-01', strtotime($date));
//        $lastDay = date('Y-m-t', strtotime($date));
//        // 排期列表
//        $productSchedules = ProductSchedules::where('product_id', $request->get('id'))
//            ->whereBetween('date', [$firstDay, $lastDay])
//            ->select()
//            ->toArray();
//        $productSchedules = array_column($productSchedules, null, 'date');
//
//        // 预约列表
//        $books = OrderBooks::join('orders', 'orders.id=order_books.order_id')
//                ->whereBetween('order_books.travel_date', [$firstDay, $lastDay])
//                ->group(['order_books.travel_date', 'orders.product_id'])
//                ->fieldRaw('order_books.travel_date, orders.product_id, count(1) as num')
//                ->select()
//                ->toArray();
//        $books = array_column($books, null, 'travel_date');
//
//        // 遍历日期范围
//        $dates = [];
//        $firstDay = strtotime($firstDay);
//        $lastDay = strtotime($lastDay);
//        for ($currentDay = $firstDay; $currentDay <= $lastDay; $currentDay = strtotime('+1 day', $currentDay)) {
//            $date = date('Y-m-d', $currentDay);
//            $current = [
//                'date' => $date,
//                'books_num' => 0,
//                'left_num' => 0
//            ];
//            // 预约数量
//            if (isset($books[$date])) {
//                $current['books_num'] = $books[$date]['num'];
//            }
//            // 剩余数量
//            if (isset($productSchedules[$date]) && $productSchedules[$date]['num'] > 0) {
//                $leftNum = $productSchedules[$date]['num'] - $current['books_num'];
//                $current['left_num'] = $leftNum ?: 0;
//            }
//            array_push($dates, $current);
//        }
//
//        return $this->success($dates);
//    }
//
//    /**
//     * 商品排期
//     * @param Request $request
//     * @return \support\Response
//     */
//    public function addProductSchedules(Request $request) {
//        if (!$request->post('id')) {
//            return $this->error(2001, '请选择商品');
//        }
//        if (!$request->post('date')) {
//            return $this->error(2001, '选择设置日期');
//        }
//        if (!$request->post('num') || !is_numeric($request->post('num'))) {
//            return $this->error(2001, '数量格式异常');
//        }
//        $thirdProductId = Products::where('id', $request->post('id'))->value('third_product_id');
//        if (!$thirdProductId) {
//            return $this->error('商品信息未找到');
//        }
//
//        $where = ['product_id' => $request->post('id'), 'date' => $request->post('date')];
//        $productSchedules = (new ProductSchedules())->where($where)->find();
//        if (empty($productSchedules)) {
//            $productSchedules = new ProductSchedules();
//        }
//        $productSchedules->product_id = $request->post('id');
//        $productSchedules->date = $request->post('date');
//        $productSchedules->num = $request->post('num');
//        $productSchedules->third_product_id = $thirdProductId;
//        $productSchedules->save();
//        return $this->success([]);
//    }
}
