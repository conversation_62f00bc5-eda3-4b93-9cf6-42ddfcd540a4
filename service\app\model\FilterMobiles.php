<?php
namespace app\model;

use app\common\Keys;
use support\Redis;

class FilterMobiles extends base
{
    /**
     * @param string $mobile
     * @param bool   $refresh
     * @return bool
     * @throws \think\db\exception\BindParamException
     */
    public static function isFilterMobile(string $mobile, bool $refresh = false): bool
    {
        $key = 'order_filter_mobiles';

        if ($refresh || !Redis::exists($key)) {
            $list = self::query()->column('id', 'mobile');

            if (!empty($list)) {
                $chunks = array_chunk($list, 50, true);

                foreach($chunks as $mobiles) {
                    Redis::hMSet($key, $mobiles);
                }

                Redis::expire($key, 60*60*24);
            }
        }

        return Redis::hExists($key, $mobile);
    }

}