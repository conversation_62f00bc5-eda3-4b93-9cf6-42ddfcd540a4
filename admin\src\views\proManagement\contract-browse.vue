<template>
  <div class="contract-browse">
    <el-row>
      <el-col :span="24">
        <div class="search-form">
          <el-form :inline="true" ref="form" :model="dataForm" label-width="60px">
            <el-form-item label="关键字:">
              <el-input
                v-model="dataForm.title"
                placeholder="请输入搜索关键字"
                style="width: 400px"
                class="filter-item"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="success" @click="onSubmit">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
    
    <div class="browse-container">
      <!-- 左侧线路列表 -->
      <div class="browse-left" style="overflow: auto; padding: 10px">
        <div 
          v-for="item in productList" 
          :key="item.id" 
          @click="handleSelectProduct(item.id)" 
          class="btn"
          :class="{active: currentProductId === item.id}"
        >
          {{ item.product_name }}
        </div>
      </div>
      
      <!-- 右侧合同内容 -->
      <div class="browse-right">
        <ul class="infinite-list" style="overflow: auto">
          <li class="browse-right-container" v-for="item in contractList" :key="item.id">
            <div class="title">
              <span>{{ item.title }}</span>
            </div>
            
            <!-- 图片区域 -->
            <div class="images-container">
              <div class="image-section" v-if="item.ye_license && getImages(item.ye_license).length > 0">
                <h3>营业执照</h3>
                <div class="image-grid">
                  <div v-for="(image, index) in getImages(item.ye_license)" :key="'ye_'+index" class="image-item">
                    <el-image 
                      :src="image" 
                      fit="cover"
                      class="grid-image"
                      :preview-src-list="getImages(item.ye_license)">
                    </el-image>
                    <div class="image-actions">
                      <el-tooltip content="复制链接" placement="top">
                        <el-button size="mini" type="primary" icon="el-icon-link" circle @click.stop="copyImageUrl(image)"></el-button>
                      </el-tooltip>
                      <el-tooltip content="下载图片" placement="top">
                        <el-button size="mini" type="success" icon="el-icon-download" circle @click.stop="downloadImage(image)"></el-button>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="image-section" v-if="item.jy_license && getImages(item.jy_license).length > 0">
                <h3>经营许可证</h3>
                <div class="image-grid">
                  <div v-for="(image, index) in getImages(item.jy_license)" :key="'jy_'+index" class="image-item">
                    <el-image 
                      :src="image" 
                      fit="cover"
                      class="grid-image"
                      :preview-src-list="getImages(item.jy_license)">
                    </el-image>
                    <div class="image-actions">
                      <el-tooltip content="复制链接" placement="top">
                        <el-button size="mini" type="primary" icon="el-icon-link" circle @click.stop="copyImageUrl(image)"></el-button>
                      </el-tooltip>
                      <el-tooltip content="下载图片" placement="top">
                        <el-button size="mini" type="success" icon="el-icon-download" circle @click.stop="downloadImage(image)"></el-button>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="image-section" v-if="item.pay_qr && getImages(item.pay_qr).length > 0">
                <h3>收款二维码</h3>
                <div class="image-grid">
                  <div v-for="(image, index) in getImages(item.pay_qr)" :key="'pay_'+index" class="image-item">
                    <el-image 
                      :src="image" 
                      fit="cover"
                      class="grid-image"
                      :preview-src-list="getImages(item.pay_qr)">
                    </el-image>
                    <div class="image-actions">
                      <el-tooltip content="复制链接" placement="top">
                        <el-button size="mini" type="primary" icon="el-icon-link" circle @click.stop="copyImageUrl(image)"></el-button>
                      </el-tooltip>
                      <el-tooltip content="下载图片" placement="top">
                        <el-button size="mini" type="success" icon="el-icon-download" circle @click.stop="downloadImage(image)"></el-button>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="image-section" v-if="item.hotel_pictures && getImages(item.hotel_pictures).length > 0">
                <h3>酒店图片</h3>
                <div class="image-grid">
                  <div v-for="(image, index) in getImages(item.hotel_pictures)" :key="'hotel_'+index" class="image-item">
                    <el-image 
                      :src="image" 
                      fit="cover"
                      class="grid-image"
                      :preview-src-list="getImages(item.hotel_pictures)">
                    </el-image>
                    <div class="image-actions">
                      <el-tooltip content="复制链接" placement="top">
                        <el-button size="mini" type="primary" icon="el-icon-link" circle @click.stop="copyImageUrl(image)"></el-button>
                      </el-tooltip>
                      <el-tooltip content="下载图片" placement="top">
                        <el-button size="mini" type="success" icon="el-icon-download" circle @click.stop="downloadImage(image)"></el-button>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="image-section" v-if="item.restaurant_picture && getImages(item.restaurant_picture).length > 0">
                <h3>餐厅图片</h3>
                <div class="image-grid">
                  <div v-for="(image, index) in getImages(item.restaurant_picture)" :key="'rest_'+index" class="image-item">
                    <el-image 
                      :src="image" 
                      fit="cover"
                      class="grid-image"
                      :preview-src-list="getImages(item.restaurant_picture)">
                    </el-image>
                    <div class="image-actions">
                      <el-tooltip content="复制链接" placement="top">
                        <el-button size="mini" type="primary" icon="el-icon-link" circle @click.stop="copyImageUrl(image)"></el-button>
                      </el-tooltip>
                      <el-tooltip content="下载图片" placement="top">
                        <el-button size="mini" type="success" icon="el-icon-download" circle @click.stop="downloadImage(image)"></el-button>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContractBrowse',
  data() {
    return {
      productList: [], // 从合同数据中提取的线路列表
      allContractList: [], // 保存所有合同数据
      contractList: [], // 当前显示的合同数据
      currentProductId: null,
      dataForm: {
        title: ''
      },
      loading: false
    }
  },
  created() {
    this.getContractList()
  },
  methods: {
    // 获取合同列表并提取线路数据
    getContractList() {
      this.loading = true
      const params = {
        limit: 1000, // 获取更多数据以确保包含所有线路
        title: this.dataForm.title
      }
      
      this.$axios.get('/admin/line/contract', {
        params: params
      }).then(response => {
        this.allContractList = response.data.data || []
        // 提取唯一的线路信息
        const productMap = new Map()
        this.allContractList.forEach(contract => {
          if (contract.product && contract.product.id && !productMap.has(contract.product.id)) {
            productMap.set(contract.product.id, {
              id: contract.product.id,
              product_name: contract.product.product_name
            })
          }
        })
        
        this.productList = Array.from(productMap.values())
        this.loading = false
        
        // 如果有线路数据，默认选择第一个
        if (this.productList.length > 0) {
          this.handleSelectProduct(this.productList[0].id)
        } else {
          this.contractList = []
        }
      }).catch(() => {
        this.loading = false
      })
    },
    
    // 将逗号分隔的图片字符串转为数组
    getImages(imageStr) {
      if (!imageStr) return []
      return imageStr.split(',').filter(item => item.trim() !== '')
    },
    
    // 选择线路，从本地数据中过滤
    handleSelectProduct(productId) {
      this.currentProductId = productId
      this.contractList = this.allContractList.filter(contract => 
        contract.product && contract.product.id == productId
      )
    },
    
    // 提交搜索
    onSubmit() {
      this.getContractList()
    },
    
    // 复制图片链接
    copyImageUrl(url) {
      const input = document.createElement('textarea')
      input.value = url
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      document.body.removeChild(input)
      this.$message({
        message: '图片链接已复制到剪贴板',
        type: 'success'
      })
    },
    
    // 下载图片
    downloadImage(url) {
      try {
        const a = document.createElement('a')
        a.href = url
        a.target = '_blank'
        // 从URL中提取文件名
        const fileName = url.substring(url.lastIndexOf('/') + 1)
        a.download = fileName || 'image.png'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        this.$message.success('下载已开始')
      } catch(error) {
        this.$message.error('下载失败')
        console.error('Download error:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.contract-browse {
  .search-form {
    margin-top: 10px;
    display: flex;
    justify-content: center;
  }
  
  .browse-container {
    display: flex;
    
    .browse-left {
      width: 18%;
      background: #fff;
      padding: 0 20px;
      border-right: 2px solid #46a6ff;
      height: calc(100vh - 154px);
      
      .btn {
        color: #fff;
        padding: 10px 20px;
        cursor: pointer;
        text-align: center;
        background: #46a6ff;
        border-radius: 10px;
        margin-bottom: 10px;
        transition: all 0.3s;
        
        &.active {
          background: #1976d2;
          box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        &:hover {
          background: #1976d2;
        }
      }
    }
    
    .browse-right {
      width: 82%;
      background: #fff;
      padding: 0 20px;
      
      .infinite-list {
        list-style-type: none;
        height: calc(100vh - 154px);
        padding: 0;
        margin: 0;
      }
      
      .browse-right-container {
        margin-bottom: 20px;
        
        .title {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 15px;
          color: #46a6ff;
        }
        
        .images-container {
          .image-section {
            margin-bottom: 20px;
            
            h3 {
              font-size: 16px;
              margin-bottom: 10px;
              color: #333;
              font-weight: 500;
            }
            
            .image-grid {
              display: flex;
              flex-wrap: wrap;
              gap: 10px;
              
              .image-item {
                width: 180px;
                height: 180px;
                overflow: hidden;
                border-radius: 4px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                position: relative;
                
                &:hover .image-actions {
                  opacity: 1;
                }
                
                .grid-image {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  cursor: pointer;
                }
                
                .image-actions {
                  position: absolute;
                  bottom: 0;
                  left: 0;
                  right: 0;
                  padding: 5px;
                  background-color: rgba(0, 0, 0, 0.5);
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  gap: 8px;
                  opacity: 0;
                  transition: opacity 0.3s ease;
                }
              }
            }
          }
        }
      }
    }
  }
}

.el-dialog__body {
  padding: 10px 20px;
}
</style> 