<template>
    <div class="app-container">
        <div class="title">新国旅内部管系统</div>
        <div class="content">
            <div class="content_box">
                <div class="item" @click="$router.push('/login')">
                    <img src="@/assets/home/<USER>" alt="">
                    <p>订单核销系统</p>
                </div>
                <div class="item" @click="$router.push('/show_route')">
                    <img src="@/assets/home/<USER>" alt="">
                    <p>售卖中线路</p>
                </div>
                <div class="item">
                    <img src="@/assets/home/<USER>" alt="">
                    <p>签证系统</p>
                </div>
            </div>
        </div>
        <div class="bottom">新国旅 版权所有 粤ICP备 xsiks545454号 粤公网安备 xsiks545454号</div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'home',
    data() {
      return {
        driver: null
      }
    },
    mounted() {
      
    },
    methods: {

    }
  }
  </script>
  <style lang="scss" scoped>
  .app-container{
    background: url('~@/assets/home/<USER>') no-repeat;
    background-size: 100% 100%;
    height: 100%;
}
  .title {
    font-size: 40px;
    text-align: center;
    margin-top: 80px;
    color: #425B93;
    font-weight: bold
  }
  .content_box{
    display: flex;
    justify-content: center;
    margin-top: 128px;
    .item{
        background: #4D72D5;
        border-radius: 20px;
        padding: 50px 18px;
        width: 260px;
        cursor: pointer;
        text-align: center;
        & + .item {
            margin-left: 114px;
        }
        img{
            width: 80px;
            height: 80px;
        }
        p{
            color: #fff;
            font-size: 30px;
            font-weight: bold;
            margin: 40px 0 0 0;
            // margin-top: 40px;
        }
    }
}
.bottom{
    text-align: center;
    color: #425B93;
    font-size: 14px;
    position: fixed;
    bottom: 56px;
    left: 50%;
    transform: translateX(-50%)
}
  </style>