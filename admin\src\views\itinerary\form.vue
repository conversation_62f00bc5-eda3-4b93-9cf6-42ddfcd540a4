<template>
  <div class="app-container">
    <div class="form-header">
      <h2>{{ isEdit ? '编辑行程' : '新增行程' }}</h2>
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </div>

    <el-form
      ref="itineraryForm"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="itinerary-form"
    >
      <!-- 基本信息 -->
      <el-card class="form-card" shadow="never">
        <div slot="header" class="card-header">
          <span>基本信息</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="行程标题" prop="title">
              <el-input v-model="formData.title" placeholder="请输入行程标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商品类型" prop="categoryId">
              <el-cascader
                v-model="formData.categoryId"
                :options="categoryOptions"
                :props="categoryProps"
                placeholder="请选择商品类型"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="目的地" prop="destination">
              <el-select
                v-model="formData.destination"
                placeholder="请选择目的地"
                multiple
                filterable
                style="width: 100%"
              >
                <el-option-group
                  v-for="province in cityOptions"
                  :key="province.provinceCode"
                  :label="province.provinceName"
                  :value="province.provinceCode"
                >
                  <el-option
                    v-for="city in province.cities"
                    :key="city.cityCode"
                    :label="city.cityName"
                    :value="city.cityCode"
                  />
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出发城市" prop="departureCity">
              <el-select
                v-model="formData.departureCity"
                placeholder="请选择出发城市"
                multiple
                filterable
                style="width: 100%"
              >
                <el-option-group
                  v-for="province in cityOptions"
                  :key="province.provinceCode"
                  :label="province.provinceName"
                  :value="province.provinceCode"
                >
                  <el-option
                    v-for="city in province.cities"
                    :key="city.cityCode"
                    :label="city.cityName"
                    :value="city.cityCode"
                  />
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="天数" prop="days">
              <el-input-number
                v-model="formData.days"
                :min="1"
                :max="30"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="晚数" prop="nights">
              <el-input-number
                v-model="formData.nights"
                :min="0"
                :max="29"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品类型" prop="product_type">
              <el-select v-model="formData.product_type" style="width: 100%">
                <el-option label="跟团游" value="group" />
                <el-option label="自由行" value="free" />
                <el-option label="定制游" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="旅行方式" prop="travel_mode">
              <el-select v-model="formData.travel_mode" style="width: 100%">
                <el-option label="飞机" value="flight" />
                <el-option label="高铁" value="train" />
                <el-option label="汽车" value="bus" />
                <el-option label="自驾" value="self_drive" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="行程描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            placeholder="请输入行程描述"
          />
        </el-form-item>

        <el-form-item label="封面图片" prop="cover_image">
          <multiple-image v-model="formData.cover_image" />
        </el-form-item>
        <el-form-item label="轮播图" prop="images">
          <multiple-image v-model="formData.images" />
        </el-form-item>
        <el-form-item label="图片描述" prop="image_description">
          <multiple-image v-model="formData.image_description" />
        </el-form-item>
      </el-card>

      <!-- 价格设置 -->
      <el-card class="form-card" shadow="never">
        <div slot="header" class="card-header">
          <span>价格设置</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="销售价格" prop="price">
              <el-input-number
                v-model="formData.price"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原价" prop="original_price">
              <el-input-number
                v-model="formData.original_price"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="定金" prop="deposit">
              <el-input-number
                v-model="formData.deposit"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="单房差" prop="single_room_surcharge">
              <el-input-number
                v-model="formData.single_room_surcharge"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="房间升级费" prop="room_upgrade_surcharge">
              <el-input-number
                v-model="formData.room_upgrade_surcharge"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 团队设置 -->
      <el-card class="form-card" shadow="never">
        <div slot="header" class="card-header">
          <span>团队设置</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="团队类型" prop="group_type">
              <el-select v-model="formData.group_type" style="width: 100%">
                <el-option label="散客拼团" value="join" />
                <el-option label="独立成团" value="private" />
                <el-option label="定制团" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最小团队人数" prop="min_group_size">
              <el-input-number
                v-model="formData.min_group_size"
                :min="1"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大团队人数" prop="max_group_size">
              <el-input-number
                v-model="formData.max_group_size"
                :min="1"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 有效期设置 -->
      <el-card class="form-card" shadow="never">
        <div slot="header" class="card-header">
          <span>有效期设置</span>
        </div>

        <el-form-item label="是否长期有效" prop="is_long_term">
          <el-switch v-model="formData.is_long_term" />
        </el-form-item>

        <el-row v-if="!formData.is_long_term" :gutter="20">
          <el-col :span="12">
            <el-form-item label="有效期开始" prop="valid_from">
              <el-date-picker
                v-model="formData.valid_from"
                type="date"
                placeholder="选择开始日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效期结束" prop="valid_to">
              <el-date-picker
                v-model="formData.valid_to"
                type="date"
                placeholder="选择结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 行程安排 -->
      <el-card class="form-card" shadow="never">
        <div slot="header" class="card-header">
          <span>行程安排</span>
          <el-button type="primary" size="mini" @click="addScheduleDay">
            添加行程日
          </el-button>
        </div>

        <div v-for="(day, dayIndex) in formData.scheduleDays" :key="dayIndex" class="schedule-day">
          <div class="day-header">
            <h4>第{{ dayIndex + 1 }}天</h4>
            <el-button type="danger" size="mini" @click="removeScheduleDay(dayIndex)">
              删除
            </el-button>
          </div>

          <el-form-item :label="`第${dayIndex + 1}天标题`">
            <el-input v-model="day.title" placeholder="请输入当天行程标题" />
          </el-form-item>

          <el-form-item :label="`第${dayIndex + 1}天描述`">
            <el-input
              v-model="day.description"
              type="textarea"
              :rows="3"
              placeholder="请输入当天行程描述"
            />
          </el-form-item>

          <!-- 行程安排详情 -->
          <div class="schedule-details">
            <div class="schedule-header">
              <span>具体安排</span>
              <el-button type="primary" size="mini" @click="openScheduleDialog(dayIndex)">
                + 添加安排
              </el-button>
            </div>

            <div v-for="(schedule, scheduleIndex) in day.schedules" :key="scheduleIndex" class="schedule-item">
              <div class="schedule-summary">
                <div class="schedule-info">
                  <el-tag :type="getScheduleTypeTag(schedule.type)" size="small">
                    {{ getScheduleTypeText(schedule.type) }}
                  </el-tag>
                  <span class="schedule-time">{{ schedule.time }}</span>
                  <span class="schedule-location">{{ schedule.location }}</span>
                  <span class="schedule-duration" v-if="schedule.duration">
                    ({{ formatDuration(schedule.duration) }})
                  </span>
                </div>
                <div class="schedule-content">
                  {{ schedule.activities }}
                </div>
                <div class="schedule-actions">
                  <el-button type="text" size="mini" @click="editScheduleItem(dayIndex, scheduleIndex)">
                    编辑
                  </el-button>
                  <el-button type="text" size="mini" @click="removeScheduleItem(dayIndex, scheduleIndex)">
                    删除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 费用说明 -->
      <el-card class="form-card" shadow="never">
        <div slot="header" class="card-header">
          <span>费用说明</span>
        </div>

        <el-form-item label="费用包含" prop="priceIncludes">
          <el-input
            v-model="formData.priceIncludes"
            type="textarea"
            :rows="4"
            placeholder="请输入费用包含的项目"
          />
        </el-form-item>

        <el-form-item label="费用不含" prop="priceExcludes">
          <el-input
            v-model="formData.priceExcludes"
            type="textarea"
            :rows="4"
            placeholder="请输入费用不包含的项目"
          />
        </el-form-item>

        <el-form-item label="退改政策" prop="refundPolicy">
          <el-input
            v-model="formData.refundPolicy"
            type="textarea"
            :rows="4"
            placeholder="请输入退改政策"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <!-- 行程安排弹框 -->
    <el-dialog
      :title="scheduleDialogTitle"
      :visible.sync="scheduleDialogVisible"
      width="800px"
      @close="closeScheduleDialog"
    >
      <el-form
        ref="scheduleForm"
        :model="currentSchedule"
        :rules="scheduleRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="行程类型" prop="type">
              <el-select v-model="currentSchedule.type" style="width: 100%">
                <el-option label="餐食" value="meal" />
                <el-option label="交通" value="transport" />
                <el-option label="景点" value="attraction" />
                <el-option label="住宿" value="accommodation" />
                <el-option label="自由活动" value="freeActivity" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="时间" prop="start_time">
              <el-time-picker
                v-model="currentSchedule.start_time"
                placeholder="选择时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="地点" prop="location">
          <el-input v-model="currentSchedule.location" placeholder="请输入地点" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="游玩时长" prop="duration">
              <div class="duration-input">
                <el-input-number
                  v-model="currentSchedule.durationHours"
                  :min="0"
                  :max="23"
                  controls-position="right"
                  style="width: 80px"
                />
                <span style="margin: 0 5px;">小时</span>
                <el-input-number
                  v-model="currentSchedule.durationMinutes"
                  :min="0"
                  :max="59"
                  controls-position="right"
                  style="width: 80px"
                />
                <span style="margin-left: 5px;">分钟</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="活动内容" prop="activities">
          <el-input
            v-model="currentSchedule.activities"
            type="textarea"
            :rows="3"
            placeholder="请输入活动内容"
          />
        </el-form-item>

        <el-form-item label="图片" prop="images">
          <multiple-image v-model="currentSchedule.images" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeScheduleDialog">取消</el-button>
        <el-button type="primary" @click="saveScheduleItem">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getItineraryDetailApi,
  createItineraryApi,
  updateItineraryApi,
  getItineraryCategoryTreeApi,
  getCityListApi
} from "@/api/admin";
import { getToken } from "@/utils/auth";
import MultipleImage from "@/components/Upload/MultipleImage.vue";

export default {
  name: "ItineraryForm",
  components: { MultipleImage },
  data() {
    return {
      isEdit: false,
      itineraryId: null,
      saving: false,
      uploadAction: process.env.VUE_APP_BASE_API + "/admin/upload/index",
      uploadHeaders: {
        Authorization: "Bearer " + getToken()
      },
      formData: {
        title: '',
        categoryId: [],
        destination: [],
        days: 1,
        nights: 0,
        productType: 'group',
        departureCity: [],
        travel_mode: 'flight',
        description: '',
        cover_image: '',
        price: 0,
        original_price: 0,
        deposit: 0,
        single_room_surcharge: 0,
        room_upgrade_surcharge: 0,
        group_type: 'join',
        min_group_size: 1,
        max_group_size: 40,
        is_long_term: true,
        valid_from: null,
        valid_to: null,
        schedule_days: [],
        price_includes: '',
        price_excludes: '',
        refund_policy: ''
      },
      // 分类选项
      categoryOptions: [],
      categoryProps: {
        value: 'category_id',
        label: 'category_name',
        children: 'children',
        checkStrictly: true
      },
      // 城市选项
      cityOptions: [],
      // 行程安排弹框相关
      scheduleDialogVisible: false,
      scheduleDialogTitle: '添加行程安排',
      currentSchedule: {
        type: 'attraction',
        time: '',
        location: '',
        durationHours: 0,
        durationMinutes: 0,
        activities: '',
        images: []
      },
      currentDayIndex: -1,
      currentScheduleIndex: -1,
      rules: {
        title: [
          { required: true, message: '请输入行程标题', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择商品类型', trigger: 'change' }
        ],
        destination: [
          { required: true, message: '请选择目的地', trigger: 'change' }
        ],
        departureCity: [
          { required: true, message: '请选择出发城市', trigger: 'change' }
        ],
        days: [
          { required: true, message: '请输入天数', trigger: 'blur' }
        ],
        nights: [
          { required: true, message: '请输入晚数', trigger: 'blur' }
        ],
        productType: [
          { required: true, message: '请选择产品类型', trigger: 'change' }
        ],
        price: [
          { required: true, message: '请输入销售价格', trigger: 'blur' }
        ]
      },
      // 行程安排表单验证规则
      scheduleRules: {
        type: [
          { required: true, message: '请选择行程类型', trigger: 'change' }
        ],
        time: [
          { required: true, message: '请选择时间', trigger: 'change' }
        ],
        location: [
          { required: true, message: '请输入地点', trigger: 'blur' }
        ],
        activities: [
          { required: true, message: '请输入活动内容', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.initForm();
    this.loadCategoryOptions();
    this.loadCityOptions();
  },
  methods: {
    // 初始化表单
    async initForm() {
      const { id } = this.$route.params;
      if (id) {
        this.isEdit = true;
        this.itineraryId = id;
        await this.loadItineraryDetail();
      } else {
        // 新增时初始化行程安排
        this.initScheduleDays();
      }
    },

    // 初始化行程安排
    initScheduleDays() {
      const days = this.formData.days || 1;
      this.formData.scheduleDays = [];
      for (let i = 0; i < days; i++) {
        this.formData.scheduleDays.push({
          title: '',
          description: '',
          schedules: []
        });
      }
    },

    // 加载行程详情
    async loadItineraryDetail() {
      try {
        const response = await getItineraryDetailApi(this.itineraryId);
        const data = response.data;

        // 处理分类ID数据 - 优先使用category_path，其次使用category_id
        if (data.category_path && Array.isArray(data.category_path)) {
          this.formData.categoryId = data.category_path;
        } else if (data.categoryId) {
          if (Array.isArray(data.categoryId)) {
            this.formData.categoryId = data.categoryId;
          } else if (typeof data.categoryId === 'string') {
            // 如果是字符串，尝试解析为数组
            try {
              const parsed = JSON.parse(data.categoryId);
              this.formData.categoryId = Array.isArray(parsed) ? parsed : [parsed];
            } catch (e) {
              this.formData.categoryId = [data.categoryId];
            }
          } else {
            this.formData.categoryId = [data.categoryId];
          }
        } else if (data.category_id) {
          // 处理单个category_id的情况
          this.formData.categoryId = [parseInt(data.category_id)];
        }

        // 处理目的地和出发城市数据
        if (data.destination) {
          this.formData.destination = Array.isArray(data.destination) ? data.destination : [data.destination];
        }
        if (data.departure_city) {
          this.formData.departureCity = Array.isArray(data.departure_city) ? data.departure_city : [data.departure_city];
        } else if (data.departureCity) {
          this.formData.departureCity = Array.isArray(data.departureCity) ? data.departureCity : [data.departureCity];
        }

        // 处理行程安排数据
        if (data.scheduleDays && Array.isArray(data.scheduleDays)) {
          this.formData.scheduleDays = data.scheduleDays.map(day => {
            // 深度拷贝并处理字段名和数据格式
            const schedules = day.schedules || day.schedule || [];
            const processedSchedules = Array.isArray(schedules) ? schedules.map(schedule => ({
              ...schedule,
              // 将 start_time 映射为 time，并格式化时间
              time: schedule.start_time ? schedule.start_time.substring(0, 5) : (schedule.time || ''),
              // 确保其他必要字段存在
              location: schedule.location || '',
              activities: schedule.activities || '',
              duration: schedule.duration || 0,
              type: schedule.type || 'attraction',
              images: schedule.images || []
            })) : [];

            return {
              ...day,
              schedules: processedSchedules
            };
          });
        }

        // 处理图片相关数据
        if (data.images) {
          this.formData.images = Array.isArray(data.images) ? data.images : [];
        }
        if (data.image_description) {
          this.formData.image_description = Array.isArray(data.image_description) ? data.image_description : [];
        } else {
          this.formData.image_description = [];
        }

        // 处理其他数据 - 排除已经处理过的字段
        const { category_id, category_path, departure_city, scheduleDays, images, image_description, ...otherData } = data;
        this.formData = { ...this.formData, ...otherData };

        // 强制触发响应式更新
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      } catch (error) {
        console.error('获取行程详情失败:', error);
        this.$message.error('获取行程详情失败');
      }
    },

    // 保存行程
    async handleSave() {
      this.$refs.itineraryForm.validate(async (valid) => {
        if (!valid) return;

        this.saving = true;
        try {
          await (this.isEdit
            ? updateItineraryApi(this.itineraryId, this.formData)
            : createItineraryApi(this.formData));

          this.$message.success(this.isEdit ? '更新成功' : '创建成功');
          this.$router.push('/itinerary');
        } catch (error) {
          console.error('保存失败:', error);
          this.$message.error('保存失败');
        } finally {
          this.saving = false;
        }
      });
    },

    // 取消操作
    handleCancel() {
      this.$router.push('/itinerary');
    },

    // 封面图片上传成功
    handleCoverSuccess(response) {
      if (response.data && response.data.url) {
        this.formData.coverImage = response.data.url;
      } else {
        this.$message.error('图片上传失败');
      }
    },

    // 上传前验证
    beforeCoverUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },

    // 添加行程日
    addScheduleDay() {
      this.formData.scheduleDays.push({
        title: '',
        description: '',
        schedules: []
      });
    },

    // 删除行程日
    removeScheduleDay(index) {
      this.formData.scheduleDays.splice(index, 1);
    },



    // 删除行程安排
    removeScheduleItem(dayIndex, scheduleIndex) {
      this.$confirm('确认删除该行程安排吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.formData.scheduleDays[dayIndex].schedules.splice(scheduleIndex, 1);
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },

    // 打开行程安排弹框
    openScheduleDialog(dayIndex) {
      this.currentDayIndex = dayIndex;
      this.currentScheduleIndex = -1;
      this.scheduleDialogTitle = '添加行程安排';
      this.resetCurrentSchedule();
      this.scheduleDialogVisible = true;
    },

    // 编辑行程安排
    editScheduleItem(dayIndex, scheduleIndex) {
      this.currentDayIndex = dayIndex;
      this.currentScheduleIndex = scheduleIndex;
      this.scheduleDialogTitle = '编辑行程安排';

      const schedule = this.formData.scheduleDays[dayIndex].schedules[scheduleIndex];
      this.currentSchedule = {
        type: schedule.type || 'attraction',
        start_time: schedule.start_time || '',
        location: schedule.location || '',
        durationHours: Math.floor((schedule.duration || 0) / 60),
        durationMinutes: (schedule.duration || 0) % 60,
        activities: schedule.activities || '',
        images: schedule.images || []
      };

      this.scheduleDialogVisible = true;
    },

    // 重置当前行程安排数据
    resetCurrentSchedule() {
      this.currentSchedule = {
        type: 'attraction',
        start_time: '',
        location: '',
        durationHours: 0,
        durationMinutes: 0,
        activities: '',
        images: []
      };
    },

    // 关闭弹框
    closeScheduleDialog() {
      this.scheduleDialogVisible = false;
      this.resetCurrentSchedule();
      this.currentDayIndex = -1;
      this.currentScheduleIndex = -1;
    },

    // 保存行程安排
    saveScheduleItem() {
      this.$refs.scheduleForm.validate((valid) => {
        if (!valid) return;

        const scheduleData = {
          type: this.currentSchedule.type,
          start_time: this.currentSchedule.start_time,
          location: this.currentSchedule.location,
          duration: this.currentSchedule.durationHours * 60 + this.currentSchedule.durationMinutes,
          activities: this.currentSchedule.activities,
          images: this.currentSchedule.images
        };

        if (this.currentScheduleIndex === -1) {
          // 新增
          this.formData.scheduleDays[this.currentDayIndex].schedules.push(scheduleData);
        } else {
          // 编辑
          this.formData.scheduleDays[this.currentDayIndex].schedules[this.currentScheduleIndex] = scheduleData;
        }

        this.closeScheduleDialog();
        this.$message.success('保存成功');
      });
    },

    // 加载分类选项
    async loadCategoryOptions() {
      try {
        const response = await getItineraryCategoryTreeApi();
        this.categoryOptions = this.formatCategoryTree(response.data || []);
      } catch (error) {
        console.error('获取分类数据失败:', error);
      }
    },

    // 格式化分类树数据
    formatCategoryTree(categories) {
      return categories.map(category => ({
        category_id: category.category_id,
        category_name: category.category_name,
        children: category.children ? this.formatCategoryTree(category.children) : []
      }));
    },

    // 加载城市选项
    async loadCityOptions() {
      try {
        const response = await getCityListApi();
        this.cityOptions = response.data || [];
        console.log('加载的城市数据:', this.cityOptions);
      } catch (error) {
        console.error('获取城市数据失败:', error);
        // 如果API失败，提供默认城市数据
        this.cityOptions = this.getDefaultCityOptions();
      }
    },

    // 检查行程安排数据
    checkScheduleData() {
      console.log('=== 检查行程安排数据 ===');
      console.log('formData.scheduleDays:', this.formData.scheduleDays);

      if (this.formData.scheduleDays && Array.isArray(this.formData.scheduleDays)) {
        this.formData.scheduleDays.forEach((day) => {
          console.log(`第${day.day}天:`, day);
          console.log(`第${day.day}天schedules长度:`, day.schedules ? day.schedules.length : 0);
          console.log(`第${day.day}天schedules内容:`, day.schedules);

          if (day.schedules && Array.isArray(day.schedules)) {
            day.schedules.forEach((schedule, scheduleIndex) => {
              console.log(`  安排${scheduleIndex + 1}:`, schedule);
            });
          }
        });
      }
      console.log('=== 检查完毕 ===');
    },

    // 获取默认城市数据
    getDefaultCityOptions() {
      return [
        {
          provinceCode: '110000',
          provinceName: '北京市',
          cities: [
            { cityCode: '110100', cityName: '北京市', cityInitial: 'B' }
          ]
        },
        {
          provinceCode: '310000',
          provinceName: '上海市',
          cities: [
            { cityCode: '310100', cityName: '上海市', cityInitial: 'S' }
          ]
        },
        {
          provinceCode: '340000',
          provinceName: '安徽省',
          cities: [
            { cityCode: '341100', cityName: '滁州市', cityInitial: 'C' },
            { cityCode: '341700', cityName: '池州市', cityInitial: 'C' }
          ]
        },
        {
          provinceCode: '440000',
          provinceName: '广东省',
          cities: [
            { cityCode: '440100', cityName: '广州市', cityInitial: 'G' },
            { cityCode: '440300', cityName: '深圳市', cityInitial: 'S' }
          ]
        }
      ];
    },

    // 获取行程类型标签样式
    getScheduleTypeTag(type) {
      const tagMap = {
        'meal': 'success',
        'transport': 'info',
        'attraction': 'warning',
        'accommodation': 'primary',
        'freeActivity': ''
      };
      return tagMap[type] || '';
    },

    // 获取行程类型文本
    getScheduleTypeText(type) {
      const textMap = {
        'meal': '餐食',
        'transport': '交通',
        'attraction': '景点',
        'accommodation': '住宿',
        'freeActivity': '自由活动'
      };
      return textMap[type] || type;
    },

    // 格式化时长显示
    formatDuration(minutes) {
      if (!minutes) return '';
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      let result = '';
      if (hours > 0) result += `${hours}小时`;
      if (mins > 0) result += `${mins}分钟`;
      return result || '0分钟';
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.form-header h2 {
  margin: 0;
  color: #303133;
}

.form-actions {
  display: flex;
  gap: 10px;
}

.form-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  color: #303133;
}

.cover-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.cover-uploader .el-upload:hover {
  border-color: #409EFF;
}

.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.cover-image {
  width: 178px;
  height: 178px;
  display: block;
}

.schedule-day {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  background-color: #fafafa;
}

.day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.day-header h4 {
  margin: 0;
  color: #303133;
}

.schedule-details {
  margin-top: 20px;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
  color: #606266;
}

.schedule-item {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.schedule-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.schedule-info {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.schedule-time {
  font-weight: bold;
  color: #409eff;
}

.schedule-location {
  color: #606266;
}

.schedule-duration {
  color: #909399;
  font-size: 12px;
}

.schedule-content {
  color: #303133;
  line-height: 1.5;
  margin: 5px 0;
}

.schedule-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.duration-input {
  display: flex;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}
</style>
