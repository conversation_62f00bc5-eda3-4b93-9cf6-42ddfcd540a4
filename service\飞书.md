#飞猪回调接口

接口规范
飞猪调用接入方接口以及接入方请求飞猪网关接口，都需遵循一下规范：

只能使用 HTTPS 协议 POST 方法发出请求。
Content-Type 必须为 application/json。
请求飞猪网关时，建议请求头携带 Accept-Encoding 为 gzip/deflate/br 字段。
飞猪网关返回的响应中，飞猪响应头将携带 Content-Encoding 字段，客户端自行解压。
请求头必须携带签名参数，签名生成逻辑见下文。
接口响应格式规范
为了保证数据交互的一致性和可读性，飞猪平台规定了请求接入方接口时的标准响应格式，以及接入方请求飞猪网关时返回的标准格式。以下是这些标准格式的详细说明。

接口处理成功时，返回的结果：
```json
{
  "success": true,
  "code": "ok",
  "message": "Processing successful",
  "data": { }
}
```
接口处理失败时，返回的结果：
```json
{
"success": false,
"code": "PARAMS_ERROR",
"message": "Signature information has expired, please update the signature information and try again"
}
```
其中，code码与message为用于表达接口处理成功或失败的相关信息。

签名规则,签名demo：
```php
<?php
/**
 * 生成签名
 * Generate signature
 * 
 * @param string $appKey 应用的公钥 (Application's public key)
 * @param string $appSecret 应用的私钥 (Application's private key)
 * @param string $httpMethod HTTP方法（GET, POST等） (HTTP method (GET, POST, etc.))
 * @param string $url 请求的URL (Request URL)
 * @param string $contentString 请求内容的JSON字符串 (JSON string of request content)
 * @param int $timestamp 时间戳 (Timestamp)
 * @param string $nonce 随机字符串，保证请求的唯一性 (Random string to ensure request uniqueness)
 * @param string $optionsKeysString 其他选项键值对的字符串 (String of other option key-value pairs)
 * @return string 生成的签名字符串 (Generated signature string)
 */
function generateSignature($appKey, $appSecret, $httpMethod, $url, $contentString, $timestamp, $nonce, $optionsKeysString) {
    // 依据content字符串生成对应的SHA256哈希值，并转为小写形式
    // Generate the corresponding SHA256 hash based on the content string and convert it to lowercase
    $contentHash = strtolower(hash('sha256', $contentString));

    // 生成待签名字符串，由HTTP方法、URL、内容哈希和其他选项组成，以换行符分隔
    // Generate the string to be signed, composed of HTTP method, URL, content hash, and other options, separated by newlines
    $stringToSign = implode("\n", [$httpMethod, $url, $contentHash, $optionsKeysString]);

    // 生成待加密的明文，由公钥、时间戳、随机字符串和待签名字符串组成
    // Generate the plaintext to be encrypted, composed of public key, timestamp, random string, and the string to be signed
    $plainText = $appKey . $timestamp . $nonce . $stringToSign;

    // 使用HMAC-SHA256算法生成签名，并将其转换为大写
    // Generate the signature using HMAC-SHA256 algorithm and convert it to uppercase
    $sign = strtoupper(hash_hmac('sha256', $plainText, $appSecret));

    return $sign;
}

// 函数使用示例
// Example of function usage
$appKey = 'test-ak1'; // 应用的公钥 (Application's public key)
$appSecret = 'test-ak1-password'; // 应用的私钥 (Application's private key)
$httpMethod = 'POST'; // 使用的HTTP方法 (HTTP method used)
$url = '/'; // 请求的URL (Request URL)
// 请求内容的JSON字符串 (JSON string of request content)
$contentString = json_encode([
    "success" => "true",
    "code" => 200,
    "message" => "ok",
    "data" => [
        "testData" => "testData"
    ]
]);
$timestamp = 1710122376621; // 时间戳 (Timestamp)
$nonce = '2ebc2be3-65a5-4f42-9d04-983ba0033806'; // 随机字符串 (Random string)
// 其他选项的键值对，以换行符分隔 (Key-value pairs of other options, separated by newlines)
$optionsKeysString = implode("\n", ["key1:value1", "key2:value2"]);

// 调用函数生成签名
// Call the function to generate the signature
$sign = generateSignature($appKey, $appSecret, $httpMethod, $url, $contentString, $timestamp, $nonce, $optionsKeysString);

// 输出生成的签名
// 1.7 - 8 - 5 = 4
// 12+17=29
// 22+4 = 26
// Output the generated signature
// 签名打印结果为：865797CD2EBF1EC6934FD139480E2636D115F04C279AD9DF41937DCF9F10799C
// The printed signature result is: 865797CD2EBF1EC6934FD139480E2636D115F04C279AD9DF41937DCF9F10799C
echo $sign;
```


## 1、创建订单
```json
{
  "bizType": 2,
  "fliggyOrderId": "string",
  "vendor": {
    "type": "string",
    "env": "sandbox",
    "extend": {
      "apiKey": "string",
      "apiSecret": "string"
    }
  },
  "contacts": {
    "mobile": "string",
    "email": "string"
  },
  "travellers": [
    {
      "certificateType": 0,
      "certificateId": "string",
      "name": "string",
      "firstName": "string",
      "lastName": "string",
      "mobile": "string",
      "mobilePrefix": "string",
      "sex": "M",
      "email": "string",
      "country": "string",
      "birth": "string",
      "travellerDynamicParameters": null,
      "subProductList": [
        null
      ],
      "type": 0,
      "extendInfos": [
        {
          "propertyName": "string",
          "propertyValue": null
        }
      ]
    }
  ],
  "adultNumber": 0,
  "childNumber": 0,
  "productList": [
    {
      "productId": "string",
      "quantity": 0,
      "totalPrice": 0,
      "price": 0,
      "currencyType": "string",
      "travelDate": "string",
      "crowdTypeId": "ADULT",
      "totalCouponPrice": 0,
      "totalActualPrice": 0
    }
  ],
  "confirmTime": "2024-10-15 13:00:00",
  "extendMap": null
}
```
字段解析：
```text
bizType
required
number
业务类型，对于线路业务，固定为 2

Value: 2
fliggyOrderId
required
string
飞猪订单号

vendor
required
object (vendor)
系统商信息

type
required
string
系统商标识

env
required
string
Enum: "sandbox" "production"
环境标识。

"sandbox"：测试环境；
"production"：正式环境；
extend
required
object
拓展信息

contacts
object
联系人

mobile
string
手机号

email
string
电子邮件

travellers
required
Array of objects (TransactionTraveller)
出行人列表

Array
certificateType
number (CARD_TYPE)
Enum: 0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18
证件类型。

0: 身份证;
1: 护照;
2: 学生证;
3: 军人证;
4: 回乡证;
5: 台胞证;
6: 港澳通行证;
7: 国际海员;
8: 外国人永久居住证;
10: 警官证;
11: 士兵证;
12: 台湾通行证;
13: 入台证;
14: 户口本;
15: 出生证明;
16: 驾驶证;
17: 港澳居民居住证;
18: 台湾居民居住证;
9: 其他证件;
certificateId
string
证件号码

name
string
姓名

firstName
string
名

lastName
string
姓

mobile
string
手机号码

mobilePrefix
string
手机号码的国际区号

sex
string
Enum: "M" "F"
性别。

"M": 男；
"F": 女。
email
string
电子邮件

country
string
国家地区代码

birth
string
生日

travellerDynamicParameters
any
k-v结构的对象，用来传递出行人动态信息。该字段较为特殊，开放平台接入方如果需要使用，请与飞猪对接同学确认。

subProductList
Array of any
出行人所关联的子产品

type
number (TRAVELLER_TYPE)
Enum: 0 1 2
出行人，联系人类型。

0：成人；
1：儿童；
extendInfos
Array of objects (ExtendInfos)
拓展属性列表

adultNumber
number
成人数

childNumber
number
儿童数

productList
required
Array of objects
线路交易产品信息列表

Array
productId
required
string
线路产品id

quantity
required
number
数量

totalPrice
required
number
订单总价，货币作为单位，一般为分

price
required
number
单价，分作为单位

currencyType
string
币种

travelDate
required
string (DateString) ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01...Show pattern
出团日期，格式为：YYYY-MM-DD

crowdTypeId
required
string (LINE_CROWD_TYPE)
Enum: "ADULT" "CHILD" "DIFF"
人群类型。

"ADULT"：成人；
"CHILD"：儿童；
"DIFF"：单房差。
totalCouponPrice
number
总优惠金额，分

totalActualPrice
number
总实收金额，分

confirmTime
string (DateTimeString) ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01...Show pattern
二次确认时间，格式为：YYYY-MM-DD HH:mm:ss。 当没有二次确认时间时，在飞猪内部为已确认状态，飞猪认为该订单为待出行

extendMap
any
系统商拓展信息

Responses
200 成功
RESPONSE SCHEMA: application/json
success
required
boolean
创单是否成功

code
required
string
系统商接口的状态码

message
required
string
响应结果的描述，例如成功、失败、失败原因等

data
object
{success} 值为 true 时，必须返回 {data} 字段，包含创单相关信息

orderId
string
系统商内部订单id。建议返回该字段，后续会在财务对账等事项中用到

fliggyOrderId
string
飞猪订单id

extendMap
any
扩展属性

```

## 订单查询 
回调数据
```json
{
  "vendor": {
    "type": "string",
    "env": "sandbox",
    "extend": {
      "apiKey": "string",
      "apiSecret": "string"
    }
  },
  "bizType": 2,
  "fliggyOrderId": "string",
  "orderId": "string",
  "extendMap": null
}
```
字段解析
```text
REQUEST BODY SCHEMA: application/json
vendor
required
object (vendor)
系统商信息

type
required
string
系统商标识

env
required
string
Enum: "sandbox" "production"
环境标识。

"sandbox"：测试环境；
"production"：正式环境；
extend
required
object
拓展信息

bizType
required
number
业务类型，对于线路业务，固定为 2

Value: 2
fliggyOrderId
required
string
飞猪订单ID

orderId	
string
系统商订单ID

extendMap	
any
系统商个性化请求参数

Responses
200 成功
RESPONSE SCHEMA: application/json
success
required
boolean
是否成功

code
required
string
系统商接口的状态码

message
required
string
响应结果的描述，例如成功、失败、失败原因等

data	
object
{success} 值为 true 时，必须返回 {data} 字段，包含订单相关信息
```

### 退款
- 回调数据
```json
{
  "bizType": 2,
  "fliggyOrderId": "string",
  "orderId": "string",
  "vendor": {
    "type": "string",
    "env": "sandbox",
    "extend": {
      "apiKey": "string",
      "apiSecret": "string"
    }
  },
  "refundType": 1,
  "refundReason": "string",
  "refundFee": 0,
  "extendMap": null
}
```
- 字段解析
```text
REQUEST BODY SCHEMA: application/json
bizType
required
number
业务类型，对于线路业务，固定为 2

Value: 2
fliggyOrderId
required
string
飞猪订单号

orderId	
string
系统商订单号

vendor
required
object (vendor)
系统商信息

type
required
string
系统商标识

env
required
string
Enum: "sandbox" "production"
环境标识。

"sandbox"：测试环境；
"production"：正式环境；
extend
required
object
拓展信息

refundType	
number (TRANSACTION_REFUND_TYPE)
Enum: 1 2
退款类型。

1：退款退单；
2：退款不退单；
refundReason	
string
退款原因

refundFee	
number
退款金额，单位为分

extendMap	
any
系统商拓展信息

Responses
200 成功
RESPONSE SCHEMA: application/json
success
required
boolean
接口调用是否成功

code
required
string
系统商响应的状态码

message
required
string
响应结果的描述，例如成功、失败、失败原因等

data	
object
{success} 值为 true 时，必须返回 {data} 字段，包含退款相关信息

fliggyOrderId	
string
飞猪订单号

orderId	
string
系统商订单号

refundStatus
required
number (REFUND_STATUS)
Enum: 1 2 3
退款状态。（当不支持退款或退款失败时该字段应返回 2）

1：退款成功；
2：退款失败；
3：退款处理中；
extendMap	
any
系统商拓展信息
```

## 修改订单
- 回调数据
```json
{
  "vendor": {
    "type": "string",
    "env": "sandbox",
    "extend": {
      "apiKey": "string",
      "apiSecret": "string"
    }
  },
  "fliggyOrderId": "string",
  "orderId": "string",
  "travelDate": "string",
  "travellers": [
    {
      "certificateType": 0,
      "certificateId": "string",
      "name": "string",
      "firstName": "string",
      "lastName": "string",
      "mobile": "string",
      "mobilePrefix": "string",
      "sex": "M",
      "email": "string",
      "country": "string",
      "birth": "string",
      "travellerDynamicParameters": null,
      "subProductList": [
        null
      ],
      "type": 0,
      "extendInfos": [
        {
          "propertyName": "string",
          "propertyValue": null
        }
      ]
    }
  ],
  "orderInfo": {
    "rentCarInfo": {
      "carStartTime": "string",
      "carStartMin": "string",
      "placeTake": "string",
      "carEndTime": "string",
      "carEndMin": "string",
      "rentDays": "string",
      "packageNum": "string"
    },
    "contactInfo": {
      "weixin": "string",
      "emergencyPhoneNumber": "string",
      "email": "string",
      "overseaPhoneNumber": "string",
      "shippingAddress": "string"
    },
    "dayTripInfo": {
      "gatherTime": "string",
      "gatherAddress": "string",
      "pickupDate": "string",
      "pickupPlace": "string",
      "diningTime": "string",
      "abroadDate": "string",
      "abroadCountry": "string",
      "planScenic": "string",
      "memo": "string"
    },
    "transferInfo": {
      "goAirlineNo": "string",
      "goDate": "string",
      "flightLandingTime": "string",
      "returnHotelName": "string",
      "returnHotelAddr": "string",
      "returnHotelTel": "string",
      "placeOn": "string",
      "backAirlineNo": "string",
      "backDate": "string",
      "backAirlineTime": "string",
      "departHotelName": "string",
      "departHotelAddr": "string",
      "departHotelTel": "string",
      "placeOff": "string",
      "passengerNum": "string",
      "landingVisa": "0"
    },
    "travellerInfo": {
      "totalPeople": "string",
      "adultsNumber": "string",
      "childNumber": "string",
      "babyNumber": "string",
      "oldmanNumber": "string",
      "studentPeople": "string"
    },
    "agreement": "string"
  },
  "bizType": 2,
  "extendMap": null
}
```
- 字段解析
```text
REQUEST BODY SCHEMA: application/json
vendor
required
object (vendor)
系统商信息

type
required
string
系统商标识

env
required
string
Enum: "sandbox" "production"
环境标识。

"sandbox"：测试环境；
"production"：正式环境；
extend
required
object
拓展信息

apiKey
required
string
系统商秘钥-key

apiSecret
required
string
系统商密钥-secret

fliggyOrderId
required
string
飞猪订单id

orderId	
string
供应商内部订单id，有些供应商不会返回自己内部的订单id

travelDate	
string
更新后的出行日期，格式为：YYYY-MM-DD

travellers	
Array of objects (TransactionTraveller)
更新后的出行人列表

Array 
certificateType	
number (CARD_TYPE)
Enum: 0 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18
证件类型。

0: 身份证;
1: 护照;
2: 学生证;
3: 军人证;
4: 回乡证;
5: 台胞证;
6: 港澳通行证;
7: 国际海员;
8: 外国人永久居住证;
10: 警官证;
11: 士兵证;
12: 台湾通行证;
13: 入台证;
14: 户口本;
15: 出生证明;
16: 驾驶证;
17: 港澳居民居住证;
18: 台湾居民居住证;
9: 其他证件;
certificateId	
string
证件号码

name	
string
姓名

firstName	
string
名

lastName	
string
姓

mobile	
string
手机号码

mobilePrefix	
string
手机号码的国际区号

sex	
string
Enum: "M" "F"
性别。

"M": 男；
"F": 女。
email	
string
电子邮件

country	
string
国家地区代码

birth	
string
生日

travellerDynamicParameters	
any
k-v结构的对象，用来传递出行人动态信息。该字段较为特殊，开放平台接入方如果需要使用，请与飞猪对接同学确认。

subProductList	
Array of any
出行人所关联的子产品

type	
number (TRAVELLER_TYPE)
Enum: 0 1 2
出行人，联系人类型。

0：成人；
1：儿童；
extendInfos	
Array of objects (ExtendInfos)
拓展属性列表

Array 
propertyName
required
string
属性名

propertyValue
required
any
属性值

orderInfo	
object
订单信息

rentCarInfo	
object
租车/包车信息

contactInfo	
object
联系人信息

weixin	
string
微信号

emergencyPhoneNumber	
string
紧急联系方式

email	
string
电子邮箱

overseaPhoneNumber	
string
境外手机号

shippingAddress	
string
收货地址

dayTripInfo	
object
日游信息

transferInfo	
object
接送机信息

travellerInfo	
object
同行人信息

totalPeople	
string
总人数

adultsNumber	
string
成人数

childNumber	
string
儿童数

babyNumber	
string
婴儿数

oldmanNumber	
string
老人数

studentPeople	
string
学生数

agreement	
string
更新后的合同信息

bizType
required
number
业务类型，对于线路业务，固定为 2

Value: 2
extendMap	
any
系统商个性化请求参数

Responses
200 成功
RESPONSE SCHEMA: application/json
success
required
boolean
是否成功

code
required
string
一般填写供应商接口的状态码

message
required
string
失败时返回对应的失败信息
```
