<?php
declare (strict_types=1);

namespace base\AuditLog;

use base\AuditLog\traits\Field;

class AirField
{
    use Field;

    /**
     * @var string 索引字段(不可设置为字段别名)
     */
    private string $index = '';

    /**
     * 当前模型的全局唯一索引键
     *
     * @param string $index
     *
     * @return $this
     */
    public function setIndex(string $index): self
    {
        $this->index = $index;

        return $this;
    }

    public function getIndex(): string
    {
        return $this->index;
    }
}