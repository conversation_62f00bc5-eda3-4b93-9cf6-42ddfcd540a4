<?php

namespace app\middleware;

use Firebase\JWT\JWT;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\Key;
use Webman\MiddlewareInterface;
use Webman\Http\Response;
use <PERSON>man\Http\Request;

class wechatAuth implements MiddlewareInterface
{
    public function process(Request $request, callable $next): Response {
        // 设置允许跨域的来源
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Content-Type,Referer, Authorization, X-Requested-With,User-Agent,Token,Accept-Language");
        return $next($request);
    }
}