<?php
namespace app\model;

class OtaRouteGroups extends base
{
    protected $table = 'ota_route_groups';

    /**
     * 获取产品ID数组
     */
    public function getProductIdsArrayAttr()
    {
        if (empty($this->product_ids)) {
            return [];
        }
        return array_filter(explode(',', $this->product_ids));
    }

    /**
     * 获取产品列表
     */
    public function getProductsAttr()
    {
        $productIds = $this->getProductIdsArrayAttr();
        if (empty($productIds)) {
            return [];
        }

        return Products::whereIn('id', $productIds)->select();
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr()
    {
        return $this->status == 1 ? '启用' : '禁用';
    }

    /**
     * 获取包含的产品名称（用于展示）
     */
    public function getProductNamesAttr()
    {
        $products = $this->getProductsAttr();
        if (empty($products)) {
            return '暂无线路';
        }

        $names = [];
        foreach ($products as $product) {
            $names[] = $product->product_name;
        }

        return implode('、', array_slice($names, 0, 3)) . (count($names) > 3 ? '等' : '');
    }

    /**
     * 获取启用的线路组列表
     * @return array
     */
    public static function getActiveGroups($shopId)
    {
        return self::where('status', 1)
                    ->where('shop_id', $shopId)
                   ->order('sort_order', 'desc')
                   ->order('id', 'asc')
                   ->select();
    }

    /**
     * 根据产品ID获取匹配的线路组
     * @param array $productIds
     * @return array
     */
    public static function getGroupsByProductIds($productIds, $shopId)
    {
        if (empty($productIds)) {
            return [];
        }

        $groups = self::getActiveGroups($shopId);
        $matchedGroups = [];

        foreach ($groups as $group) {
            $groupProductIds = $group->getProductIdsArrayAttr();
            if (!empty($groupProductIds)) {
                $intersection = array_intersect($productIds, $groupProductIds);
                if (!empty($intersection)) {
                    $matchedGroups[] = $group;
                }
            }
        }

        return $matchedGroups;
    }

    /**
     * 检查产品ID是否属于指定线路组
     * @param int $groupId
     * @param int $productId
     * @return bool
     */
    public static function isProductInGroup($groupId, $productId)
    {
        $group = self::find($groupId);
        if (!$group || $group->status != 1) {
            return false;
        }

        $productIds = $group->getProductIdsArrayAttr();
        return in_array($productId, $productIds);
    }

    /**
     * 根据线路组ID获取所有产品ID
     * @param array $groupIds
     * @return array
     */
    public static function getProductIdsByGroupIds($groupIds)
    {
        if (empty($groupIds)) {
            return [];
        }

        $groups = self::whereIn('id', $groupIds)->where('status', 1)->select();
        $allProductIds = [];

        foreach ($groups as $group) {
            $productIds = $group->getProductIdsArrayAttr();
            $allProductIds = array_merge($allProductIds, $productIds);
        }

        return array_unique($allProductIds);
    }

    /**
     * 比较数据变更
     * @param array $oldData 原数据
     * @param array $newData 新数据
     * @return array
     */
    public static function compareData($oldData, $newData)
    {
        $changes = [];
        $fieldNames = [
            'group_name' => '组名',
            'product_ids' => '线路权限',
            'status' => '状态',
            'sort_order' => '排序权重',
            'description' => '描述',
        ];

        // 权限字段，需要特殊处理变更记录
        $permissionFields = ['product_ids', 'product_xs_ids'];

        foreach ($fieldNames as $field => $label) {
            if (!isset($oldData[$field]) || !isset($newData[$field])) {
                continue;
            }
            $oldValue = $oldData[$field] ?? '';
            $newValue = $newData[$field] ?? '';

            // 权限字段特殊处理
            if (in_array($field, $permissionFields)) {
                $permissionChange = self::comparePermissionField($field, $oldValue, $newValue);
                if (!empty($permissionChange)) {
                    $changes[$field] = [
                        'label' => $label,
                        'old_value' => '',
                        'new_value' => $permissionChange['new_value'],
                        'changes' => $permissionChange['changes']
                    ];
                }
            } elseif ($oldValue != $newValue) {
                $changes[$field] = [
                    'label' => $label,
                    'old_value' => self::formatFieldValue($field, $oldValue),
                    'new_value' => self::formatFieldValue($field, $newValue)
                ];
            }
        }

        return $changes;
    }

    /**
     * 格式化字段值显示
     * @param string $field 字段名
     * @param mixed $value 字段值
     * @return string
     */
    public static function formatFieldValue($field, $value)
    {
        if (empty($value) && $value !== '0' && $value !== 0) {
            return '未设置';
        }

        switch ($field) {
            case 'status':
                return $value == 1 ? '是' : '否';

            case 'product_ids':
            case 'product_xs_ids':
                return self::formatProductNames($value);

            default:
                return $value;
        }
    }

    /**
     * 格式化产品名称显示
     * @param string $productIds 产品ID列表，逗号分隔
     * @return string
     */
    private static function formatProductNames($productIds)
    {
        if (empty($productIds)) {
            return '未设置';
        }

        $ids = array_filter(explode(',', $productIds));
        if (empty($ids)) {
            return '未设置';
        }

        try {
            $products = \app\model\Products::whereIn('id', $ids)
                ->field('product_name')
                ->select();

            if (empty($products)) {
                return '产品不存在(' . $productIds . ')';
            }

            $names = [];
            foreach ($products as $product) {
                $names[] = $product->product_name;
            }

            return implode('、', $names);
        } catch (\Exception $e) {
            return '产品ID(' . $productIds . ')';
        }
    }

    /**
     * 比较权限字段变更，只记录增加和删除的部分
     * @param string $field 字段名
     * @param string $oldValue 旧值
     * @param string $newValue 新值
     * @return array
     */
    private static function comparePermissionField($field, $oldValue, $newValue)
    {
        $oldIds = array_filter(explode(',', $oldValue ?? ''));
        $newIds = array_filter(explode(',', $newValue ?? ''));

        // 计算增加和删除的ID
        $addedIds = array_diff($newIds, $oldIds);
        $removedIds = array_diff($oldIds, $newIds);

        if (empty($addedIds) && empty($removedIds)) {
            return [];
        }

        $changes = [];
        $oldText = '';
        $newText = '';

        // 处理删除的项目
        if (!empty($removedIds)) {
            $removedNames = self::getPermissionNames($field, $removedIds);
            $changes['removed'] = $removedNames;
            $oldText .= '删除: ' . implode('、', $removedNames);
        }

        // 处理添加的项目
        if (!empty($addedIds)) {
            $addedNames = self::getPermissionNames($field, $addedIds);
            $changes['added'] = $addedNames;
            if (!empty($oldText)) {
                $newText = $oldText . ' | ';
            }
            $newText .= '新增: ' . implode('、', $addedNames);
        } else {
            $newText = $oldText;
        }

        return [
            'old_value' => self::formatFieldValue($field, $oldValue),
            'new_value' => $newText,
            'changes' => $changes
        ];
    }

    /**
     * 获取权限名称
     * @param string $field 字段名
     * @param array $ids ID数组
     * @return array
     */
    private static function getPermissionNames($field, $ids)
    {
        if (empty($ids)) {
            return [];
        }

        try {
            switch ($field) {
                case 'product_ids':
                case 'product_xs_ids':
                    $items = \app\model\Products::whereIn('id', $ids)
                        ->field('product_name')
                        ->select();
                    return array_column($items->toArray(), 'product_name');

                case 'route_group_ids':
                    $items = \app\model\OtaRouteGroups::whereIn('id', $ids)
                        ->field('group_name')
                        ->select();
                    return array_column($items->toArray(), 'group_name');

                default:
                    return $ids;
            }
        } catch (\Exception $e) {
            return $ids;
        }
    }
}
