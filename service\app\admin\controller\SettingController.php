<?php

namespace app\admin\controller;

use app\model\Settings;
use support\Redis;
use support\Request;

class SettingController extends base {
    // 境内
    const SETTING_DOMESTIC = 'setting:domestic';
    // 境外
    const SETTING_ABROAD = 'setting:abroad';
    /**
     * 配置合同获取
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getContractSetting(Request $request) {
        $shopId = $request->admin->shop_id;
        $domestic = Redis::get(self::SETTING_DOMESTIC . $shopId) ?? '';
        $abroad = Redis::get(self::SETTING_ABROAD . $shopId) ?? '';
        return $this->success(compact('domestic', 'abroad'));
    }

    /**
     * 合同配置保存
     */
    public function saveContractSetting(Request $request) {
        $shopId = $request->admin->shop_id;
        if (!empty($request->post('domestic'))) {
            Redis::set(self::SETTING_DOMESTIC . $shopId, $request->post('domestic'));
        }
        if (!empty($request->post('abroad'))) {
            Redis::set(self::SETTING_ABROAD . $shopId, $request->post('abroad'));
        }

        return $this->success($request->post());
    }
}
