/*
 Navicat Premium Dump SQL

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 80012 (8.0.12)
 Source Host           : 127.0.0.1:3306
 Source Schema         : travel

 Target Server Type    : MySQL
 Target Server Version : 80012 (8.0.12)
 File Encoding         : 65001

 Date: 04/08/2025 13:46:35
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_logs
-- ----------------------------
DROP TABLE IF EXISTS `admin_logs`;
CREATE TABLE `admin_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员id',
  `ip` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '登陆ip',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登陆时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 121 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for admins
-- ----------------------------
DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `username` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '账号',
  `password` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `avatar` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像',
  `mobile` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `remember_token` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态：0禁用 1启用',
  `is_super` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否管理员',
  `is_order` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否分配订单',
  `is_private` tinyint(4) NULL DEFAULT 0 COMMENT '是否分单',
  `is_anchor` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否主播',
  `is_franchisee` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否加盟商',
  `shop_id` int(11) NOT NULL COMMENT '门店id',
  `is_xs` tinyint(4) NULL DEFAULT 0,
  `start_work_time` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '上线时间',
  `end_work_time` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '停止分单的时间',
  `last_work_time` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '下线时间',
  `order_num` int(10) UNSIGNED NULL DEFAULT 99999999 COMMENT '订单数量: 99999999为默认不参与逻辑',
  `product_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '产品ID，用英文逗号隔开',
  `product_xs_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '线索产品id',
  `create_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `route_type` tinyint(4) NULL DEFAULT 10 COMMENT '线路类型（10-境内跟团，20-境外跟团）',
  `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '类型（0-管理员，1-客服，2-主播，3-中控）',
  `wechat` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信号',
  `wechat_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '微信图片',
  `job` tinyint(1) NOT NULL DEFAULT 0 COMMENT '职位',
  `pid` int(11) NOT NULL DEFAULT 0 COMMENT '上级ID',
  `service_promise` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '服务承诺',
  `dy_nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '抖音昵称',
  `ip` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后登陆ip',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后登陆地址',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `admin_users_username_unique`(`username` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 62 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'cms管理员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for announcements
-- ----------------------------
DROP TABLE IF EXISTS `announcements`;
CREATE TABLE `announcements`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '最后一次修改ID',
  `create_time` bigint(20) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` bigint(20) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for backs
-- ----------------------------
DROP TABLE IF EXISTS `backs`;
CREATE TABLE `backs`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '想拉回的id',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '被拉回的订单',
  `is_private` tinyint(4) NULL DEFAULT 0,
  `status` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态: 0申请中 1同意 2拒绝 3取消 ',
  `admin` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '被拉回的管理员id',
  `apply_id` int(11) NOT NULL COMMENT '申请者id',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2881 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '拉回订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for blacks
-- ----------------------------
DROP TABLE IF EXISTS `blacks`;
CREATE TABLE `blacks`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `mobile` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1055 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '黑名单,短信' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for call_cloudcall_seats
-- ----------------------------
DROP TABLE IF EXISTS `call_cloudcall_seats`;
CREATE TABLE `call_cloudcall_seats`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` int(11) NOT NULL,
  `from_exten` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '坐席工号',
  `exten_type` enum('gateway','sip','local') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'local' COMMENT '接听方式Local为“手机”',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '1正常0关闭',
  `cloud` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'tycc100' COMMENT '外呼引擎',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '云呼坐席管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for call_records
-- ----------------------------
DROP TABLE IF EXISTS `call_records`;
CREATE TABLE `call_records`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `caller` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主叫号码',
  `callee` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '被叫号码',
  `platform` tinyint(4) NOT NULL DEFAULT 1 COMMENT '云呼平台 1:网易七鱼 2:容联七陌',
  `call_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '云呼平台返回的呼叫ID',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '呼叫状态 1:呼叫中 2:已接通 3:已拒绝 4:占线 5:无人接听 6:呼叫失败 7:已挂断',
  `call_time` datetime NOT NULL COMMENT '发起呼叫时间',
  `start_time` datetime NULL DEFAULT NULL COMMENT '通话开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '通话结束时间',
  `duration` int(11) NOT NULL DEFAULT 0 COMMENT '通话时长(秒)',
  `error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '错误信息',
  `extra_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '额外数据(JSON)',
  `response_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '平台响应数据(JSON)',
  `callback_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '回调数据(JSON)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_admin_id`(`admin_id` ASC) USING BTREE,
  INDEX `idx_call_id`(`call_id` ASC) USING BTREE,
  INDEX `idx_callee`(`callee` ASC) USING BTREE,
  INDEX `idx_call_time`(`call_time` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_platform`(`platform` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '云呼记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cities
-- ----------------------------
DROP TABLE IF EXISTS `cities`;
CREATE TABLE `cities`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_id` int(11) NOT NULL COMMENT '国家ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市英文名称',
  `city_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地理名称ID',
  `poi_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'POI ID',
  `initial` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市名称首字母',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_country_id`(`country_id` ASC) USING BTREE,
  INDEX `idx_initial`(`initial` ASC) USING BTREE,
  INDEX `idx_geo_name_id`(`city_code` ASC) USING BTREE,
  INDEX `idx_poi_id`(`poi_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 469 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '城市表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for city_info
-- ----------------------------
DROP TABLE IF EXISTS `city_info`;
CREATE TABLE `city_info`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `province_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省份名称',
  `province_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省份编码',
  `city_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市名称',
  `city_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市编码',
  `city_initial` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市名称大写首字母',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_code`(`city_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 368 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '城市信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for contract
-- ----------------------------
DROP TABLE IF EXISTS `contract`;
CREATE TABLE `contract`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `product_id` int(11) NULL DEFAULT NULL,
  `ye_license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营业执照',
  `jy_license` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经营许可证',
  `pay_qr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收款二维码',
  `hotel_pictures` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '酒店图片',
  `restaurant_picture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '餐厅图片',
  `template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同模版',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `create_time` int(11) NULL DEFAULT NULL,
  `update_time` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for countries
-- ----------------------------
DROP TABLE IF EXISTS `countries`;
CREATE TABLE `countries`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '国家名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国家英文名称',
  `initial` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国家名称首字母',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_initial`(`initial` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 188 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国家表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dy_order_appointments
-- ----------------------------
DROP TABLE IF EXISTS `dy_order_appointments`;
CREATE TABLE `dy_order_appointments`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `appoint_order_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '平台预约号',
  `dy_order_id` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单ID',
  `source_order_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '预约订单所归属的预售订单id',
  `presale_coupon_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '旅行社预售券ID',
  `out_presale_coupon_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '商家侧预售券ID',
  `biz_type` int(11) NULL DEFAULT 0 COMMENT '旅行社预约单：3012',
  `pay_amount` int(11) NOT NULL DEFAULT 0 COMMENT '补差金额，单位为分',
  `actual_amount` int(11) NOT NULL DEFAULT 0 COMMENT '用户实际支付+平台成本补贴优惠',
  `merchant_discount_amount` int(11) NOT NULL DEFAULT 0 COMMENT '商家优惠金额',
  `discount_amount` int(11) NOT NULL DEFAULT 0 COMMENT '优惠金额',
  `original_amount` int(11) NOT NULL DEFAULT 0 COMMENT '订单原始金额（预约补差原始金额）',
  `pay_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '支付信息',
  `book_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '用户预约信息',
  `buyer_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '联系人信息',
  `remark_from_guest` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客人备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '抖音订单预约表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dy_order_products
-- ----------------------------
DROP TABLE IF EXISTS `dy_order_products`;
CREATE TABLE `dy_order_products`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dy_order_id` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `sku_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '预售券ID',
  `product_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '预售券ID',
  `out_product_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '预售券商家ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '商品名称',
  `category_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '旅行社商品类目ID',
  `category_full_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '商品类目全称',
  `travel_details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '行程详情',
  `commodity` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '套餐详情',
  `is_superimposed_discounts` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '可以享受店内其他优惠',
  `date_add_price_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '日期加价规则',
  `user_add_price_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '出行人加价政策',
  `room_add_price_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '单房差加价政策',
  `room_upgrade_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '房型升级政策',
  `add_price_policy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '线下加价政策',
  `description_rich_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述属性',
  `fee_include` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '费用包含',
  `fee_not_include` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '费用不包含',
  `self_pay_expense_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '自理费用政策',
  `use_date` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '可使用日期',
  `earliest_appointment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '最早可预约规则',
  `appointment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '最晚可预约规则',
  `refund_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '退款规则',
  `refund_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '退款规则描述',
  `merchant_break_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '商家违约规则',
  `merchant_break_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '商家违约描述',
  `presale_appointment_cancel_policy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '取消预约策略',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '抖音订单商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dy_orders
-- ----------------------------
DROP TABLE IF EXISTS `dy_orders`;
CREATE TABLE `dy_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '订单号',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单状态（0-初始，10-已支付，20-已接单，30-已取消，40-已退款）',
  `dy_order_id` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '抖音订单号',
  `biz_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '旅行社预售券：3011',
  `total_amount` int(11) NOT NULL DEFAULT 0 COMMENT '总原始价格',
  `total_coupon_count` int(11) NOT NULL DEFAULT 0 COMMENT '总券张数',
  `each_coupon_amount` int(11) NOT NULL DEFAULT 0 COMMENT '单张券原始价格',
  `pay_amount` int(11) NOT NULL DEFAULT 0 COMMENT '用户实付金额',
  `actual_amount` int(11) NOT NULL DEFAULT 0 COMMENT '订单实收金额',
  `merchant_discount_amount` int(11) NOT NULL DEFAULT 0 COMMENT '商家优惠金额',
  `pay_time_unix` int(11) NOT NULL DEFAULT 0 COMMENT '支付时间戳，秒 ',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '抖音订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for filter_mobiles
-- ----------------------------
DROP TABLE IF EXISTS `filter_mobiles`;
CREATE TABLE `filter_mobiles`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mobile` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for finances
-- ----------------------------
DROP TABLE IF EXISTS `finances`;
CREATE TABLE `finances`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `type` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '1 发钱,2减钱',
  `total` int(11) NOT NULL DEFAULT 0 COMMENT '总金额',
  `status` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态：0失效 1有效',
  `shop_id` int(11) NOT NULL DEFAULT 1 COMMENT '门店id',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5947 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '财务表,增加一个核销加一笔,退款减一笔' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fliggy_configs
-- ----------------------------
DROP TABLE IF EXISTS `fliggy_configs`;
CREATE TABLE `fliggy_configs`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '应用公钥',
  `app_secret` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '应用私钥',
  `env` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'sandbox' COMMENT '环境 sandbox-测试 production-生产',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态 1-启用 2-禁用',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `create_at` int(11) NOT NULL COMMENT '创建时间戳',
  `update_at` int(11) NOT NULL COMMENT '更新时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_app_key`(`app_key` ASC) USING BTREE,
  INDEX `idx_env`(`env` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '飞猪配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fliggy_order_logs
-- ----------------------------
DROP TABLE IF EXISTS `fliggy_order_logs`;
CREATE TABLE `fliggy_order_logs`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `fliggy_order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '飞猪订单号',
  `order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统商内部订单号',
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型 create-创建 query-查询 refund-退款 update-修改',
  `request_data` json NULL COMMENT '请求数据',
  `response_data` json NULL COMMENT '响应数据',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '处理状态 1-成功 2-失败',
  `error_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '错误信息',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户代理',
  `create_at` int(11) NOT NULL COMMENT '创建时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_fliggy_order_id`(`fliggy_order_id` ASC) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_action_type`(`action_type` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_at`(`create_at` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '飞猪订单操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fliggy_orders
-- ----------------------------
DROP TABLE IF EXISTS `fliggy_orders`;
CREATE TABLE `fliggy_orders`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `fliggy_order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '飞猪订单号',
  `order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '系统商内部订单号',
  `biz_type` tinyint(4) NOT NULL DEFAULT 2 COMMENT '业务类型 2-线路业务',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '订单状态 1-待确认 2-已确认 3-已取消 4-已完成 5-已退款',
  `adult_number` int(11) NULL DEFAULT 0 COMMENT '成人数',
  `child_number` int(11) NULL DEFAULT 0 COMMENT '儿童数',
  `travel_date` date NULL DEFAULT NULL COMMENT '出行日期',
  `confirm_time` int(11) NULL DEFAULT NULL COMMENT '二次确认时间戳',
  `refund_status` tinyint(4) NULL DEFAULT NULL COMMENT '退款状态 1-退款成功 2-退款失败 3-退款处理中',
  `refund_fee` int(11) NULL DEFAULT 0 COMMENT '退款金额（分）',
  `refund_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '退款原因',
  `refund_time` int(11) NULL DEFAULT NULL COMMENT '退款时间戳',
  `vendor` json NULL COMMENT '系统商信息',
  `contacts` json NULL COMMENT '联系人信息',
  `travellers` json NULL COMMENT '出行人信息',
  `product_list` json NULL COMMENT '产品列表',
  `order_info` json NULL COMMENT '订单详细信息',
  `extend_map` json NULL COMMENT '扩展信息',
  `create_at` int(11) NOT NULL COMMENT '创建时间戳',
  `update_at` int(11) NOT NULL COMMENT '更新时间戳',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_fliggy_order_id`(`fliggy_order_id` ASC) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_create_at`(`create_at` ASC) USING BTREE,
  INDEX `idx_travel_date`(`travel_date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '飞猪订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for follows
-- ----------------------------
DROP TABLE IF EXISTS `follows`;
CREATE TABLE `follows`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NULL DEFAULT NULL COMMENT '订单ID',
  `admin_id` int(11) NULL DEFAULT NULL COMMENT '管理员ID',
  `status` int(11) NULL DEFAULT NULL COMMENT '状态：0待跟进 1跟进中 2已核销 3核销失败 4放弃跟单',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `is_private` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是私域',
  `create_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 47 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '跟进表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for itinerary
-- ----------------------------
DROP TABLE IF EXISTS `itinerary`;
CREATE TABLE `itinerary`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线路名称',
  `price` decimal(10, 2) NOT NULL COMMENT '价格',
  `original_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '原价',
  `view_count` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '浏览数量',
  `is_hot` tinyint(2) NULL DEFAULT 0 COMMENT '1 热点',
  `is_recommend` tinyint(2) NULL DEFAULT NULL COMMENT '1 推荐',
  `is_discount` tinyint(2) NULL DEFAULT NULL COMMENT '1 优惠',
  `days` int(11) NOT NULL COMMENT '行程天数',
  `nights` int(11) NULL DEFAULT 0 COMMENT '行程夜数',
  `sort` int(10) NULL DEFAULT 9999 COMMENT '排序',
  `sales` int(11) NULL DEFAULT 0 COMMENT '销量',
  `themes_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '主题标签，使用英文逗号分隔',
  `destination` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目的地',
  `departure_city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '始发地',
  `product_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品类型,跟团游、自由行等',
  `cover_image` varchar(255) CHARACTER SET utf32 COLLATE utf32_general_ci NOT NULL DEFAULT '' COMMENT '封面图',
  `images` json NULL COMMENT '图片集',
  `image_description` json NULL COMMENT '图片描述',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线路描述',
  `features` json NULL COMMENT '线路特色',
  `status` enum('online','offline') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'offline' COMMENT '上架状态',
  `travel_mode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '旅行方式(休闲度假,人文历史等)',
  `group_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '成团类型(拼团,包团)',
  `min_group_size` int(11) NULL DEFAULT 1 COMMENT '最小出行人数',
  `max_group_size` int(11) NULL DEFAULT 10 COMMENT '最大出行人数',
  `collect_type` enum('capitation','collectAll') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'capitation' COMMENT '收费方式',
  `deposit` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '预付定金',
  `promotion_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '促销价格',
  `category_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品品类ID',
  `category_path` json NULL COMMENT '品类路径',
  `valid_from` date NULL DEFAULT NULL COMMENT '预约有效期开始日期',
  `valid_to` date NULL DEFAULT NULL COMMENT '预约有效期结束日期',
  `is_long_term` tinyint(1) NULL DEFAULT 0 COMMENT '是否长期有效',
  `single_room_surcharge` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '单房差',
  `room_upgrade_surcharge` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '升级房型费用',
  `consumable_date_type` enum('specificDates','specificDays') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '顾客可消费日期类型',
  `consumable_days` int(11) NULL DEFAULT NULL COMMENT '指定天数',
  `consumable_dates` json NULL COMMENT '指定日期',
  `purchase_limit_single` tinyint(1) NULL DEFAULT NULL COMMENT '限制单笔购买数量',
  `purchase_limit_per_order` int(11) NULL DEFAULT NULL COMMENT '每单限购数量',
  `purchase_limit_rules` tinyint(1) NULL DEFAULT NULL COMMENT '限制购买规则',
  `purchase_limit_details` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '购买限制详情',
  `earliest_booking_days` int(11) NULL DEFAULT NULL COMMENT '最早可预约时间(可预约多少天内的库存)',
  `advance_booking_required` tinyint(1) NULL DEFAULT NULL COMMENT '需提前预约',
  `advance_booking_range` enum('oneDayBefore','sameDay') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提前范围',
  `advance_booking_time` int(11) NULL DEFAULT NULL COMMENT '提前时间',
  `advance_booking_time_unit` enum('days','hours') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提前时间单位',
  `has_no_cancellation_after_appointment` tinyint(1) NULL DEFAULT NULL COMMENT '取消选项',
  `refund_before_appointment` tinyint(1) NULL DEFAULT NULL COMMENT '未预约随时退款',
  `auto_refund_after_expiration` tinyint(1) NULL DEFAULT NULL COMMENT '过期未预约自动全额退款',
  `has_breach_of_contract_terms` tinyint(1) NULL DEFAULT NULL COMMENT '预约成功后若双方违约，按如下条款支付违约金',
  `breach_of_contract_terms` json NULL COMMENT '违约金条款，数组结构，包含buyerPenalty、days、sellerPenalty',
  `breach_of_contract_base` enum('amountTotal','amountUnconsumed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '违约金基数',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  `child_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '儿童价格',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_destination`(`destination` ASC) USING BTREE,
  INDEX `idx_departure_city`(`departure_city` ASC) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '旅行线路表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for itinerary_day
-- ----------------------------
DROP TABLE IF EXISTS `itinerary_day`;
CREATE TABLE `itinerary_day`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `itinerary_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的行程ID',
  `day` int(11) NOT NULL COMMENT '第几天',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '行程标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '行程描述',
  `activities` json NULL COMMENT '活动内容',
  `meeting_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '集合点',
  `meeting_time` time NULL DEFAULT NULL COMMENT '集合时间',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_itinerary_day`(`itinerary_id` ASC, `day` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 289 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '旅行行程单天安排表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for itinerary_inventory
-- ----------------------------
DROP TABLE IF EXISTS `itinerary_inventory`;
CREATE TABLE `itinerary_inventory`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `itinerary_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线路ID',
  `inventory_date` date NOT NULL COMMENT '库存日期',
  `status` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '库存状态',
  `total_amount` int(11) NULL DEFAULT NULL COMMENT '总库存数量，NULL表示不限',
  `booked_amount` int(11) NOT NULL DEFAULT 0 COMMENT '已预约数量',
  `rule_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对应的规则ID',
  `has_hot_date_surcharge` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有热门日期加价',
  `hot_date_surcharge_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '热门日期加价金额',
  `has_traveler_surcharge` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有出行人加价',
  `adult_surcharge_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '成人加价金额',
  `child_surcharge_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '儿童加价金额',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_itinerary_date`(`itinerary_id` ASC, `inventory_date` ASC) USING BTREE,
  INDEX `idx_inventory_date`(`inventory_date` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 107 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '线路每日库存表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for itinerary_inventory_log
-- ----------------------------
DROP TABLE IF EXISTS `itinerary_inventory_log`;
CREATE TABLE `itinerary_inventory_log`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `itinerary_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线路ID',
  `operation_type` enum('create','update','delete') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
  `operation_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '操作内容(JSON格式)',
  `affected_dates` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '受影响的日期范围',
  `operator` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_itinerary_id`(`itinerary_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '库存操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for itinerary_order
-- ----------------------------
DROP TABLE IF EXISTS `itinerary_order`;
CREATE TABLE `itinerary_order`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `itinerary_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线路ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户姓名',
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `travel_date` date NOT NULL COMMENT '出行日期',
  `adult_count` int(11) NOT NULL DEFAULT 0 COMMENT '成人人数',
  `child_count` int(11) NOT NULL DEFAULT 0 COMMENT '儿童人数',
  `base_price` decimal(10, 2) NOT NULL COMMENT '基础价格',
  `hot_date_surcharge` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '热门日期加价总额',
  `adult_surcharge` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '成人加价总额',
  `child_surcharge` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '儿童加价总额',
  `room_count` int(11) NULL DEFAULT 0 COMMENT '房间数量',
  `single_room_count` int(11) NULL DEFAULT 0 COMMENT '单人间数量',
  `upgrade_room_count` int(11) NULL DEFAULT 0 COMMENT '升级房间数量',
  `single_room_surcharge` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '单房差',
  `upgrade_room_surcharge` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '升级房差',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '订单备注',
  `total_amount` decimal(10, 2) NOT NULL COMMENT '订单总金额',
  `status` enum('pending','paid','cancelled','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  `adult_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成人单人价格',
  `child_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '儿童单价',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_no`(`order_no` ASC) USING BTREE,
  INDEX `idx_itinerary_id`(`itinerary_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_travel_date`(`travel_date` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '线路订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for itinerary_schedule
-- ----------------------------
DROP TABLE IF EXISTS `itinerary_schedule`;
CREATE TABLE `itinerary_schedule`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `itinerary_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的行程ID',
  `itinerary_day_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的单天行程ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '行程标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '行程描述',
  `activities` json NULL COMMENT '活动内容',
  `meals` json NULL COMMENT '餐食信息',
  `accommodation` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住宿信息',
  `images` json NULL COMMENT '图片集',
  `type` enum('meal','transport','attraction','accommodation','freeActivity') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '行程类型',
  `start_time` time NULL DEFAULT NULL COMMENT '开始时间',
  `meal_type` json NULL COMMENT '餐食类型',
  `adult_meal_included` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '成人是否含餐',
  `child_meal_included` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '儿童是否含餐',
  `meal_duration_hours` int(11) NULL DEFAULT 0 COMMENT '用餐时长-小时',
  `meal_duration_minutes` int(11) NULL DEFAULT 0 COMMENT '用餐时长-分钟',
  `meal_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '餐标价格',
  `transport_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交通类型',
  `flight_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '航班号',
  `departure_city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出发城市',
  `arrival_city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抵达城市',
  `estimated_duration_hours` int(11) NULL DEFAULT 0 COMMENT '预计用时-小时',
  `estimated_duration_minutes` int(11) NULL DEFAULT 0 COMMENT '预计用时-分钟',
  `stay_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住宿方式',
  `hotel_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '酒店名称',
  `activity_duration_hours` int(11) NULL DEFAULT 0 COMMENT '活动时长-小时',
  `activity_duration_minutes` int(11) NULL DEFAULT 0 COMMENT '活动时长-分钟',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '补充说明',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `sort` int(10) NOT NULL DEFAULT 0 COMMENT '排序',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `delete_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_itinerary_day`(`itinerary_id` ASC, `itinerary_day_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 807 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '旅行行程安排表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for line
-- ----------------------------
DROP TABLE IF EXISTS `line`;
CREATE TABLE `line`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '1 启用  0禁用',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `create_time` int(11) NULL DEFAULT NULL,
  `update_time` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for live_room
-- ----------------------------
DROP TABLE IF EXISTS `live_room`;
CREATE TABLE `live_room`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '直播间名称',
  `status` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态（0-未启用，1-已启用）',
  `product_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '产品ID，用英文逗号隔开',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播间列表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for live_room_works
-- ----------------------------
DROP TABLE IF EXISTS `live_room_works`;
CREATE TABLE `live_room_works`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `live_room_id` int(11) NOT NULL DEFAULT 0 COMMENT '直播间id',
  `zhubo_id` int(11) NOT NULL DEFAULT 0 COMMENT '主播id',
  `zhongkong_id` int(11) NOT NULL DEFAULT 0 COMMENT '中控id',
  `month` int(11) NULL DEFAULT NULL COMMENT '当月',
  `day` int(11) NULL DEFAULT NULL COMMENT '当天',
  `route` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路线',
  `start` datetime NULL DEFAULT NULL COMMENT '排班开始时间',
  `end` datetime NULL DEFAULT NULL COMMENT '排班结束时间',
  `orders` int(11) NULL DEFAULT NULL COMMENT '订单数',
  `total` decimal(20, 2) NULL DEFAULT NULL COMMENT '总金额',
  `asset_total` decimal(20, 2) NULL DEFAULT NULL COMMENT '核销金额',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_zhubo_id`(`zhubo_id` ASC) USING BTREE,
  INDEX `idx_zhongkong_id`(`zhongkong_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '直播间排班表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for logs
-- ----------------------------
DROP TABLE IF EXISTS `logs`;
CREATE TABLE `logs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员id',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `action` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '动作',
  `check_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '核销码',
  `create_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `is_private` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48298 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '电话记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for logs_xs
-- ----------------------------
DROP TABLE IF EXISTS `logs_xs`;
CREATE TABLE `logs_xs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员id',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `action` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '动作',
  `check_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '核销码',
  `create_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '电话记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for onlines
-- ----------------------------
DROP TABLE IF EXISTS `onlines`;
CREATE TABLE `onlines`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL COMMENT '管理员ID',
  `start` int(11) NOT NULL COMMENT '上线时间',
  `end` int(11) NOT NULL COMMENT '下线时间',
  `create_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1998 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '在线记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_after_sales
-- ----------------------------
DROP TABLE IF EXISTS `order_after_sales`;
CREATE TABLE `order_after_sales`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `book_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '预约ID',
  `audit_order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核ID',
  `after_sale_type` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '售后类型（1-取消预并退款，2-取消预约）',
  `after_sale_type_agg_name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '售后类型名称',
  `refund_amount` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `refund_type` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '退款类型',
  `after_sale_reason` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '售后原因',
  `after_sale_apply_time_ms` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '申请时间',
  `audit_expire_time_ms` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '售后时间',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态（0-未处理，1-同意，2-拒绝）',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '售后列表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_books
-- ----------------------------
DROP TABLE IF EXISTS `order_books`;
CREATE TABLE `order_books`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NULL DEFAULT 0 COMMENT '订单号',
  `travel_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '出游日期',
  `num` int(11) NOT NULL DEFAULT 0 COMMENT '出游人数',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '出行人名称',
  `mobile` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系电话',
  `code_pic` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '券码图片',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0-未处理，1-已处理）',
  `note` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单预约表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `os` tinyint(4) NOT NULL DEFAULT 0 COMMENT '来自哪个平台,1美团',
  `sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '平台订单号，转单加的管理员的 _ID',
  `check_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '核销码',
  `is_check` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '还没有核销，1核销，2核销错误',
  `is_wechat` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '已加微信',
  `is_zhibo` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否主播',
  `type` tinyint(4) NULL DEFAULT 0 COMMENT '0 订单  1 线索',
  `level` tinyint(4) NULL DEFAULT NULL COMMENT '等级',
  `product_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品ID',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `evidence_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证据图片',
  `category_desc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类描述',
  `category_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类ID',
  `create_at` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '生成订单时间',
  `travel_date` datetime NULL DEFAULT NULL COMMENT '出行日期',
  `travel_end` datetime NULL DEFAULT NULL COMMENT '出行结束日期',
  `contact` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系人',
  `mobile` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系手机',
  `unit_price` int(11) NOT NULL DEFAULT 0 COMMENT '单价',
  `total_price` int(11) NOT NULL DEFAULT 0 COMMENT '总价',
  `actual_price` int(11) NOT NULL DEFAULT 0 COMMENT '有效付款金额',
  `asset_price` int(11) NOT NULL DEFAULT 0 COMMENT '核销金额',
  `quantity` int(11) NOT NULL DEFAULT 0 COMMENT '人数',
  `order_status` smallint(6) NOT NULL DEFAULT 0 COMMENT '订单状态',
  `refund_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '退款状态',
  `asset_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '核销状态',
  `fans_status` tinyint(4) NULL DEFAULT NULL COMMENT '粉丝状态 1有效 0无效',
  `personnel` json NULL COMMENT '出行人员情况',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分配给哪个管理员的',
  `zhubo` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '主播',
  `last_follow` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最后更新时间',
  `next_follow` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '下次改进时间',
  `status` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '跟进状态: 0待跟进 1跟进中 2已核销 3核销失败 4放弃跟单',
  `sys_status` int(11) NOT NULL DEFAULT 0 COMMENT '系统状态',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `give_time` bigint(20) NULL DEFAULT NULL COMMENT '分配客服时间',
  `next_remind_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '下次短信提示时间',
  `appointment_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否已预约',
  `book_start_date` datetime NULL DEFAULT NULL COMMENT '出行日期',
  `live_room_work_id` int(11) NOT NULL DEFAULT 0 COMMENT '排班表id',
  `create_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `is_refunded` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否已退款（0-否，1-是）',
  `is_apply_appointment` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已申请预约',
  `verification_date` datetime NULL DEFAULT NULL COMMENT '核销日期',
  `is_direct_mode` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否直连模式（0-否，1-是）',
  `is_public_pool` tinyint(1) NOT NULL DEFAULT 0 COMMENT '公共池（0-否，1-是）',
  `last_pool_date` datetime NULL DEFAULT NULL COMMENT '最后加入公共池时间',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sn`(`sn` ASC) USING BTREE,
  INDEX `admin_id`(`admin_id` ASC) USING BTREE,
  INDEX `check_sn`(`check_sn` ASC) USING BTREE,
  INDEX `create_at`(`create_at` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `next_remind_time`(`next_remind_time` ASC) USING BTREE,
  INDEX `idx_give_time`(`give_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29919 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for orders_xs
-- ----------------------------
DROP TABLE IF EXISTS `orders_xs`;
CREATE TABLE `orders_xs`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `os` tinyint(4) NOT NULL DEFAULT 0 COMMENT '来自哪个平台,1美团',
  `sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '平台订单号，转单加的管理员的 _ID',
  `check_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '核销码',
  `is_check` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '还没有核销，1核销，2核销错误',
  `is_wechat` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '已加微信',
  `is_zhibo` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否主播',
  `type` tinyint(4) NULL DEFAULT 0 COMMENT '0 订单  1 线索',
  `level` tinyint(4) NULL DEFAULT NULL COMMENT '等级',
  `product_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品ID',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品名称',
  `evidence_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '证据图片',
  `category_desc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '分类描述',
  `wechat` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信号',
  `category_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类ID',
  `create_at` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '生成订单时间',
  `travel_date` datetime NULL DEFAULT NULL COMMENT '出行日期',
  `travel_end` datetime NULL DEFAULT NULL COMMENT '出行结束日期',
  `contact` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系人',
  `mobile` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系手机',
  `unit_price` int(11) NOT NULL DEFAULT 0 COMMENT '单价',
  `total_price` int(11) NOT NULL DEFAULT 0 COMMENT '总价',
  `actual_price` int(11) NOT NULL DEFAULT 0 COMMENT '有效付款金额',
  `asset_price` int(11) NOT NULL DEFAULT 0 COMMENT '核销金额',
  `quantity` int(11) NOT NULL DEFAULT 0 COMMENT '人数',
  `order_status` smallint(6) NOT NULL DEFAULT 0 COMMENT '订单状态',
  `refund_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '退款状态',
  `asset_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '核销状态',
  `fans_status` tinyint(4) NULL DEFAULT 0 COMMENT '粉丝状态 1有效 0无效',
  `personnel` json NULL COMMENT '出行人员情况',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分配给哪个管理员的',
  `zhubo` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '主播',
  `last_follow` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最后更新时间',
  `next_follow` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '下次改进时间',
  `status` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '跟进状态: 0待跟进 1跟进中 2已核销 3核销失败 4放弃跟单',
  `sys_status` int(11) NOT NULL DEFAULT 0 COMMENT '系统状态',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `give_time` bigint(20) NULL DEFAULT NULL COMMENT '分配客服时间',
  `next_remind_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '下次短信提示时间',
  `appointment_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否已预约',
  `book_start_date` datetime NULL DEFAULT NULL COMMENT '出行日期',
  `live_room_work_id` int(11) NOT NULL DEFAULT 0 COMMENT '排班表id',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `create_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `update_time` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `is_refunded` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否已退款（0-否，1-是）',
  `is_apply_appointment` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已申请预约',
  `verification_date` datetime NULL DEFAULT NULL COMMENT '核销日期',
  `is_direct_mode` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否直连模式（0-否，1-是）',
  `is_public_pool` tinyint(1) NOT NULL DEFAULT 0 COMMENT '公共池（0-否，1-是）',
  `last_pool_date` datetime NULL DEFAULT NULL COMMENT '最后加入公共池时间',
  `create_admin_id` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sn`(`sn` ASC) USING BTREE,
  INDEX `admin_id`(`admin_id` ASC) USING BTREE,
  INDEX `check_sn`(`check_sn` ASC) USING BTREE,
  INDEX `create_at`(`create_at` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `next_remind_time`(`next_remind_time` ASC) USING BTREE,
  INDEX `idx_give_time`(`give_time` ASC) USING BTREE,
  INDEX `idx_create_admin_id`(`shop_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30164 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_schedules
-- ----------------------------
DROP TABLE IF EXISTS `product_schedules`;
CREATE TABLE `product_schedules`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
  `third_product_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '第三方商品id',
  `date` date NOT NULL COMMENT '日期',
  `num` int(11) NOT NULL DEFAULT 0 COMMENT '可预约人数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品班期表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `third_product_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品id',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名称',
  `is_private` tinyint(4) NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0-不可用，1-可用）',
  `os` int(11) NOT NULL DEFAULT 0 COMMENT '平台id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0-未设置，1-境内，2-境外',
  `day` int(11) NOT NULL DEFAULT 0 COMMENT '天',
  `night` int(11) NOT NULL DEFAULT 0 COMMENT '晚',
  `trip_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详细行程',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `shop_id` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '门店id',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_third_product_id`(`third_product_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 438 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for public_pool_orders
-- ----------------------------
DROP TABLE IF EXISTS `public_pool_orders`;
CREATE TABLE `public_pool_orders`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '订单号',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单状态（0-未领取，10-已领取）',
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `receive_admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '领取ID',
  `received_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '领取时间',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单公共池' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qa_citys
-- ----------------------------
DROP TABLE IF EXISTS `qa_citys`;
CREATE TABLE `qa_citys`  (
  `city_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `city_name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '城市名',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  PRIMARY KEY (`city_id`) USING BTREE,
  UNIQUE INDEX `city_name`(`city_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qa_questions
-- ----------------------------
DROP TABLE IF EXISTS `qa_questions`;
CREATE TABLE `qa_questions`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `qa_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT 'qas表id',
  `title` varchar(180) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容',
  `sort` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7318 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'QA问题' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qas
-- ----------------------------
DROP TABLE IF EXISTS `qas`;
CREATE TABLE `qas`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `city_id` int(10) UNSIGNED NOT NULL COMMENT '城市名',
  `is_private` tinyint(4) NULL DEFAULT 0,
  `title` varchar(200) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'QA标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '内容',
  `trip_zip` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '行程下载',
  `img_zip` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '图片下载',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '状态：0禁用 1启用',
  `shop_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `create_time` bigint(20) NULL DEFAULT NULL,
  `update_time` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'QA表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qimo_agents
-- ----------------------------
DROP TABLE IF EXISTS `qimo_agents`;
CREATE TABLE `qimo_agents`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '坐席名称',
  `account_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '坐席账号ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '登录用户名',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '登录密码',
  `pbx_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'PBX地址',
  `login_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'Local' COMMENT '登录类型：Local/Remote',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '坐席描述',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '坐席状态：0-禁用，1-空闲，2-忙碌，3-通话中',
  `last_call_time` int(11) NULL DEFAULT NULL COMMENT '最后通话时间',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_account_id`(`account_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_last_call_time`(`last_call_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '七陌坐席配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qimo_call_logs
-- ----------------------------
DROP TABLE IF EXISTS `qimo_call_logs`;
CREATE TABLE `qimo_call_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_id` int(11) NULL DEFAULT 0 COMMENT '操作管理员ID',
  `agent_id` int(11) NULL DEFAULT 0 COMMENT '坐席ID',
  `caller_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '主叫号码',
  `callee_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '被叫号码',
  `direction` tinyint(1) NULL DEFAULT 1 COMMENT '呼叫方向：1-外呼，2-内呼',
  `call_status` tinyint(1) NULL DEFAULT 1 COMMENT '通话状态：1-呼叫中，2-已接听，3-已挂断，4-呼叫失败',
  `call_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '七陌通话ID',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '会话ID',
  `start_time` int(11) NULL DEFAULT NULL COMMENT '开始时间',
  `answer_time` int(11) NULL DEFAULT NULL COMMENT '接听时间',
  `end_time` int(11) NULL DEFAULT NULL COMMENT '结束时间',
  `duration` int(11) NULL DEFAULT 0 COMMENT '通话时长（秒）',
  `record_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '录音文件URL',
  `event_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '事件原始数据（JSON格式）',
  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_admin_id`(`admin_id` ASC) USING BTREE,
  INDEX `idx_agent_id`(`agent_id` ASC) USING BTREE,
  INDEX `idx_call_id`(`call_id` ASC) USING BTREE,
  INDEX `idx_callee_number`(`callee_number` ASC) USING BTREE,
  INDEX `idx_call_status`(`call_status` ASC) USING BTREE,
  INDEX `idx_start_time`(`start_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '七陌通话记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for routes
-- ----------------------------
DROP TABLE IF EXISTS `routes`;
CREATE TABLE `routes`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `route_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '线路名称',
  `route_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图片',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0-不可用，1-可用）',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0-一日游，1-境内多日游，2-境外多日游',
  `days` int(11) NOT NULL DEFAULT 0 COMMENT '行程天数',
  `ground_price` int(11) NOT NULL DEFAULT 0 COMMENT '地接价',
  `sale_price` int(11) NOT NULL DEFAULT 0 COMMENT '销售价',
  `trip_info` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详细行程',
  `qualifications` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '资质',
  `payment_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '收款码',
  `shop_id` int(11) NOT NULL COMMENT '门店id',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '线路表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rules
-- ----------------------------
DROP TABLE IF EXISTS `rules`;
CREATE TABLE `rules`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0',
  `c` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `a` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `create_time` int(10) UNSIGNED NOT NULL,
  `update_time` int(10) UNSIGNED NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sales
-- ----------------------------
DROP TABLE IF EXISTS `sales`;
CREATE TABLE `sales`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `date` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `order_amount` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `orders` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `refund_amount` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `refunds` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `asset_amount` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `assets` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `travel_amount` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `travels` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `create_time` int(10) UNSIGNED NOT NULL,
  `update_time` int(10) UNSIGNED NOT NULL,
  `shop_id` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for settings
-- ----------------------------
DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型,包括 system,contract',
  `name` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名称, 类似字段名',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统设置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for shops
-- ----------------------------
DROP TABLE IF EXISTS `shops`;
CREATE TABLE `shops`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '门店名称',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单状态（0-未启用，1-已启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '门店列表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for shortcut_contents
-- ----------------------------
DROP TABLE IF EXISTS `shortcut_contents`;
CREATE TABLE `shortcut_contents`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容',
  `sort` int(11) NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '状态：0禁用 1启用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_unicode_ci COMMENT = '快捷跟进' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for teams
-- ----------------------------
DROP TABLE IF EXISTS `teams`;
CREATE TABLE `teams`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '团队名称',
  `admin_ids` json NOT NULL,
  `rules` json NOT NULL COMMENT '团队拥有的权限',
  `create_time` int(10) UNSIGNED NOT NULL,
  `update_time` int(10) UNSIGNED NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '团队表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for third_mobile_logs
-- ----------------------------
DROP TABLE IF EXISTS `third_mobile_logs`;
CREATE TABLE `third_mobile_logs`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mobile` char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `area` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号归属地',
  `originalIsp` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号原运营商',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9501 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '三方手机归属查询记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for uploads
-- ----------------------------
DROP TABLE IF EXISTS `uploads`;
CREATE TABLE `uploads`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '谁上传的',
  `filesize` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小',
  `filepath` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '存储路径',
  `mime` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'mime 类型',
  `create_at` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '上传时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 295 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '上传文件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for works
-- ----------------------------
DROP TABLE IF EXISTS `works`;
CREATE TABLE `works`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `month` int(11) NULL DEFAULT NULL COMMENT '当月',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台',
  `start` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `end` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `orders` int(11) NULL DEFAULT NULL COMMENT '订单数',
  `total` decimal(20, 2) NULL DEFAULT NULL COMMENT '总金额',
  `asset_total` decimal(20, 2) NULL DEFAULT NULL COMMENT '核销金额',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态：0禁用 1开启',
  `create_time` int(10) UNSIGNED NOT NULL,
  `update_time` int(10) UNSIGNED NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `admin_id`(`admin_id` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 186 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '排班表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
