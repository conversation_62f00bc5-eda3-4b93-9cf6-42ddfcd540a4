<?php

namespace app\command;

use app\model\Admins;
use app\model\Orders;
use app\model\Sales;
use app\model\Works;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputDefinition;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use think\facade\Db;

class Service extends Command
{

    protected static $defaultName = 'server';
    protected static $defaultDescription = '处理大部分服务';

    protected function configure()
    {
        $this
            ->setName('server')
            ->setDescription('处理大部分服务')
            ->setDefinition(
                new InputDefinition(array(
                    new InputOption('date', 'd', InputOption::VALUE_REQUIRED),
                    new InputOption('admin', 'a', InputOption::VALUE_REQUIRED)
                ))
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('Server start');
        
        $date = $input->getOption('date');
        $admin = $input->getOption('admin');

        $admin_id = null;
        if($admin) {
            $admin_id = Admins::where('username', $admin)->value('id');
        }
        if($date) {
            //只处理主播的.
            if(stripos($date,',') != false) {
                $times = explode(',', $date);
                $start = date('Y-m-d H:i:s', strtotime($times[0]));
                $end = date('Y-m-d H:i:s', strtotime($times[1]));
                $this->zhubo($start, $end, true);
                return 1;
            }else{
                $time = strtotime($date);
                $start = date('Y-m-d 00:00:00', $time);
                $end = date('Y-m-d 23:59:59', $time);
            }
        }else{
            $start = date('Y-m-d 00:00:00');
            $end = date('Y-m-d 23:59:59');
        }

        $this->zhubo($start, $end);
        $this->comm($start, $end, $admin_id);
        $output->writeln('Server end');

        return 1;
    }

    //把订单绑定到主播身上
    protected function zhubo($start, $end, $clear = false) {

        $query = Admins::where('is_anchor', 1);
        $admins = $query->select();

        //先清理旧的
        if($clear) {
            $_start = strtotime($start) * 1000;
            $_end = strtotime($end) * 1000 + 999;

            Orders::where('is_zhibo', 2)
                ->whereBetween('create_at', [$_start, $_end])
                ->update(['zhubo' => 0]);
        }

        foreach($admins as $admin) {

            $query2 = Works::where('admin_id', $admin->id)->where('status', 1)->whereBetween('start',[$start, $end]);
            $lst = $query2->select();

            foreach($lst as $lt) {
                //修改成新的数据
                $_s = strtotime($lt->start) * 1000;
                $_e = strtotime($lt->end)  * 1000 + 999;
                $lt = Orders::where('is_zhibo', 2)->whereIn('os', explode(',', $lt->os))
                    ->where('zhubo', 0)->whereBetween('create_at', [$_s, $_e])
                    ->update(['zhubo' => $lt->admin_id]);
            }
        }

    }

    protected function comm($start, $end, $admin_id = 0) {
        $query = new Admins();
        if($admin_id) {
            $query = Admins::where('id', $admin_id);
        }
        $query->chunk(100, function($admins) use ($start, $end) {
            $_start = strtotime($start)*1000;
            $_end = strtotime($end)*1000 + 999;
            foreach($admins as $admin) {
                if($admin->is_anchor == 0) {
                    //订单金额
                    $order = Orders::where('admin_id', $admin->id)->whereBetween('create_at', [$_start, $_end])
                    ->fieldRaw(
                        "sum(total_price) as `order_amount`, count(id) as `orders`,
                        sum(`asset_price`) as asset_amount, sum(if(asset_price > 0,1 , 0)) as assets,
                        sum(if(order_status = 5,`total_price`, 0)) as refund_amount, sum(if(order_status = 5,1 , 0)) as refunds
                        ")->find();
                    //出行结束
                    $travel = Orders::where('admin_id', $admin->id)->where('order_status', 4)->whereBetween('travel_end', [$start, $end])->fieldRaw("
                        sum(total_price) as `travel_amount`, count(id) as `travels`
                    ")->find();
                }else{
                    //订单金额
                    $order = Orders::where('zhubo', $admin->id)->whereBetween('create_at', [$_start, $_end])
                    ->fieldRaw(
                        "sum(total_price) as `order_amount`, count(id) as `orders`,
                        sum(`asset_price`) as asset_amount, sum(if(asset_price > 0,1 , 0)) as assets,
                        sum(if(order_status = 5,`total_price`, 0)) as refund_amount, sum(if(order_status = 5,1 , 0)) as refunds
                        ")->find();
                    //出行结束
                    $travel = Orders::where('zhubo', $admin->id)->where('order_status', 4)->whereBetween('travel_end', [$start, $end])->fieldRaw("
                        sum(total_price) as `travel_amount`, count(id) as `travels`
                    ")->find();
                }
                
                $date = date('Ymd', strtotime($start));
                $item = Sales::where('admin_id', $admin->id)->where('date', $date)->find();
                if(empty($item)) {
                    $item = new Sales();
                }

                $item->admin_id = $admin->id;
                $item->date = $date;
                $item->order_amount = $order->order_amount ??0;
                $item->orders = $order->orders ??0;
                $item->asset_amount = $order->asset_amount ??0;
                $item->assets = $order->assets ??0;
                $item->refund_amount = $order->refund_amount ??0;
                $item->travel_amount = $travel->travel_amount ??0;
                $item->travels = $travel->travels ??0;
                $item->save();
            }
        });
    }

}