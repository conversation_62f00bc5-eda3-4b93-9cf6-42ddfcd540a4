<template>
    <div class="lineOnSale">
        <div class="lineOnSale_list">
            <img src="@/assets/home/<USER>" alt="">
            <div class="lineOnSale_centent">
                <div class="lineOnSale_title">天上人间—马尔代夫7日自由行7天</div>
                <div class="lineOnSale_price">
                    <div class="desc">
                        <p>行程天数：</p>
                        <overflowConcealment :content="'天上人间—马尔代夫7日自尔代夫7日自由行7天天上人间—马尔代夫7夫7日自由日自由行7天'"></overflowConcealment>
                    </div>
                    <div class="desc">
                        <p>购物店：</p>
                        <overflowConcealment :content="'马尔代夫'"></overflowConcealment>
                    </div>
                </div>
                <div class="lineOnSale_price">
                    <div class="desc">
                        <p>是否含大交通：</p>
                        <overflowConcealment :content="'否'"></overflowConcealment>
                    </div>
                    <div class="desc">
                        <p>可售卖平台：</p>
                        <overflowConcealment :content="'否'"></overflowConcealment>
                    </div>
                </div>
                <div class="lineOnSale_price">
                    <div class="desc">
                        <p>卖点：</p>
                        <overflowConcealment :content="'誉为印度洋的珍珠'"></overflowConcealment>
                    </div>
                    <div class="desc">
                        <p>佣金：</p>
                        <overflowConcealment :content="'6521615'"></overflowConcealment>
                    </div>
                </div>
                <div class="lineOnSale_price">
                    <div class="desc">
                        <p>赠送项目：</p>
                        <overflowConcealment :content="'沙巴岛一日游'"></overflowConcealment>
                    </div>
                    <div class="desc">
                        <p>卖价：</p>
                        <overflowConcealment :content="'6546466'"></overflowConcealment>
                    </div>
                </div>
            </div>
            <div class="lineOnSale_right">
                <div class="lineOnSale_btn">
                    <el-button class="btn" type="primary">下载行程</el-button>
                    <el-button class="btn" type="primary">下载话术</el-button>
                    <el-button class="btn" type="primary">下载图片</el-button>
                    <el-button class="btn" type="primary">下载视频</el-button>
                </div>
            </div>
        </div>
    </div>
  </template>
  
  <script>
  import overflowConcealment from './components/overflow_concealment'
  export default {
    name: 'lineOnSale',
    data() {
      return {
        isTruncated:false
      }
    },
    components: {
        overflowConcealment
    },
    mounted() {

    },
    methods: {
        handleDownload(url) {
            if (url) {
                window.open(url)
            } else {
                this.$message({
                showClose: true,
                message: '暂无下载链接'
                })
            }
        }
    }
  }
  </script>
  <style lang="scss" scoped>
  ::v-deep.el-button + .el-button{
    margin: 0;
}
.desc_conten{
    white-space: nowrap;
    overflow: hidden;
    position: relative;
    .desc_btn{
        position: absolute;
        right: 0;
    }
    // text-overflow: ellipsis;
}
    .lineOnSale{
        padding: 60px 30px;
        margin: 0 100px;
        .lineOnSale_list{
            display: flex;
            img{
                width: 290px;
                height: 290px;
            }
            .lineOnSale_centent{
                margin-left: 50px;
                .lineOnSale_title{
                    font-size: 24px;
                    color: #000000;
                    line-height: 30px;
                    font-weight: bold;
                }
                .lineOnSale_price{
                    display: flex;
                    margin-top: 5px;
                    justify-content: space-around;
                    .desc{
                        ::v-deep.el-button--medium{
                            padding: 6px 10px;
                        }
                    }
                    div{
                        width: 540px;
                        & + div{
                            margin-left: 40px;
                        }
                        p{
                            font-size: 15px;
                            color: #2E2E2E;
                            line-height: 30px;
                            margin: 0;
                            font-weight: bold;
                        }
                        // p:nth-child(1){
                        //     font-weight: bold;
                        // }
                        p:nth-child(2){
                            font-size: 14px;
                            color: rgba(0, 0, 0, 0.60);
                        }
                    }
                }
            }
        }
        .lineOnSale_right{
            margin-left: 20px;
            display: flex;
            align-items: center;
            ::v-deep.el-button{
                display: block;
            }
            .btn{
                width: 160px;
                height: 36px;
                font-weight: bold;
                & + .btn{
                    margin-top: 10px;
                }
            }
        }
    }
  </style>