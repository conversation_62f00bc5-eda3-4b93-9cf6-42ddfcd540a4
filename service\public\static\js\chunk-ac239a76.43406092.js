(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ac239a76"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),Math.easeInOutQuad=function(e,t,a,i){return e/=i/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,a){var o=r(),l=e-o,s=20,c=0;t="undefined"===typeof t?500:t;var u=function(){c+=s;var e=Math.easeInOutQuad(c,o,l,t);n(e),c<t?i(u):a&&"function"===typeof a&&a()};u()}},"2cbf":function(e,t,a){"use strict";a("bac3")},"333d":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[a("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},n=[],r=(a("374d"),a("09f4")),o={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e}),this.autoScroll&&Object(r["a"])(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&Object(r["a"])(0,800)}}},l=o,s=(a("2cbf"),a("8a34")),c=Object(s["a"])(l,i,n,!1,null,"6af373ef",null);t["a"]=c.exports},"50fc":function(e,t,a){"use strict";a.d(t,"f",(function(){return n})),a.d(t,"b",(function(){return r})),a.d(t,"h",(function(){return o})),a.d(t,"k",(function(){return l})),a.d(t,"e",(function(){return s})),a.d(t,"a",(function(){return c})),a.d(t,"j",(function(){return u})),a.d(t,"m",(function(){return d})),a.d(t,"g",(function(){return p})),a.d(t,"i",(function(){return m})),a.d(t,"l",(function(){return f})),a.d(t,"d",(function(){return g})),a.d(t,"n",(function(){return h})),a.d(t,"c",(function(){return b}));a("e168");var i=a("b775");function n(){return Object(i["a"])({url:"admin/products/list",method:"get"})}function r(e){return Object(i["a"])({url:"/admin/products/add",method:"post",data:e})}function o(e){var t,a;return Object(i["a"])({url:"/admin/products/list?page=".concat(e.page,"&limit=").concat(e.limit,"&product_name=").concat(null!==(t=e.product_name)&&void 0!==t?t:"","&third_product_id=").concat(null!==(a=e.third_product_id)&&void 0!==a?a:""),method:"get"})}function l(e){return Object(i["a"])({url:"/admin/products/add",method:"post",data:e})}function s(e){return Object(i["a"])({url:"/admin/products/productschedules?id=".concat(e.id,"&date=").concat(e.date),method:"get"})}function c(e){return Object(i["a"])({url:"/admin/products/addproductschedules",method:"post",data:e})}function u(e){var t,a;return Object(i["a"])({url:"/admin/routes/list?page=".concat(e.page,"&limit=").concat(e.limit,"&route_name=").concat(null!==(t=e.route_name)&&void 0!==t?t:"","&third_product_id=").concat(null!==(a=e.third_product_id)&&void 0!==a?a:""),method:"get"})}function d(e){return Object(i["a"])({url:"/admin/routes/add",method:"post",data:e})}function p(){return Object(i["a"])({url:"admin/products-xs/list",method:"get"})}function m(e){var t,a;return Object(i["a"])({url:"/admin/products-xs/list?page=".concat(e.page,"&limit=").concat(e.limit,"&product_name=").concat(null!==(t=e.product_name)&&void 0!==t?t:"","&third_product_id=").concat(null!==(a=e.third_product_id)&&void 0!==a?a:""),method:"get"})}function f(e){return Object(i["a"])({url:"/admin/products-xs/add",method:"post",data:e})}function g(){return Object(i["a"])({url:"/admin/setting/getContractSetting",method:"get"})}function h(e){return Object(i["a"])({url:"/admin/setting/savecontractsetting",method:"post",data:e})}function b(e){return Object(i["a"])({url:"/admin/admin/index",method:"get",params:e})}},"607f":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"线路名称"},model:{value:e.listQuery.route_name,callback:function(t){e.$set(e.listQuery,"route_name",t)},expression:"listQuery.route_name"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.getList}},[e._v(" 搜索 ")]),e.token?a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-circle-plus"},on:{click:function(t){return e.handleCreate("add",[])}}},[e._v(" 新增线路 ")]):e._e()],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.list,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"center",label:"操作",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(a){return e.handleCreate("edit",t.row)}}},[e._v(" 修改 ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"线路名称",prop:"route_name"}}),a("el-table-column",{attrs:{align:"center",label:"行程图片",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.route_image?a("div",[e.getImages(t.row.route_image).length>0?a("div",{staticClass:"image-preview-wrapper"},[a("el-image",{staticClass:"preview-image",attrs:{src:e.getImages(t.row.route_image)[0],fit:"cover","preview-src-list":e.getImages(t.row.route_image)}}),e.getImages(t.row.jy_license).length>1?a("span",{staticClass:"image-count"},[e._v(" +"+e._s(e.getImages(t.row.jy_license).length-1)+" ")]):e._e()],1):e._e()]):a("span",[e._v("-")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"行程天数",prop:"days",withd:"300"}}),a("el-table-column",{attrs:{align:"center",label:"资质",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.qualifications?a("div",[e.getImages(t.row.qualifications).length>0?a("div",{staticClass:"image-preview-wrapper"},[a("el-image",{staticClass:"preview-image",attrs:{src:e.getImages(t.row.qualifications)[0],fit:"cover","preview-src-list":e.getImages(t.row.qualifications)}}),e.getImages(t.row.jy_license).length>1?a("span",{staticClass:"image-count"},[e._v(" +"+e._s(e.getImages(t.row.jy_license).length-1)+" ")]):e._e()],1):e._e()]):a("span",[e._v("-")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"收款二维码",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.payment_code?a("div",[e.getImages(t.row.payment_code).length>0?a("div",{staticClass:"image-preview-wrapper"},[a("el-image",{staticClass:"preview-image",attrs:{src:e.getImages(t.row.payment_code)[0],fit:"cover","preview-src-list":e.getImages(t.row.payment_code)}}),e.getImages(t.row.pay_qr).length>1?a("span",{staticClass:"image-count"},[e._v(" +"+e._s(e.getImages(t.row.pay_qr).length-1)+" ")]):e._e()],1):e._e()]):a("span",[e._v("-")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"地接价（分）",prop:"ground_price",width:"150"}}),a("el-table-column",{attrs:{align:"center",label:"销售价（分）",width:"150",prop:"sale_price"}}),a("el-table-column",{attrs:{align:"center",label:"利润(分)",width:"100",prop:"profit"}}),a("el-table-column",{attrs:{align:"type",label:"线路类型",width:"100",prop:"type"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1===t.row.type?"境内多日游":2===t.row.type?"境外多日游":"一日游"))])]}}])}),a("el-table-column",{attrs:{align:"type",label:"线路详细",width:"180",prop:"trip_info"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"link-type",on:{click:function(a){return e.openLink(t.row.trip_info)}}},[e._v(e._s(t.row.trip_info))])]}}])}),a("el-table-column",{attrs:{width:"180px",align:"center",label:"创建时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("parseTime")(t.row.created_at,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{width:"180px",align:"center",label:"修改时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("parseTime")(t.row.updated_at,"{y}-{m}-{d} {h}:{i}")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){return e.$set(e.listQuery,"page",t)},"update:limit":function(t){return e.$set(e.listQuery,"limit",t)},pagination:e.getList}}),a("el-dialog",{attrs:{visible:e.dialogVisible,title:e.dialogTitle},on:{"update:visible":function(t){e.dialogVisible=t},open:e.onOpen,close:e.onClose}},[a("el-form",{ref:"elForm",attrs:{model:e.formData,rules:e.rules,size:"medium","label-width":"120px"}},[a("el-form-item",{attrs:{label:"线路名称",prop:"route_name",required:""}},[a("el-input",{attrs:{placeholder:"请输入线路名称",clearable:""},model:{value:e.formData.route_name,callback:function(t){e.$set(e.formData,"route_name",t)},expression:"formData.route_name"}})],1),a("el-form-item",{attrs:{label:"行程图片"}},[a("multiple-image",{model:{value:e.formData.route_image,callback:function(t){e.$set(e.formData,"route_image",t)},expression:"formData.route_image"}})],1),a("el-form-item",{attrs:{label:"资质"}},[a("multiple-image",{model:{value:e.formData.qualifications,callback:function(t){e.$set(e.formData,"qualifications",t)},expression:"formData.qualifications"}})],1),a("el-form-item",{attrs:{label:"收款码"}},[a("multiple-image",{model:{value:e.formData.payment_code,callback:function(t){e.$set(e.formData,"payment_code",t)},expression:"formData.payment_code"}})],1),a("el-form-item",{attrs:{label:"行程天数",prop:"days",required:""}},[a("el-input",{attrs:{type:"number",placeholder:"请输入地接价",clearable:""},model:{value:e.formData.days,callback:function(t){e.$set(e.formData,"days",t)},expression:"formData.days"}})],1),a("el-form-item",{attrs:{label:"地接价(分)",prop:"ground_price",required:""}},[a("el-input",{attrs:{type:"number",placeholder:"请输入地接价",clearable:""},on:{input:function(t){return e.calcProfit()}},model:{value:e.formData.ground_price,callback:function(t){e.$set(e.formData,"ground_price",t)},expression:"formData.ground_price"}})],1),a("el-form-item",{attrs:{label:"售卖价(分)",prop:"sale_price",required:""}},[a("el-input",{attrs:{type:"number",placeholder:"请输入售卖价",clearable:""},on:{input:function(t){return e.calcProfit()}},model:{value:e.formData.sale_price,callback:function(t){e.$set(e.formData,"sale_price",t)},expression:"formData.sale_price"}})],1),a("el-form-item",{attrs:{label:"利润(分)",prop:"profit"}},[a("el-input",{attrs:{placeholder:"请输入地接价",clearable:"",disabled:"true"},model:{value:e.formData.profit,callback:function(t){e.$set(e.formData,"profit",t)},expression:"formData.profit"}})],1),a("el-form-item",{attrs:{label:"线路类型",prop:"type",required:""}},[a("el-radio-group",{attrs:{size:"medium"},model:{value:e.formData.type,callback:function(t){e.$set(e.formData,"type",t)},expression:"formData.type"}},e._l(e.fieldTypeOptions,(function(t,i){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),a("el-form-item",{attrs:{label:"线路详细",prop:"trip_info"}},[a("el-upload",{ref:"field105",attrs:{action:"","before-upload":e.wordBeforeUpload,"http-request":e.handlesAvatarSuccess,"on-success":e.handleWordSuccess,"on-error":e.handleUploadError,"on-remove":e.handleRemove,"on-change":e.handleChange,"before-remove":e.beforeRemove,limit:1,"file-list":e.fieldFileList,accept:".pdf,.docx,.xlsx"}},[a("el-button",{attrs:{size:"small",type:"primary",icon:"el-icon-upload"}},[e._v("点击上传")])],1)],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.close}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handelConfirm}},[e._v("确定")])],1)],1)],1)},n=[],r=a("0fc4"),o=a("7921"),l=a("d09a"),s=(a("e168"),a("3dd5"),a("3066"),a("d987"),a("4cc3"),a("7019"),a("485c"),a("452e"),a("90c8"),a("bd1a"),a("333d")),c=a("50fc"),u=a("5f87"),d=a("b560"),p={name:"ProManagement",components:{Pagination:s["a"],MultipleImage:d["a"]},data:function(){return{listQuery:{page:1,limit:10},total:0,listLoading:!0,list:[],ossArr:[],dialogVisible:!1,dialogTitle:"新增线路",fieldFileList:[],token:Object(u["a"])(),formData:{os:"",route_name:"",type:0,day:"",night:"",third_product_id:"",trip_info:"",ground_price:0,sale_price:0,profit:0,days:"",route_image:""},fieldAction:"//admin/upload/index",fieldTypeOptions:[{label:"一日游",value:0},{label:"境内多日游",value:1},{label:"境外多日游",value:2}],rules:{route_name:[{required:!0,message:"请输入线路名称",trigger:"blur"}],days:[{required:!0,message:"请输入行程天数",trigger:"blur"}],ground_price:[{required:!0,message:"请输入地接价",trigger:"blur"}],sale_price:[{required:!0,message:"请输入销售价",trigger:"blur"}],type:[{required:!0,message:"请选择线路类型",trigger:"change"}]},editType:""}},created:function(){this.getList()},methods:{handleCreate:function(e,t){this.editType=e,"add"===e?this.AddProduct():this.editProduct(t)},AddProduct:function(){this.formData={os:"",route_name:"",type:"",day:"",night:"",third_product_id:"",trip_info:"",ground_price:0,sale_price:0,profit:0,days:"",id:0,route_image:""},this.dialogTitle="新增线路",this.dialogVisible=!0},calcProfit:function(){var e=this.formData.sale_price-this.formData.ground_price;this.formData.profit=e>0?e:0},editProduct:function(e){this.formData=Object(l["a"])({},e),this.dialogTitle="编辑线路",this.dialogVisible=!0},getList:function(){var e=this;return Object(o["a"])(Object(r["a"])().mark((function t(){var a;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("this.listQuery",e.listQuery),t.next=3,Object(c["j"])(e.listQuery);case 3:a=t.sent,0===a.error&&(e.listLoading=!1,e.total=a.data.total,e.list=a.data.data);case 5:case"end":return t.stop()}}),t)})))()},onWork:function(e){this.$router.push("/system/proScheduling?id=".concat(e))},handleChange:function(e,t){this.fileList=t.slice(-1)},handlesAvatarSuccess:function(e){var t=this;return Object(o["a"])(Object(r["a"])().mark((function a(){var i,n,o,l;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,i=new FormData,i.append("file",e.file),a.next=5,t.$axios.post("/admin/upload/index",i,{headers:{"Content-type":"multipart/form-data","X-Token":Object(u["a"])()}});case 5:n=a.sent,console.log(n,"收拾收拾"),0===n.error&&e.onSuccess(n),a.next=17;break;case 10:a.prev=10,a.t0=a["catch"](0),console.log(e,"error--handlesAvatarSuccess"),o=e.file.uid,l=t.$refs.field105.uploadFiles.findIndex((function(e){return e.uid===o})),t.$refs.field105.uploadFiles.splice(l,1),t.$message.error("上传失败");case 17:case"end":return a.stop()}}),a,null,[[0,10]])})))()},handleWordSuccess:function(e,t,a,i){console.log(e,t,a,"成功了"),e&&(this.formData.trip_info="".concat(window.location.protocol,"//").concat(window.location.host).concat(e.data),this.fieldFileList=[{name:t.name,uid:t.uid,url:this.formData.trip_info}],this.$message.success("上传成功"))},getImages:function(e){return e?e.split(",").filter((function(e){return""!==e.trim()})):[]},beforeRemove:function(e,t){return this.$confirm("确定移除 ".concat(e.name,"？"))},handleRemove:function(e,t){var a=this;console.log(e,t,"handleRemove"),this.formData.trip_info="",this.fieldFileList.map((function(t,i){t.uid===e.uid&&a.fieldFileList.splice(i,1)}))},handleUploadError:function(e,t){var a=this;this.$message.error("上传失败: ".concat(t.name)),console.log(this.fieldFileList,"失败了"),this.fieldFileList.map((function(e,i){e.uid===t.uid&&a.fieldFileList.splice(i,1)}))},handlesRouteImageSuccess:function(e){var t=this;return Object(o["a"])(Object(r["a"])().mark((function a(){var i,n;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,i=new FormData,i.append("file",e.file),t.upLoading=!0,t,a.next=7,t.$axios.post("/admin/upload/index",i,{headers:{"Content-type":"multipart/form-data","X-Token":Object(u["a"])()}});case 7:n=a.sent,t.formData.route_image="".concat(window.location.protocol,"//").concat(window.location.host).concat(n.data),e.onSuccess(n),a.next=15;break;case 12:a.prev=12,a.t0=a["catch"](0),console.error("error:",a.t0);case 15:case"end":return a.stop()}}),a,null,[[0,12]])})))()},handleSuccess:function(e,t,a){console.log(e,t,a),e.data&&(this.formData.route_image="".concat(window.location.protocol,"//").concat(window.location.host).concat(e.data))},wordBeforeUpload:function(e){var t=["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"].includes(e.type);return t||this.$message.error("只允许上传 PDF、DOCX、XLSX 格式的文件"),t},openLink:function(e){window.open(e)},onOpen:function(){},onClose:function(){this.$refs["elForm"].resetFields(),this.$refs.field105.uploadFiles=[]},close:function(){console.log("1111",this.$refs.field105.uploadFiles),this.dialogVisible=!1,this.$refs.field105.uploadFiles=[]},handelConfirm:function(){var e=this;this.$refs["elForm"].validate(function(){var t=Object(o["a"])(Object(r["a"])().mark((function t(a){var i;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,Object(c["m"])(e.formData);case 4:i=t.sent,0===i.error&&(e.getList(),e.$message({message:(e.editType,"商品线路成功"),type:"success"})),e.close();case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},m=p,f=(a("a779"),a("8a34")),g=Object(f["a"])(m,i,n,!1,null,"2c062074",null);t["default"]=g.exports},"7e02":function(e,t,a){"use strict";a("8fc5")},"8fc5":function(e,t,a){},a779:function(e,t,a){"use strict";a("c900")},b560:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"multiple-image-upload"},[a("el-upload",{staticClass:"image-uploader",attrs:{data:e.dataObj,multiple:!0,"file-list":e.fileList,"on-success":e.handleSuccess,"on-remove":e.handleRemove,"on-preview":e.handlePictureCardPreview,"http-request":e.handlesAvatarSuccess,action:"","list-type":"picture-card"}},[a("i",{staticClass:"el-icon-plus"})]),a("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1)},n=[],r=a("0fc4"),o=a("7921"),l=a("756f"),s=(a("e168"),a("3dd5"),a("3066"),a("e224"),a("4cc3"),a("485c"),a("452e"),a("90c8"),a("bd1a"),a("5f87")),c={name:"MultipleImageUpload",props:{value:{type:String,default:""}},data:function(){return{dialogVisible:!1,dialogImageUrl:"",tempUrls:[],fileList:[],dataObj:{token:"",key:""}}},watch:{value:{handler:function(e){if(e){var t=e.split(",").filter((function(e){return""!==e.trim()}));this.tempUrls=Object(l["a"])(t),this.fileList=t.map((function(e,t){return{name:"图片".concat(t+1),url:e,uid:Date.now()+t}}))}else this.tempUrls=[],this.fileList=[]},immediate:!0}},methods:{handlesAvatarSuccess:function(e){var t=this;return Object(o["a"])(Object(r["a"])().mark((function a(){var i,n,o;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,i=new FormData,i.append("file",e.file),a.next=5,t.$axios.post("/admin/upload/index",i,{headers:{"Content-type":"multipart/form-data","X-Token":Object(s["a"])()}});case 5:n=a.sent,n.data&&(o="".concat(window.location.protocol,"//").concat(window.location.host).concat(n.data),e.onSuccess({data:{url:o}})),a.next=13;break;case 9:a.prev=9,a.t0=a["catch"](0),console.error("上传失败:",a.t0),e.onError(a.t0);case 13:case"end":return a.stop()}}),a,null,[[0,9]])})))()},handleSuccess:function(e,t){if(e&&e.data&&e.data.url){this.tempUrls.push(e.data.url),this.emitInput(this.tempUrls.join(","));var a={name:t.name,url:e.data.url,uid:t.uid||Date.now()},i=this.fileList.findIndex((function(e){return e.uid===a.uid}));i>=0?this.fileList.splice(i,1,a):this.fileList.push(a)}},handleRemove:function(e,t){var a=this.tempUrls.findIndex((function(t){return e.url===t}));-1!==a&&(this.tempUrls.splice(a,1),this.emitInput(this.tempUrls.join(",")))},emitInput:function(e){this.$emit("input",e)},handlePictureCardPreview:function(e){this.dialogImageUrl=e.url,this.dialogVisible=!0}}},u=c,d=(a("7e02"),a("8a34")),p=Object(d["a"])(u,i,n,!1,null,"364987a4",null);t["a"]=p.exports},bac3:function(e,t,a){},c900:function(e,t,a){}}]);