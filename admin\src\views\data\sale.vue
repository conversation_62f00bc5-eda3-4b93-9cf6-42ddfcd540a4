<template>
  <div class="app-container">

    <div class="filter-container">
      <el-input v-model="listQuery.admin" placeholder="管理员用户名" style="width: 200px; margin-right: 10px;" class="filter-item" />
      <el-date-picker
        class="filter-item"
        v-model="listQuery.times"
        type="datetimerange"
        :default-time="['00:00:00', '23:59:59']"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期">
      </el-date-picker>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getList">
        搜索
      </el-button>
    </div>

    <el-table v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">

      <el-table-column fixed label="日期" width="120" prop="date" />

      <el-table-column  fixed label="姓名" width="120" prop="admin.name" />
      
      <el-table-column label="管理员" width="120" prop="admin.username" />

      <el-table-column label="订单数" width="120" prop="orders" />

      <el-table-column label="订单总金额">
        <template slot-scope="scope">
          {{ scope.row.order_amount/100 }}
        </template>
      </el-table-column>

      <el-table-column label="核销数" width="120" prop="assets" />

      <el-table-column label="核销金额">
        <template slot-scope="scope">
          {{ scope.row.asset_amount/100 }}
        </template>
      </el-table-column>

      <el-table-column label="退款订单" width="120" prop="refunds" />

      <el-table-column label="退款金额">
        <template slot-scope="scope">
          {{ scope.row.refund_amount/100 }}
        </template>
      </el-table-column>

      <el-table-column label="已出行订单" width="120" prop="travels" />

      <el-table-column label="已出行金额">
        <template slot-scope="scope">
          {{ scope.row.travel_amount/100 }}
        </template>
      </el-table-column>
      
    </el-table>

  </div>
</template>

<script>

export default {
  name: 'Datalist',
  data() {
    return {
      oss: null,
      list: [],
      listLoading: true,
      listQuery:{}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$axios.get('/admin/data/sale', { params: this.listQuery }).then(response => {
        this.list = response.data.data
        this.listLoading = false
      })
    }
  }
}
</script>