<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.sn"
        placeholder="订单号"
        style="width: 300px"
        class="filter-item"
      />

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status == 0 && scope.row.status == 0"
            type="primary"
            size="small"
            icon="el-icon-check"
            @click="onPass(scope.row)"
          >
            确认
          </el-button>
        </template>
      </el-table-column>

      <el-table-column align="center" label="订单号" prop="orderInfo.sn" />
      <el-table-column align="center" label="出游日期" prop="travel_date" />
      <el-table-column align="center" label="出游人数" prop="num" />
      <el-table-column
        align="center"
        label="出行人名称"
        width="100"
        prop="name"
      />
      <el-table-column align="center" label="联系电话" prop="mobile" />
      <el-table-column align="center" label="状态">
        <template slot-scope="scope">
          <span v-if="scope.row.status == 1">已确认</span>
          <span v-if="scope.row.status == 0">待确认</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="券码图片" width="120">
        <template slot-scope="scope">
          <el-image v-for="pic in scope.row.code_pic"
            style="width: 100px; height: 100px"
            :src="pic"
            :preview-src-list="scope.row.code_pic"
          >
          </el-image>
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" prop="note" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
// import Pagination from '@/Wangeditor/Pagination'
import Pagination from "@/components/PaginationFixed";

export default {
  name: "Orderlist",
  components: { Pagination },
  filters: {},
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
      },
      oss: {},
      item: {},
      dialogVisible: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.$axios
        .get("/admin/orderbooks/list", { params: this.listQuery })
        .then((response) => {
          this.list = response.data.data;
          this.total = response.data.total;
          this.oss = response.ext;
          this.listLoading = false;
        });
    },
    onBack() {
      this.$axios
        .post("/admin/order/back", this.item)
        .then((res) => {
          this.dialogVisible = false;
          this.item = {};
          this.getList();
        })
        .catch((err) => {});
    },
    onPass(item) {
      this.$axios
        .post("/admin/orderbooks/updateStatus", { id: item.id })
        .then((res) => {
          this.dialogVisible = false;
          this.item = {};
          this.getList();
        })
        .catch((err) => {});
    },
  },
};
</script>
<style scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 52px; /* 分页条的高度，以避免内容重叠 */
}
::v-deep .el-image-viewer__close {
  background-color: #fff;
}
</style>
