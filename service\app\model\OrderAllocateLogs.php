<?php
namespace app\model;

class OrderAllocateLogs extends base
{
    protected $table = 'order_allocate_logs';

    /**
     * 关联管理员（客服）
     */
    public function admin()
    {
        return $this->belongsTo(Admins::class, 'admin_id')->visible(['id', 'name', 'username', 'route_type']);
    }

    /**
     * 关联管理员（客服）
     */
    public function shop()
    {
        return $this->belongsTo(Shops::class, 'shop_id')->visible(['id', 'name']);
    }

    /**
     * 获取门店信息（简化处理，直接返回shop_id）
     */
    public function getShopAttribute()
    {
        return [
            'id' => $this->shop_id,
            'name' => $this->shop_id ? "门店{$this->shop_id}" : '未知门店'
        ];
    }

    /**
     * 记录订单分配日志
     * @param string $orderSn 订单号
     * @param int $adminId 客服id
     * @param int $shopId 门店id
     * @param string $routePermission 管理员线路权限
     * @param array $extraData 额外数据
     * @return bool
     */
    public static function recordAllocateLog($orderSn, $adminId, $shopId, $routePermission = '', $extraData = [])
    {
        $log = new self();
        $log->order_sn = $orderSn;
        $log->admin_id = $adminId;
        $log->shop_id = $shopId;
        $log->allocate_time = time();
        $log->route_permission = $routePermission;

        // 记录额外信息
        if (!empty($extraData)) {
            $log->extra_data = json_encode($extraData, JSON_UNESCAPED_UNICODE);
        }

        return $log->save();
    }

    /**
     * 获取分配时间格式化
     */
    public function getAllocateTimeFormatAttr()
    {
        return date('Y-m-d H:i:s', $this->allocate_time);
    }

    /**
     * 获取额外数据
     */
    public function getExtraDataAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }
}
