(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3dbaaa10"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function l(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,a){var s=l(),r=t-s,o=20,c=0;e="undefined"===typeof e?500:e;var u=function(){c+=o;var t=Math.easeInOutQuad(c,s,r,e);n(t),c<e?i(u):a&&"function"===typeof a&&a()};u()}},4636:function(t,e,a){"use strict";a("879c")},"67f2":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[a("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},n=[],l=(a("374d"),a("09f4")),s={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(l["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(l["a"])(0,800)}}},r=s,o=(a("7d30"),a("8a34")),c=Object(o["a"])(r,i,n,!1,null,"28fdfbeb",null);e["a"]=c.exports},7140:function(t,e,a){},"7d30":function(t,e,a){"use strict";a("7140")},"879c":function(t,e,a){},a29f:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"订单号"},model:{value:t.listQuery.sn,callback:function(e){t.$set(t.listQuery,"sn",e)},expression:"listQuery.sn"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"标题"},model:{value:t.listQuery.product_name,callback:function(e){t.$set(t.listQuery,"product_name",e)},expression:"listQuery.product_name"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"手机号"},model:{value:t.listQuery.mobile,callback:function(e){t.$set(t.listQuery,"mobile",e)},expression:"listQuery.mobile"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"100px"},attrs:{placeholder:"主播"},model:{value:t.listQuery.zhubo,callback:function(e){t.$set(t.listQuery,"zhubo",e)},expression:"listQuery.zhubo"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"100px"},attrs:{placeholder:"客服"},model:{value:t.listQuery.admin,callback:function(e){t.$set(t.listQuery,"admin",e)},expression:"listQuery.admin"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"跟进状态"},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},[a("el-option",{key:"",attrs:{label:"请选择",value:""}}),t._l(t.status_arr,(function(t,e){return a("el-option",{key:e,attrs:{label:t,value:e}})}))],2),a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"时间"},model:{value:t.listQuery.timetype,callback:function(e){t.$set(t.listQuery,"timetype",e)},expression:"listQuery.timetype"}},[a("el-option",{key:"",attrs:{label:"请选择",value:""}}),t._l(t.timetype_arr,(function(t,e){return a("el-option",{key:e,attrs:{label:t,value:e}})}))],2),a("el-date-picker",{staticClass:"filter-item",attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","default-time":["00:00:00","23:59:59"],"end-placeholder":"结束日期"},model:{value:t.listQuery.times,callback:function(e){t.$set(t.listQuery,"times",e)},expression:"listQuery.times"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",clearable:"",placeholder:"预约状态"},model:{value:t.listQuery.appointment_status,callback:function(e){t.$set(t.listQuery,"appointment_status",e)},expression:"listQuery.appointment_status"}},[a("el-option",{key:"0",attrs:{label:"未预约",value:"0"}}),a("el-option",{key:"1",attrs:{label:"已预约(未处理)",value:"1"}}),a("el-option",{key:"2",attrs:{label:"已预约(已处理)",value:"2"}})],1),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(e){return t.getList(1)}}},[t._v(" 导出 ")]),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",disabled:0==t.multipleSelection.length,icon:"el-icon-refresh"},on:{click:function(e){return t.onCirculationAll()}}},[t._v(" 批量流转 ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":"",height:t.tableMaxHeight},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{align:"center",fixed:"",width:"200",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button-group",[a("el-button",{attrs:{type:e.row.backs&&0==e.row.backs.status?t.types[7]:t.types[4],size:"small",icon:"el-icon-refresh"},on:{click:function(a){return t.onCirculation(e.row)}}},[t._v(" "+t._s(e.row.backs&&0==e.row.backs.status?"流转中":"流转出")+" ")]),a("el-button",{attrs:{type:t.types[e.row.order_status],size:"small",icon:"el-icon-edit"},on:{click:function(a){return t.onInfo(e.row)}}},[t._v(" 跟进 ")]),a("el-button",{attrs:{size:"small",icon:"el-icon-thumb"},on:{click:function(a){return t.onOneClickRepair(e.row)}}},[t._v(" 同步 ")]),1==e.row.appointment_status?a("el-button",{attrs:{size:"small",icon:"el-icon-edit"},on:{click:function(a){return t.onOneClickYyHandle(e.row)}}},[t._v(" 预约处理 ")]):t._e()],1)]}}])}),a("el-table-column",{attrs:{align:"center",fixed:"",label:"电话",width:"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.mobile))]),a("span",{staticStyle:{display:"block","font-size":"12px"}},[t._v(t._s(e.row.mobileInfo.area)+"-"+t._s(e.row.mobileInfo.originalIsp))])]}}])}),a("el-table-column",{attrs:{align:"center",fixed:"",label:"平台",width:"80",prop:"os_name"}}),a("el-table-column",{attrs:{align:"center",fixed:"",label:"直播",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.is_zhibo?a("el-tag",[t._v("是")]):a("el-tag",{attrs:{type:"info"}},[t._v("否")])]}}])}),a("el-table-column",{attrs:{align:"center",fixed:"",label:"客服",width:"80",prop:"admin.username"}}),a("el-table-column",{attrs:{align:"center",label:"订单号",width:"180",prop:"sn"}}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"下单时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.create_at,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{width:"160px",align:"center",label:"派单时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.give_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"预约状态（抖音）",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{padding:"1px 5px","border-radius":"3px"},style:{color:t.order_status[e.row.appointment_status],border:"1px solid "+t.order_status[e.row.appointment_status]},attrs:{type:"primary"}},[t._v(" "+t._s(1==e.row.appointment_status?"已预约(未处理)":2==e.row.appointment_status?"已预约(已处理)":"未预约")+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"状态",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{padding:"1px 5px","border-radius":"3px"},style:{color:t.order_status[e.row.order_status],border:"1px solid "+t.order_status[e.row.order_status]},attrs:{type:"primary"}},[t._v(" "+t._s(e.row.order_status_name)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"跟进状态",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{padding:"1px 5px","border-radius":"3px"},style:{color:t.follow_status[e.row.status],border:"1px solid "+t.follow_status[e.row.status]},attrs:{type:"primary"}},[t._v(" "+t._s(e.row.status_name)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",width:"500px",label:"标题",prop:"product_name"}}),a("el-table-column",{attrs:{width:"500px",align:"center",label:"跟进备注",prop:"remark"}}),a("el-table-column",{attrs:{align:"center",label:"联系人",width:"120",prop:"contact"}}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"出行时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.travel_date,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"最后跟进时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.last_follow,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"核单",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.is_check?a("i",{staticClass:"el-icon-check"}):t._e(),2==e.row.is_check?a("i",{staticClass:"el-icon-close"}):t._e()]}}])}),a("el-table-column",{attrs:{align:"center",width:"138px",label:"分类",prop:"category_desc"}}),a("el-table-column",{attrs:{align:"center",label:"总金额",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.total_price/100))])]}}])}),a("el-table-column",{attrs:{align:"center",width:"80px",label:"人数",prop:"quantity"}}),a("el-table-column",{attrs:{align:"center",label:"主播",width:"80",prop:"anchor.username"}}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"修改时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.update_time,"{y}-{m}-{d} {h}:{i}")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.setMode}}),a("el-dialog",{attrs:{title:"订单跟进",visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("el-form",{attrs:{"label-width":"130px",model:t.item}},[a("el-form-item",{attrs:{label:"产品名称"}},[t._v(" "+t._s(t.item.product_name)+" ")]),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"产品状态"}},[t._v(" "+t._s(t.item.order_status_name)+" ")]),a("el-form-item",{attrs:{label:"数量"}},[t._v(" "+t._s(t.item.quantity)+" ")]),a("el-form-item",{attrs:{label:"联系人"}},[t._v(" "+t._s(t.item.contact)+" ")]),a("el-form-item",{attrs:{label:"手机"}},[t._v(" "+t._s(t.item.mobile)+" ")]),a("el-form-item",{attrs:{label:"下单时间"}},[t._v(" "+t._s(t._f("parseTime")(t.item.create_at,"{y}-{m}-{d} {h}:{i}"))+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"人员"}},[a("el-row",[a("el-col",{attrs:{span:3}},[t._v("大人")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"adult",placeholder:"大人"},model:{value:t.item.personnel.adult,callback:function(e){t.$set(t.item.personnel,"adult",e)},expression:"item.personnel.adult"}})],1),a("el-col",{attrs:{span:3}},[t._v("老人")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"old",placeholder:"老人"},model:{value:t.item.personnel.old,callback:function(e){t.$set(t.item.personnel,"old",e)},expression:"item.personnel.old"}})],1),a("el-col",{attrs:{span:3}},[t._v("小孩")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"child",placeholder:"小孩"},model:{value:t.item.personnel.child,callback:function(e){t.$set(t.item.personnel,"child",e)},expression:"item.personnel.child"}})],1)],1)],1),1!==t.item.status?a("el-form-item",{attrs:{label:"核销码"}},[a("el-input",{attrs:{name:"check_sn",placeholder:"请输入平台核销码"},model:{value:t.item.check_sn,callback:function(e){t.$set(t.item,"check_sn",e)},expression:"item.check_sn"}})],1):t._e(),2!==t.item.status?a("el-form-item",{attrs:{label:"加微信"}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.item.is_wechat,callback:function(e){t.$set(t.item,"is_wechat",e)},expression:"item.is_wechat"}},[t._v("已加微信")])],1):t._e(),a("el-form-item",{attrs:{required:"",pros:"travel_date",label:"出游日期"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期时间"},model:{value:t.item.travel_date,callback:function(e){t.$set(t.item,"travel_date",e)},expression:"item.travel_date"}})],1),1!==t.item.status?a("el-form-item",{attrs:{label:"返回日期"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期时间"},model:{value:t.item.travel_end,callback:function(e){t.$set(t.item,"travel_end",e)},expression:"item.travel_end"}})],1):t._e(),2!==t.item.status?a("el-form-item",{attrs:{required:"",pros:"next_follow",label:"下次跟进时间"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间"},model:{value:t.next_follow,callback:function(e){t.next_follow=e},expression:"next_follow"}})],1):t._e()],1)],1),a("el-form-item",{attrs:{label:"跟进状态"}},[t._l(t.status_arr,(function(e,i){return[i>0?a("el-radio",{attrs:{label:i,border:""},model:{value:t.item.status,callback:function(e){t.$set(t.item,"status",e)},expression:"item.status"}},[t._v(t._s(e))]):t._e()]}))],2),a("el-form-item",{attrs:{required:"",pros:"desc",label:"跟进说明"}},[a("el-input",{attrs:{type:"textarea"},model:{value:t.item.desc,callback:function(e){t.$set(t.item,"desc",e)},expression:"item.desc"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSave(t.item)}}},[t._v("保 存")])],1),a("el-tabs",{attrs:{type:"border-card"},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[a("el-tab-pane",{attrs:{name:"follow",label:"跟进记录"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.item.follow}},[a("el-table-column",{attrs:{label:"日期",width:"138"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{label:"跟进人",width:"110",prop:"name"}}),a("el-table-column",{attrs:{label:"状态",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.status_arr[e.row.status]))])]}}])}),a("el-table-column",{attrs:{prop:"desc",label:"跟进说明"}})],1)],1),a("el-tab-pane",{attrs:{name:"finance",label:"财务记录"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.item.finance}},[a("el-table-column",{attrs:{label:"日期"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("parseTime")(e.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{label:"类型",width:"110"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.type_arr[e.row.type]))])]}}])}),a("el-table-column",{attrs:{label:"状态",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.total/100))])]}}])})],1)],1)],1)],1),a("el-dialog",{attrs:{title:"纯核销",visible:t.dialog2Visible},on:{"update:visible":function(e){t.dialog2Visible=e}}},[a("el-form",{attrs:{"label-width":"160px",model:t.form}},[a("el-form-item",{attrs:{label:"平台"}},[a("el-radio",{attrs:{label:"1"},model:{value:t.form.os,callback:function(e){t.$set(t.form,"os",e)},expression:"form.os"}},[t._v("美团")])],1),a("el-form-item",{attrs:{label:"核销码"}},[a("el-input",{attrs:{placeholder:"请输入平台核销码"},model:{value:t.form.check_sn,callback:function(e){t.$set(t.form,"check_sn",e)},expression:"form.check_sn"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onPass(t.form)}}},[t._v("保 存")])],1)],1),a("el-dialog",{attrs:{title:"申请转出订单",visible:t.applyVisible},on:{"update:visible":function(e){t.applyVisible=e}}},[a("el-form",{ref:"ruleForm",attrs:{"label-width":"160px",model:t.item3,rules:t.rules}},[t.isAll?t._e():a("el-form-item",{attrs:{label:"标题:"}},[a("el-input",{attrs:{disabled:""},model:{value:t.item3.product_name,callback:function(e){t.$set(t.item3,"product_name",e)},expression:"item3.product_name"}})],1),t.isAll?t._e():a("el-form-item",{attrs:{label:"订单号:"}},[a("el-input",{attrs:{disabled:""},model:{value:t.item3.sn,callback:function(e){t.$set(t.item3,"sn",e)},expression:"item3.sn"}})],1),a("el-form-item",{staticStyle:{width:"600px"},attrs:{label:"流转对象:",prop:"flowObj"}},[a("el-select",{attrs:{placeholder:"请选择"},on:{change:t.onChange2},model:{value:t.item3.flowObj,callback:function(e){t.$set(t.item3,"flowObj",e)},expression:"item3.flowObj"}},[a("el-form-item",{staticStyle:{display:"inline-flex","text-align":"left",width:"770px"}},t._l(t.adminList,(function(t){return a("el-option",{key:t.value,staticStyle:{width:"250px",display:"inline-flex","word-break":"break-all"},attrs:{label:t.username,value:t.id}})})),1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t.item3.backs&&0==t.item3.backs.status?a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onCancel(t.item3.flowObj)}}},[t._v("取 消")]):a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onCirculationSave(t.item3.flowObj)}}},[t._v("确 认")])],1)],1)],1)},n=[],l=a("d00a"),s=a("d09a"),r=a("0fc4"),o=a("7921"),c=(a("e224"),a("4cc3"),a("374d"),a("5227"),a("67f2")),u=a("f8b7"),d={name:"Orderlist",components:{Pagination:c["a"]},data:function(){return{active:"follow",types:{0:"",1:"",2:"",3:"primary",4:"success",5:"warning",6:"danger",7:"info"},types2:{1:"primary",2:"success",3:"warning"},status_arr:["待跟进","跟进中","已核销","核销失败","放弃跟单","加入公海"],type_arr:["-","收益","支出"],timetype_arr:{},order_status:["#9e9f9c","#04bcd9","#fc9904","#1193f4","#48b14b","#eb1662","#9d1cb5"],follow_status:["#9e9f9c","#04bcd9","#fc9904","#1193f4","#48b14b","#eb1662"],options:[],value:null,next_follow:null,list:[],total:0,listLoading:!0,listQuery:{page:1,limit:10,times:[],status:null,admin:null,zhubo:null,os_status:[],appointment_status:""},item:{next_follow:"",personnel:{adult:""}},follow:[],dialogVisible:!1,dialog2Visible:!1,applyVisible:!1,oss:[],isSynchronization:!1,item3:{sn:null,backs:null,flowObj:"",os:null},multipleSelection:[],sn:[],adminList:[],form:{},isAll:!1,rules:{flowObj:[{required:!0,message:"请选择流转对象",trigger:"change"}]}}},created:function(){var t=this;return Object(o["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.listQuery.zhubo=t.$route.query.zhubo||null,t.$route.query.start&&t.$route.query.end&&(t.listQuery.times=[t.$route.query.start,t.$route.query.end]),t.setQuery("status"),t.setQuery("os_status"),t.setQuery("times"),t.setQuery("appointment_status"),t.getShortcutContent(),t.getAdminList();case 8:case"end":return e.stop()}}),e)})))()},mounted:function(){console.log(this.$route.query),this.setMode(),this.$route.query.id&&this.onInfo({id:this.$route.query.id})},computed:{tableMaxHeight:function(){return window.innerHeight-320+"px"}},watch:{$route:function(t,e){this.onInfo({id:this.$route.query.id})}},methods:{handleSelectionChange:function(t){this.multipleSelection=t;var e=[];this.multipleSelection.map((function(t){e.push(t.sn)})),this.sn=e},setQuery:function(t){this.$route.query.hasOwnProperty(t)?this.listQuery[t]=this.$route.query[t]:this.listQuery[t]=""},setMode:function(){var t=this;return Object(o["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getList();case 2:case"end":return e.stop()}}),e)})))()},setOneClickRepair:function(){var t=this;return Object(o["a"])(Object(r["a"])().mark((function e(){var a;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.list.map((function(t){return t.id})),e.next=3,t.onOneClickRepair({id:a.join()});case 3:case"end":return e.stop()}}),e)})))()},getList:function(t){var e=this;return Object(o["a"])(Object(r["a"])().mark((function a(){var i,n;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.listQuery.excel=null,e.listQuery.is_refunded=1,1!=t){a.next=8;break}return e.listQuery.excel=1,i=e.listQuery.times[0]instanceof Date,n=Object(s["a"])(Object(s["a"])({},e.listQuery),{},{times:[i?e.listQuery.times[0].toISOString():"",i?e.listQuery.times[1].toISOString():""]}),window.open("/admin/order/index?"+e.objectToQuery(n)),a.abrupt("return");case 8:return a.next=10,e.$axios.get("/admin/order/index",{params:e.listQuery}).then((function(t){e.list=t.data.data,e.total=t.data.total,e.timetype_arr=t.ext.timetype,e.oss=t.ext.oss,e.listLoading=!1,e.isSynchronization=!0}));case 10:case"end":return a.stop()}}),a)})))()},objectToQuery:function(t){return Object.keys(t).map((function(e){var a=t[e];return void 0==a||null==a?"":encodeURIComponent(e)+"="+encodeURIComponent(a)})).join("&")},onInfo:function(t){var e=this;this.value=null,this.next_follow=null,this.$set(t,"next_follow",null),this.active="follow",this.$axios.get("/admin/order/info",{params:{id:t.id}}).then((function(t){e.item=t.data,e.dialogVisible=!0})).catch((function(t){}))},resetForm:function(t){this.$refs[t].resetFields()},getAdminList:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.$axios.get("/admin/admin/index",{params:{limit:100,status:1,is_order:1,type_desc:e}}).then((function(e){t.adminList=e.data.data,t.listLoading=!1})).catch((function(t){}))},onCirculation:function(t){this.getAdminList(t.category_desc),this.applyVisible=!0,this.isAll=!1,this.item3=Object(s["a"])(Object(s["a"])({},t),{},{os:Number(t.os)}),console.log(this.item3),this.item3.backs&&this.item3.backs.admin_id?this.item3.flowObj=this.item3.backs.admin_id:this.resetForm("ruleForm")},onCirculationAll:function(){this.applyVisible=!0,this.isAll=!0},onCirculationSave:function(t){var e=this;this.$refs.ruleForm.validate(function(){var a=Object(o["a"])(Object(r["a"])().mark((function a(i){var n;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!i){a.next=11;break}if(!e.isAll){a.next=5;break}e.$confirm("是否批量流转订单","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(o["a"])(Object(r["a"])().mark((function a(){return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(u["c"])({sn:e.sn,to_admin_id:t});case 2:n=a.sent,n.data&&(e.$message({message:"批量流转订单成功",type:"success"}),e.applyVisible=!1,e.isAll=!1,e.getList());case 4:case"end":return a.stop()}}),a)})))),a.next=9;break;case 5:return a.next=7,Object(u["a"])({sn:e.item3.sn,os:e.item3.os,to_admin_id:t});case 7:n=a.sent,n.data&&(e.$message({message:"流转订单成功",type:"success"}),e.applyVisible=!1,e.isAll=!1,e.getList());case 9:a.next=12;break;case 11:return a.abrupt("return",!1);case 12:case"end":return a.stop()}}),a)})));return function(t){return a.apply(this,arguments)}}())},onCancel:function(){var t=this;this.$refs.ruleForm.validate((function(e){if(!e)return!1;t.$axios.post("/admin/order/backcancel",{id:t.item3.id}).then((function(e){t.applyVisible=!1,t.isAll=!1,t.getList()})).catch((function(t){console.log(t)}))}))},onBack:function(){var t=this;this.$axios.post("/admin/order/back",this.item).then((function(e){t.dialogVisible=!1,t.item={},t.getList()})).catch((function(t){}))},onSave:function(t){var e=this;this.$axios.post("/admin/order/save",{id:t.id,check_sn:t.check_sn,is_wechat:t.is_wechat,travel_end:t.travel_end,travel_date:t.travel_date,desc:t.desc,status:t.status,next_follow:this.next_follow,personnel:this.item.personnel}).then((function(t){e.dialogVisible=!1,e.item={next_follow:"",personnel:{adult:""}},e.$router.push({path:"/order/refunded"})})).catch((function(t){}))},onPass:function(t){var e=this;this.$axios.post("/admin/order/pass",{check_sn:t.check_sn}).then((function(t){e.dialog2Visible=!1,e.form={}})).catch((function(t){}))},onChange:function(t){this.$set(this.item,"desc",t+(void 0!=this.item.desc?this.item.desc:""))},onChange2:function(t){this.$set(this.item,"to_admin_id",t+(void 0!=this.item.admin_id?this.item.admin_id:""))},handleChange:function(t){console.log(t)},getShortcutContent:function(){var t=this;this.listLoading=!0,this.$axios.get("/admin/shortcutContent/list",{params:{page:1,limit:50,status:1}}).then((function(e){var a,i=Object(l["a"])(e.data.data);try{for(i.s();!(a=i.n()).done;){var n=a.value;t.options.push({value:n.id,label:n.content})}}catch(s){i.e(s)}finally{i.f()}})).catch((function(){}))},onOneClickRepair:function(t){},onOneClickYyHandle:function(t){var e=this;this.$axios.post("/admin/order/changeAppointmentStatus",{id:t.id}).then((function(t){e.dialogVisible=!1,e.$notify({title:"成功",message:"已处理",type:"success"})})).catch((function(t){e.$notify.error({title:"错误",message:t})}))}}},p=d,m=(a("4636"),a("8a34")),f=Object(m["a"])(p,i,n,!1,null,"2abd1cfe",null);e["default"]=f.exports},f8b7:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"c",(function(){return l})),a.d(e,"b",(function(){return s})),a.d(e,"d",(function(){return r}));var i=a("b775");function n(t){return Object(i["a"])({url:"/admin/order/back",method:"post",data:t})}function l(t){return Object(i["a"])({url:"admin/order/backBatch",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/admin/order-xs/back",method:"post",data:t})}function r(t){return Object(i["a"])({url:"admin/order-xs/backBatch",method:"post",data:t})}}}]);