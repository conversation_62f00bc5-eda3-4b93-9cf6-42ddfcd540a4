<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    dot: {
      type: Number,
      default: 0
    }
  },
  render(h, context) {
    const { icon, title, dot } = context.props
    const vnodes = []

    if (icon) {
      if (icon.includes('el-icon')) {
        vnodes.push(<i class={[icon, 'sub-el-icon']} />)
      } else {
        vnodes.push(<svg-icon icon-class={icon}/>)
      }
    }

    if (title) {
      vnodes.push(<span slot='title' class="menu-title">
        {(title)}
        {dot > 0 && <span class="dot-badge">{dot}</span>}
      </span>)
    }
    return vnodes
  }
}
</script>

<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}

.menu-title {
  position: relative;
  display: inline-block;
}

.dot-badge {
  position: absolute;
  top: 10px;
  right: -24px;
  background-color: #f56c6c;
  color: #fff;
  border-radius: 8px;
  font-size: 10px;
  padding: 0 4px;
  height: 14px;
  line-height: 14px;
  min-width: 14px;
  text-align: center;
  box-shadow: 0 0 0 1px #fff;
}
</style>
