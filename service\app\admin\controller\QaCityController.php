<?php

namespace app\admin\controller;

use app\model\QaCitys;
use support\Log;
use support\Request;

class QaCityController extends base
{
    /**
     * cms list
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DbException
     */
    public function getQaCityList(Request $request)
    {
        $list = QaCitys::order('sort desc')->where('shop_id', $request->admin->shop_id)->paginate($request->get('limit',10));

        return $this->success($list);
    }

    public function getQaCity(Request $request)
    {
        $list = QaCitys::order('sort desc')->select();

        return $this->success($list);
    }

    public function addQaCity(Request $request)
    {
        $post = $request->post();
        if (empty($post['city_name'])) return $this->error(2001, 'city_name data cannot be empty!');

        try {
            $post->shop_id = $request->admin->shop_id;
            $data = QaCitys::create($post);
        } catch (\Exception $e) {
            return $this->error(2002, $e->getMessage());
        }
        return $this->success($data);
    }

    public function editQaCity(Request $request)
    {
        $post = $request->post();
        if (empty($post['city_id'])) return $this->error(2001, 'id data cannot be empty!');
        $city_id = $post['city_id'];
        unset($post['city_id']);

        try {
            $data = QaCitys::where('city_id',$city_id)->update($post);
        } catch (\Exception $e) {
            return $this->error(2002, $e->getMessage());
        }
        return $this->success($data);
    }

    public function delQaCity(Request $request)
    {
        $city_id = $request->post('city_id');
        if (empty($city_id)) return $this->error(2001, 'city_id data cannot be empty!');
        try {
            $data = QaCitys::where('city_id',$city_id)->delete();
        } catch (\Exception $e) {
            return $this->error(2002, $e->getMessage());
        }
        return $this->success($data);
    }
}
