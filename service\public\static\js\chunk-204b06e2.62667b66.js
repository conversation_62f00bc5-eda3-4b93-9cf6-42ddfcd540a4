(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-204b06e2"],{"09f4":function(t,e,i){"use strict";i.d(e,"a",(function(){return l})),Math.easeInOutQuad=function(t,e,i,n){return t/=n/2,t<1?i/2*t*t+e:(t--,-i/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function a(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(t,e,i){var l=o(),s=t-l,r=20,c=0;e="undefined"===typeof e?500:e;var u=function(){c+=r;var t=Math.easeInOutQuad(c,l,s,e);a(t),c<e?n(u):i&&"function"===typeof i&&i()};u()}},"1ef1":function(t,e,i){},"225f":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-container"},[i("div",{staticClass:"filter-container"},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"300px"},attrs:{placeholder:"订单号"},model:{value:t.listQuery.sn,callback:function(e){t.$set(t.listQuery,"sn",e)},expression:"listQuery.sn"}}),i("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"状态"},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},[i("el-option",{attrs:{label:"未处理",value:"0"}},[t._v("未处理")]),i("el-option",{attrs:{label:"已同意",value:"1"}},[t._v("已同意")]),i("el-option",{attrs:{label:"拒绝",value:"2"}},[t._v("拒绝")]),i("el-option",{attrs:{label:"取消",value:"3"}},[t._v("取消")])],1),i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"微信号/手机号"},model:{value:t.listQuery.mobile,callback:function(e){t.$set(t.listQuery,"mobile",e)},expression:"listQuery.mobile"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v(" 搜索 ")]),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-edit-outline"},on:{click:function(e){t.dialogVisible=!0}}},[t._v(" 申请转入 ")])],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[i("el-table-column",{attrs:{align:"center",fixed:"",width:"220",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[0==e.row.self&&0==e.row.status?i("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-check"},on:{click:function(i){return t.onPass(e.row)}}},[t._v(" 同意 ")]):t._e(),0==e.row.self&&0==e.row.status?i("el-button",{attrs:{type:"success",size:"small",icon:"el-icon-close"},on:{click:function(i){return t.onRefuse(e.row)}}},[t._v(" 拒绝 ")]):t._e(),3==e.row.self&&0==e.row.status?i("el-button",{attrs:{type:"info",size:"small",icon:"el-icon-close"},on:{click:function(i){return t.onCancel(e.row)}}},[t._v(" 取消流转 ")]):t._e()]}}])}),i("el-table-column",{attrs:{align:"center",label:"转出",width:"80",prop:"outto.username"}}),i("el-table-column",{attrs:{align:"center",label:"转入",width:"80",prop:"into.username"}}),i("el-table-column",{attrs:{align:"center",label:"申请者",width:"80",prop:"apply.username"}}),i("el-table-column",{attrs:{align:"center",label:"订单号",width:"220",prop:"orders.sn"}}),i("el-table-column",{attrs:{align:"center",label:"联系方式",width:"170"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center"}},[i("el-button",{attrs:{type:"text"},on:{click:function(i){return t.copyToClipboard(e.row.orders.mobile)}}},[t._v(t._s(e.row.orders.mobile))])],1),i("div",{staticStyle:{display:"flex","align-items":"center"}},[i("span",{staticStyle:{display:"block","font-size":"12px"}},[t._v("微信号：")]),e.row.orders.wechat?i("el-button",{attrs:{type:"text"},on:{click:function(i){return t.copyToClipboard(e.row.orders.wechat)}}},[t._v(t._s(e.row.orders.wechat))]):i("span",[t._v("-")])],1)]}}])}),i("el-table-column",{attrs:{align:"center",label:"产品",prop:"orders.product_name"}}),i("el-table-column",{attrs:{align:"center",label:"总金额",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.orders&&e.row.orders.total_price/100))])]}}])}),i("el-table-column",{attrs:{align:"center",label:"状态",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("statusFilter")(e.row.status)))])]}}])}),i("el-table-column",{attrs:{width:"180px",align:"center",label:"申请时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("parseTime")(e.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),i("el-table-column",{attrs:{width:"180px",align:"center",label:"修改时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("parseTime")(e.row.update_time,"{y}-{m}-{d} {h}:{i}")))])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}),i("el-dialog",{attrs:{title:"申请转入订单",visible:t.dialogVisible},on:{"update:visible":function(e){t.dialogVisible=e}}},[i("el-form",{attrs:{"label-width":"160px",model:t.item}},[i("el-form-item",{attrs:{label:"订单号"}},[i("el-input",{attrs:{name:"check_sn",placeholder:"请输入订单号"},model:{value:t.item.sn,callback:function(e){t.$set(t.item,"sn",e)},expression:"item.sn"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onBack()}}},[t._v("确 认")])],1)],1)],1)},a=[],o=i("67f2"),l={name:"Orderlist",components:{Pagination:o["a"]},filters:{statusFilter:function(t){var e={1:"同意",0:"申请中",2:"拒绝",3:"取消"};return e[t]}},data:function(){return{list:[],total:0,listLoading:!0,listQuery:{page:1,limit:10},oss:{},item:{},dialogVisible:!1}},created:function(){this.getList()},methods:{stripHtml:function(t){var e=document.createElement("div");return e.innerHTML=t,e.textContent||e.innerText||""},copyToClipboard:function(t){var e=this.stripHtml(t),i=document.createElement("textarea");i.value=e,document.body.appendChild(i),i.select(),document.execCommand("copy"),document.body.removeChild(i),this.$message({showClose:!0,message:"内容已复制",type:"success"})},getList:function(){var t=this;this.$axios.get("/admin/order-xs/backlist",{params:this.listQuery}).then((function(e){t.list=e.data.data,t.total=e.data.total,t.oss=e.ext,t.listLoading=!1}))},onBack:function(){var t=this;this.$axios.post("/admin/order-xs/back",this.item).then((function(e){t.dialogVisible=!1,t.item={},t.getList()})).catch((function(t){}))},onPass:function(t){var e=this;this.$axios.post("/admin/order-xs/backpass",{id:t.id}).then((function(t){e.dialogVisible=!1,e.item={},e.getList()})).catch((function(t){}))},onRefuse:function(t){var e=this;this.$axios.post("/admin/order-xs/backrefuse",{id:t.id}).then((function(t){e.dialogVisible=!1,e.item={},e.getList()})).catch((function(t){}))},onCancel:function(t){var e=this;this.$axios.post("/admin/order-xs/backcancel",{id:t.id}).then((function(t){e.dialogVisible=!1,e.item={},e.getList()})).catch((function(t){}))}}},s=l,r=(i("e26c"),i("8a34")),c=Object(r["a"])(s,n,a,!1,null,"41458eaf",null);e["default"]=c.exports},"67f2":function(t,e,i){"use strict";var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[i("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},a=[],o=(i("374d"),i("09f4")),l={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(o["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(o["a"])(0,800)}}},s=l,r=(i("7d30"),i("8a34")),c=Object(r["a"])(s,n,a,!1,null,"28fdfbeb",null);e["a"]=c.exports},7140:function(t,e,i){},"7d30":function(t,e,i){"use strict";i("7140")},e26c:function(t,e,i){"use strict";i("1ef1")}}]);