<?php

namespace base\auth;

use support\Container;
use base\base\DS;

/**
 *
 * @method int|string userId() 用户ID
 * @method int|string username() 用户名称
 * @method int|string realName() 用户昵称
 * @method int|string appid() 当前应用appid
 */
class UserInfo extends DS
{
    public function __call($method, $args)
    {
        return $this->get($method);
    }

    /**
     * 初始化
     *
     * @param $user
     *
     * @return void
     */
    public static function init($user): void
    {
        Container::set(__CLASS__, new UserInfo($user));
    }
}