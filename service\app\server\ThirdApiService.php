<?php
namespace app\server;

use app\model\Orders;
use app\model\ThirdMobileLogs;
use http\Client;
use support\Log;
use support\Redis;

/**
 * 阿里云市场相关接口
 */
class ThirdApiService {
    private $notice = '%s token已过期，请及时更换';

    /**
     * @param $mobile
     * @return array|mixed
     * @throws \think\db\exception\BindParamException
     */
    public function getMobileArea($mobile) {
        $host = "https://jumcvit.market.alicloudapi.com";
        $path = "/mobile/area";
        $method = "POST";
        $appcode = "1a2eba42c68a40739b62feadfa5add52";
        $headers = array();
        array_push($headers, "Authorization:APPCODE " . $appcode);
        //根据API的要求，定义相对应的Content-Type
        array_push($headers, "Content-Type".":"."application/x-www-form-urlencoded; charset=UTF-8");
        $bodys = "mobile_number=" . $mobile;
        $url = $host . $path;

        $aliData = $this->curlMobileRequest($url, $method, $headers, $bodys);
        if (isset($aliData['code']) && $aliData['code'] == 200) {
            ThirdMobileLogs::query()->insert([
                'mobile' => $mobile,
                'area' => $aliData['data']['area'],
                'originalIsp' => $aliData['data']['originalIsp'],
            ]);
        }

        return $aliData['data'] ?? [];
    }

    /**
     * @param $url
     * @param $method
     * @param $headers
     * @param $bodys
     * @return mixed
     */
    private function curlMobileRequest($url, $method, $headers, $bodys){
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        //设定返回信息中是否包含响应信息头，启用时会将响应信息头作为数据流输出，true 表示输出信息头, false表示不输出信息头
        //如果想将响应结果json字符串转为json数组，建议将 CURLOPT_HEADER 设置成 false
        curl_setopt($curl, CURLOPT_HEADER, true);
        if (1 == strpos("$".$url, "https://"))
        {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }
        curl_setopt($curl, CURLOPT_POSTFIELDS, $bodys);
        curl_close($curl);
        list($header, $body) = explode("\r\n\r\n", curl_exec($curl), 2);
        return json_decode($body, true);
    }

    /**
     * 发送企微提醒
     * @param $message
     * @return mixed
     */
    public function weComNotice($os) {
        // $url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e138b55c-2886-4729-922e-9a8c753f75ee';
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d948b584-a500-40f2-a2d4-08d5e8c6186a';
        $method = 'POST';
        $message = sprintf($this->notice, Orders::OSS[$os] ?? '');
        $bodys = [
            'msgtype' => 'text',
            'text' => [
                'content' => $message
            ]
        ];

        $key = 'notice-' . $os;
        if (Redis::setNx($key, 1)) {
            Redis::expire($key, 600); // 十分钟内通知一次
            $this->curlRequest($url, $method, [], $bodys);
        }
        return true;
    }

    /**
     * 发送企微提醒
     * @param $message
     * @return mixed
     */
    public function weComNoticeV2($message) {
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d948b584-a500-40f2-a2d4-08d5e8c6186a';
        $method = 'POST';
        $bodys = [
            'msgtype' => 'text',
            'text' => [
                'content' => $message
            ],
            'mentioned_mobile_list' => ['15711970154']
        ];

        $this->curlRequest($url, $method, [], $bodys);
        return true;
    }

    /**
     * @param $url
     * @param $method
     * @param $headers
     * @param $bodys
     * @return mixed
     */
    private function curlRequest($url, $method, $headers, $bodys){
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        //设定返回信息中是否包含响应信息头，启用时会将响应信息头作为数据流输出，true 表示输出信息头, false表示不输出信息头
        //如果想将响应结果json字符串转为json数组，建议将 CURLOPT_HEADER 设置成 false
        curl_setopt($curl, CURLOPT_HEADER, true);
        if (1 == strpos("$".$url, "https://"))
        {
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        }
        if (is_array($bodys)) {
            $bodys = json_encode($bodys);
        }
        curl_setopt($curl, CURLOPT_POSTFIELDS, $bodys);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array_merge($headers, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($bodys)
        ]));
        curl_close($curl);
        list($header, $body) = explode("\r\n\r\n", curl_exec($curl), 2);
        Log::info(sprintf('third req:%s, res:%s', $url, $body));
        return json_decode($body, true);
    }
}
