
import config from '../../package.json';

// 1、window.localStorage 浏览器永久缓存
export const Local = {
  // 查看 v2.4.3版本更新日志
  setKey(key) {
    // @ts-ignore
    return `${config.name}:${key}`;
  },
  // 存储数据到本地缓存，设置过期时间
  setCache(key, value, expireTime) {
    const now = new Date().getTime();
    const item = {
      value: value,
      expire: now + expireTime * 1000 // 过期时间，单位是毫秒
    };
    localStorage.setItem(Local.setKey(key), JSON.stringify(item));
  },

  // 从本地缓存读取数据，检查是否过期
  getCache(key) {
    const itemStr = localStorage.getItem(Local.setKey(key));
    if (itemStr) {
      const item = JSON.parse(itemStr);
      const now = new Date().getTime();
      if (now < item.expire) {
        return item.value;
      } else {
        // 数据已过期，清除缓存
        localStorage.removeItem(key);
      }
    }
    return null;
  },
  // 设置永久缓存
  set(key, val) {
    window.localStorage.setItem(Local.setKey(key), JSON.stringify(val));
  },
  // 获取永久缓存
  get(key) {
    let json = window.localStorage.getItem(Local.setKey(key));
    return JSON.parse(json);
  },
  // 移除永久缓存
  remove(key) {
    window.localStorage.removeItem(Local.setKey(key));
  },
  // 移除全部永久缓存
  clear() {
    window.localStorage.clear();
  },
};

// 2、window.sessionStorage 浏览器临时缓存
export const Session = {
  // 设置临时缓存
  set(key, val) {
    window.sessionStorage.setItem(Local.setKey(key), JSON.stringify(val));
  },
  // 获取临时缓存
  get(key) {
    let json = window.sessionStorage.getItem(Local.setKey(key));
    return JSON.parse(json);
  },
  // 移除临时缓存
  remove(key) {
    window.sessionStorage.removeItem(Local.setKey(key));
  },
  // 移除全部临时缓存
  clear() {
    window.sessionStorage.clear();
  },
};
