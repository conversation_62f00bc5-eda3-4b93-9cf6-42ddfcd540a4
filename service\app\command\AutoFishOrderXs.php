<?php

namespace app\command;

use app\model\Admins;
use app\model\FilterMobiles;
use app\model\Orders;
use app\model\OrdersXs;
use app\model\OrdersXs as OrdersModel;
use support\Log;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;


class AutoFishOrderXs extends Command
{
    protected static $defaultName = 'auto_fish_direct_order_xs';
    protected static $defaultDescription = '分配线索订单。';

    /**
     * @return void
     */
    protected function configure()
    {
        // $this->addArgument('name', InputArgument::OPTIONAL, 'Name description');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output($output, self::$defaultName . " started, " . date('Y-m-d H:i:s'));

        $orders = $this->orders();
        $total = count($orders);

        $this->output($output, "got {$total} orders");

        if (1 > $total) {
            return self::SUCCESS;
        }

        foreach($orders as $order) {
            $this->giveOrder($order);
        }

        return self::SUCCESS;
    }

    private function output(OutputInterface $output, string $message)
    {
        $output->writeln(sprintf('{"time":"%s", "message":"%s"}', date('Y-m-d H:i:s'), $message));
    }

    /**
     * 重新分配订单
     * @param $order
     * @throws \think\db\exception\BindParamException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function giveOrder($order) {
        try {
            $adminId = 0;
            $allocateMethod = '';
            $extraData = [
                'mobile' => $order->mobile,
                'product_id' => $order->product_id,
                'source' => '自动重新分配私域订单'
            ];

            if (FilterMobiles::isFilterMobile($order->mobile)) {
                $adminId = 60;
                $allocateMethod = '过滤手机号分配';
            } else {
                try {
                    $adminId = \app\server\OrdersXs::poolUser(1, $order->product_id);
                    $allocateMethod = '在线客服分配';
                } catch (\Exception $exception) {
                    Log::info(sprintf('dy create order fail:%s, order_id:%s', $exception->getMessage(), $order['dy_order_id']));
                    $allocateMethod = '分配失败';
                }
            }

            if ($adminId) {
                $order->shop_id  = Admins::where('id', $adminId)->value('shop_id');

                // 记录分配日志
                \app\server\OrdersXs::recordAllocateLog(
                    $order->sn,
                    $adminId,
                    $order->shop_id,
                    $allocateMethod,
                    $extraData
                );
            }

            $order->admin_id = $adminId;
            $order->give_time = time();
            $order->save();
        } catch (\Exception $e) {
            Log::info(sprintf('重新分配订单失败：%s, 失败原因：%s', json_encode($order), $e->getMessage().$e->getFile().$e->getLine())  );
        }
    }

    /**
     * @return OrdersModel[]
     */
    private function orders(): array {
        $list = OrdersModel::where('admin_id', 0)
            ->select()
            ->all();

        return $list;
    }
}
