<?php
namespace app\model;

class Works extends base
{
    public function admin() {
        return $this->belongsTo(Admins::class, 'admin_id')->visible(['name','username','avatar','is_anchor']);
    }

    public function getOssAttr() {
        return explode(',',$this->os);
    }

    public function getTotalAttr() {
        $item = Orders::whereBetween("create_at",[strtotime($this->start)*1000, strtotime($this->end)*1000 + 999])
            ->whereIN('os', explode(',',$this->os))
            ->where('is_zhibo',2)
            ->fieldRaw('SUM(total_price) as total_price')->find();
        return $item->total_price;
    }

}