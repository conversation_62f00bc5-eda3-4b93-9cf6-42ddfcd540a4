# 飞猪度假回调接口实现

本项目实现了飞猪度假平台的回调接口，包括创建订单、订单查询、退款和修改订单四个主要功能。

## 功能概述

根据飞猪度假开发文档，实现了以下四个核心回调接口：

1. **创建订单** - 接收飞猪平台的订单创建请求
2. **订单查询** - 处理飞猪平台的订单查询请求  
3. **退款处理** - 处理飞猪平台的退款请求
4. **修改订单** - 处理飞猪平台的订单修改请求

## 技术架构

### 核心组件

- **FliggyController** - 主要的回调控制器，处理飞猪平台的回调请求
- **FliggySignatureService** - 签名验证服务，确保请求的安全性
- **FliggyOrders** - 飞猪订单模型，管理订单数据
- **FliggyOrderLogs** - 订单日志模型，记录所有操作日志
- **FliggyConfigs** - 配置模型，管理应用密钥配置

### 数据库表结构

1. **fliggy_orders** - 飞猪订单主表
2. **fliggy_order_logs** - 操作日志表
3. **fliggy_configs** - 配置表

## 接口规范

### 通用要求

- 协议：HTTPS POST
- Content-Type：application/json
- 签名验证：必须携带有效的签名信息

### 请求头参数

```
x-app-key: 应用公钥
x-timestamp: 时间戳（毫秒）
x-nonce: 随机字符串
x-signature: 签名
x-options-keys: 其他选项（可选）
Content-Type: application/json
```

### 响应格式

成功响应：
```json
{
  "success": true,
  "code": "ok",
  "message": "Processing successful",
  "data": { }
}
```

失败响应：
```json
{
  "success": false,
  "code": "PARAMS_ERROR",
  "message": "Signature information has expired, please update the signature information and try again"
}
```

## API接口

### 1. 创建订单

**接口地址：** `POST /api/fliggy/create-order`

**请求参数：**
- bizType: 业务类型（固定为2）
- fliggyOrderId: 飞猪订单号
- vendor: 系统商信息
- travellers: 出行人列表
- productList: 产品列表
- contacts: 联系人信息（可选）
- confirmTime: 二次确认时间（可选）

### 2. 订单查询

**接口地址：** `POST /api/fliggy/query-order`

**请求参数：**
- bizType: 业务类型（固定为2）
- fliggyOrderId: 飞猪订单号
- orderId: 系统商订单号（可选）

### 3. 退款处理

**接口地址：** `POST /api/fliggy/refund-order`

**请求参数：**
- bizType: 业务类型（固定为2）
- fliggyOrderId: 飞猪订单号
- orderId: 系统商订单号（可选）
- refundType: 退款类型（1-退款退单，2-退款不退单）
- refundReason: 退款原因
- refundFee: 退款金额（分）

### 4. 修改订单

**接口地址：** `POST /api/fliggy/update-order`

**请求参数：**
- fliggyOrderId: 飞猪订单号
- orderId: 系统商订单号（可选）
- travelDate: 出行日期（可选）
- travellers: 出行人信息（可选）
- orderInfo: 订单详细信息（可选）

## 签名算法

### 签名生成步骤

1. 对请求体内容进行SHA256哈希，转为小写
2. 生成待签名字符串：HTTP方法 + URL + 内容哈希 + 其他选项（换行符分隔）
3. 生成明文：公钥 + 时间戳 + 随机字符串 + 待签名字符串
4. 使用HMAC-SHA256算法生成签名，转为大写

### PHP实现示例

```php
function generateSignature($appKey, $appSecret, $httpMethod, $url, $contentString, $timestamp, $nonce, $optionsKeysString) {
    $contentHash = strtolower(hash('sha256', $contentString));
    $stringToSign = implode("\n", [$httpMethod, $url, $contentHash, $optionsKeysString]);
    $plainText = $appKey . $timestamp . $nonce . $stringToSign;
    $sign = strtoupper(hash_hmac('sha256', $plainText, $appSecret));
    return $sign;
}
```

## 部署说明

### 1. 数据库初始化

执行数据库迁移文件：
```sql
source service/database/migrations/create_fliggy_orders_table.sql
```

### 2. 配置应用密钥

在 `fliggy_configs` 表中添加或更新应用配置：
```sql
INSERT INTO fliggy_configs (app_key, app_secret, env, status, description) 
VALUES ('your-app-key', 'your-app-secret', 'production', 1, '生产环境配置');
```

### 3. 启动服务

```bash
cd service
php start.php start -d
```

## 测试工具

系统提供了测试接口，用于验证功能：

- **GET /api/fliggy-test/signature** - 测试签名生成
- **GET /api/fliggy-test/create-order** - 获取创建订单测试数据
- **GET /api/fliggy-test/query-order** - 获取查询订单测试数据
- **GET /api/fliggy-test/refund** - 获取退款测试数据
- **GET /api/fliggy-test/update-order** - 获取修改订单测试数据

每个测试接口都会返回相应的测试数据和curl命令示例。

## 日志记录

系统会自动记录所有操作日志到 `fliggy_order_logs` 表，包括：

- 请求数据
- 响应数据
- 操作类型
- 处理状态
- 错误信息（如有）
- 客户端IP和User-Agent

## 安全特性

1. **签名验证** - 所有请求必须通过签名验证
2. **时间戳验证** - 请求时间戳必须在5分钟有效期内
3. **重复订单检查** - 防止重复创建相同的订单
4. **状态验证** - 确保订单状态合法性
5. **详细日志** - 记录所有操作用于审计

## 错误处理

系统会返回标准的错误码：

- `PARAMS_ERROR` - 参数错误
- `SIGNATURE_ERROR` - 签名验证失败
- `ORDER_EXISTS` - 订单已存在
- `ORDER_NOT_FOUND` - 订单未找到
- `ORDER_ALREADY_REFUNDED` - 订单已退款
- `ORDER_CANCELLED` - 订单已取消
- `ORDER_CANNOT_UPDATE` - 订单无法修改
- `SYSTEM_ERROR` - 系统错误

## 注意事项

1. 确保数据库支持JSON字段类型（MySQL 5.7+）
2. 配置正确的时区设置
3. 定期清理过期的日志数据
4. 在生产环境中使用HTTPS协议
5. 合理配置日志级别和存储策略

## 联系方式

如有问题请联系开发团队或查看飞猪官方文档。 