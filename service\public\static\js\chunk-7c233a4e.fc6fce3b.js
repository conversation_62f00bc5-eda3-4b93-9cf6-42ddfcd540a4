(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7c233a4e"],{"54e0":function(e,t,a){"use strict";a("e234")},"6c2b":function(e,t,a){"use strict";a("6d50")},"6d50":function(e,t,a){},"7a0d":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("SoftPhone",{ref:"softphone",attrs:{visible:e.softphoneVisible,softphoneInstance:e.moorSoftphone,initialNumber:e.currentPhoneNumber,currentLoginType:e.currentLoginType},on:{close:function(t){e.softphoneVisible=!1},"login-type-change":e.onLoginTypeChange,"reinit-softphone":e.onReinitSoftphone}}),a("div",{staticClass:"filter-container"},[a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"私域线路"},model:{value:e.listQuery.product_id,callback:function(t){e.$set(e.listQuery,"product_id",t)},expression:"listQuery.product_id"}},[a("el-option",{key:"",attrs:{label:"全部",value:""}}),e._l(e.lineList,(function(e,t){return a("el-option",{key:t,attrs:{label:e.product_name,value:e.third_product_id}})}))],2),a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"微信号/手机号/标题"},model:{value:e.listQuery.mobile,callback:function(t){e.$set(e.listQuery,"mobile",t)},expression:"listQuery.mobile"}}),a("el-input",{staticClass:"filter-item",staticStyle:{width:"100px"},attrs:{placeholder:"客服"},model:{value:e.listQuery.admin,callback:function(t){e.$set(e.listQuery,"admin",t)},expression:"listQuery.admin"}}),a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"来源"},model:{value:e.listQuery.source,callback:function(t){e.$set(e.listQuery,"source",t)},expression:"listQuery.source"}},[a("el-option",{key:"",attrs:{label:"全部",value:""}}),e._l(e.source,(function(e,t){return a("el-option",{key:t,attrs:{label:e,value:t}})}))],2),a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"等级"},model:{value:e.listQuery.levels,callback:function(t){e.$set(e.listQuery,"levels",t)},expression:"listQuery.levels"}},[a("el-option",{key:"",attrs:{label:"全部",value:""}}),e._l(e.levels,(function(e,t){return a("el-option",{key:t,attrs:{label:e,value:t}})}))],2),a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"跟进状态"},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},[a("el-option",{key:"",attrs:{label:"请选择",value:""}}),e._l(e.status_arr,(function(e,t){return a("el-option",{key:t,attrs:{label:e,value:t}})}))],2),a("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{filterable:"",placeholder:"时间"},model:{value:e.listQuery.timetype,callback:function(t){e.$set(e.listQuery,"timetype",t)},expression:"listQuery.timetype"}},[a("el-option",{key:"",attrs:{label:"请选择",value:""}}),e._l(e.timetype_arr,(function(e,t){return a("el-option",{key:t,attrs:{label:e,value:t}})}))],2),a("el-date-picker",{staticClass:"filter-item",attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","default-time":["00:00:00","23:59:59"],"end-placeholder":"结束日期"},model:{value:e.listQuery.times,callback:function(t){e.$set(e.listQuery,"times",t)},expression:"listQuery.times"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.getList}},[e._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.getList(1)}}},[e._v(" 导出 ")]),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",disabled:0==e.multipleSelection.length,icon:"el-icon-refresh"},on:{click:function(t){return e.onCirculationAll()}}},[e._v(" 批量流转 ")]),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["admin","isprivate","franchisee"],expression:"['admin','isprivate', 'franchisee']"}],staticClass:"filter-item",attrs:{type:"success",icon:"el-icon-plus"},on:{click:e.showCreateOrderDialog}},[e._v(" 创建线索 ")]),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["admin","isprivate","franchisee"],expression:"['admin','isprivate', 'franchisee']"}],staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-refresh"},on:{click:e.dowload}},[e._v(" 下载导入模板 ")]),a("UploadExcelButton",{attrs:{name:"导入线索"},on:{refresh:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.list,border:"",fit:"","highlight-current-row":"",height:e.tableMaxHeight},on:{"selection-change":e.handleSelectionChange,"sort-change":e.orderSort}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{align:"center",fixed:"",width:"100",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button-group",[a("el-button",{attrs:{type:t.row.backs&&0==t.row.backs.status?e.types[7]:e.types[4],size:"small",icon:"el-icon-refresh"},on:{click:function(a){return e.onCirculation(t.row)}}},[e._v(" "+e._s(t.row.backs&&0==t.row.backs.status?"流转中":"流转出")+" ")]),a("el-button",{attrs:{type:e.types[t.row.order_status],size:"small",icon:"el-icon-edit"},on:{click:function(a){return e.onInfo(t.row)}}},[e._v(" 跟进 ")]),t.row.is_direct_mode&&1==t.row.appointment_status?a("el-button",{attrs:{size:"small",icon:"el-icon-thumb"},on:{click:function(a){return e.confirmOrder(t.row)}}},[e._v(" 确认接单 ")]):e._e(),t.row.is_direct_mode&&2==t.row.appointment_status?a("el-button",{attrs:{size:"small",icon:"el-icon-thumb"},on:{click:function(a){return e.confirmOrder(t.row,2)}}},[e._v(" 协助取消/退款 ")]):e._e(),1!=t.row.appointment_status||t.row.is_direct_mode?e._e():a("el-button",{attrs:{size:"small",icon:"el-icon-edit"},on:{click:function(a){return e.onOneClickYyHandle(t.row)}}},[e._v(" 预约处理 ")])],1)]}}])}),a("el-table-column",{attrs:{align:"center",fixed:"",label:"联系方式",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center"}},[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.copyToClipboard(t.row.mobile)}}},[e._v(e._s(t.row.mobile))])],1),a("span",{staticStyle:{display:"block","font-size":"12px"}},[e._v(e._s(t.row.mobileInfo.area)+"-"+e._s(t.row.mobileInfo.originalIsp))]),a("div",{staticStyle:{display:"flex","align-items":"center"}},[a("span",{staticStyle:{display:"block","font-size":"12px"}},[e._v("微信号：")]),t.row.wechat?a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.copyToClipboard(t.row.wechat)}}},[e._v(e._s(t.row.wechat))]):a("span",[e._v("-")])],1)]}}])}),a("el-table-column",{attrs:{align:"center",fixed:"",label:"客服",width:"80",prop:"admin.username"}}),a("el-table-column",{attrs:{align:"center",width:"150px",label:"标题",prop:"product_name"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.row.product_name,placement:"top"}},[a("span",{staticClass:"ellipsis-text",on:{click:function(a){return e.copyToClipboard(t.row.product_name)}}},[e._v(" "+e._s(t.row.product_name)+" ")])])]}}])}),a("el-table-column",{attrs:{align:"center",label:"来源",width:"80",prop:"source_name"}}),a("el-table-column",{attrs:{align:"center",width:"100px",label:"等级",prop:"level_name"}}),a("el-table-column",{attrs:{width:"200px",align:"center",label:"跟进备注",prop:"remark"}}),a("el-table-column",{attrs:{align:"center",label:"跟进状态",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{padding:"1px 5px","border-radius":"3px"},style:{color:e.follow_status[t.row.status],border:"1px solid "+e.follow_status[t.row.status]},attrs:{type:"primary"}},[e._v(" "+e._s(t.row.status_name)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"粉丝状态",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{padding:"1px 5px","border-radius":"3px"},style:{color:e.follow_status[t.row.fans_status],border:"1px solid "+e.follow_status[t.row.fans_status]},attrs:{type:"primary"}},[e._v(" "+e._s(t.row.fans_status?"有效":"无效")+" ")])]}}])}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"入粉时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("parseTime")(t.row.create_at,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{width:"160px",align:"center",label:"派单时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("parseTime")(t.row.give_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{width:"160px",align:"center",label:"计划出行日期"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.travel_date))])]}}])}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"计划出行时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("parseTime")(t.row.travel_date,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"最后跟进时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("parseTime")(t.row.last_follow,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"总金额",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.total_price/100))])]}}])}),a("el-table-column",{attrs:{align:"center",width:"80px",label:"人数",prop:"quantity"}}),a("el-table-column",{attrs:{width:"138px",align:"center",label:"修改时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("parseTime")(t.row.update_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"凭证图片",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.evidence_image?a("el-image",{staticStyle:{width:"60px",height:"60px"},attrs:{src:t.row.evidence_image,fit:"cover","preview-src-list":[t.row.evidence_image]}}):a("span",[e._v("-")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){return e.$set(e.listQuery,"page",t)},"update:limit":function(t){return e.$set(e.listQuery,"limit",t)},pagination:e.setMode}}),a("el-dialog",{attrs:{title:"订单跟进",visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{attrs:{"label-width":"130px",model:e.item}},[a("el-form-item",{attrs:{label:"产品名称"}},[e._v(" "+e._s(e.item.product_name)+" ")]),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"产品状态"}},[e._v(" "+e._s(e.item.order_status_name)+" ")]),a("el-form-item",{attrs:{label:"手机"}},[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.copyToClipboard(e.item.mobile)}}},[e._v(e._s(e.item.mobile))])],1),a("el-form-item",{attrs:{label:"微信号"}},[e.item.wechat?a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.copyToClipboard(e.item.wechat)}}},[e._v(e._s(e.item.wechat))]):a("span",[e._v("-")])],1),a("el-form-item",{attrs:{label:"入粉时间"}},[e._v(" "+e._s(e._f("parseTime")(e.item.create_at,"{y}-{m}-{d} {h}:{i}"))+" ")])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"人员"}},[a("el-row",[a("el-col",{attrs:{span:3}},[e._v("大人")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"adult",placeholder:"大人"},model:{value:e.item.personnel.adult,callback:function(t){e.$set(e.item.personnel,"adult",t)},expression:"item.personnel.adult"}})],1),a("el-col",{attrs:{span:3}},[e._v("老人")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"old",placeholder:"老人"},model:{value:e.item.personnel.old,callback:function(t){e.$set(e.item.personnel,"old",t)},expression:"item.personnel.old"}})],1),a("el-col",{attrs:{span:3}},[e._v("小孩")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"child",placeholder:"小孩"},model:{value:e.item.personnel.child,callback:function(t){e.$set(e.item.personnel,"child",t)},expression:"item.personnel.child"}})],1)],1)],1),a("el-form-item",{attrs:{label:"粉丝状态"}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:e.item.fans_status,callback:function(t){e.$set(e.item,"fans_status",t)},expression:"item.fans_status"}},[e._v("有效")])],1),2!=e.item.status?a("el-form-item",{attrs:{label:"加微信"}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},on:{change:e.onWechatCheckboxChange},model:{value:e.item.is_wechat,callback:function(t){e.$set(e.item,"is_wechat",t)},expression:"item.is_wechat"}},[e._v("已加微信")])],1):e._e(),1==e.item.is_wechat?a("el-form-item",{attrs:{label:"微信号"}},[a("el-input",{attrs:{name:"wechat",placeholder:"请输入客户微信号"},model:{value:e.item.wechat,callback:function(t){e.$set(e.item,"wechat",t)},expression:"item.wechat"}})],1):e._e(),a("el-form-item",{attrs:{required:"",pros:"travel_date",label:"计划出游日期"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择日期时间"},model:{value:e.item.travel_date,callback:function(t){e.$set(e.item,"travel_date",t)},expression:"item.travel_date"}})],1),2!==e.item.status?a("el-form-item",{attrs:{required:"",pros:"next_follow",label:"下次跟进时间"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间"},model:{value:e.next_follow,callback:function(t){e.next_follow=t},expression:"next_follow"}})],1):e._e()],1)],1),a("el-form-item",{attrs:{label:"跟进状态"}},[e._l(e.status_arr,(function(t,i){return[i>0?a("el-radio",{attrs:{label:i,border:""},model:{value:e.item.status,callback:function(t){e.$set(e.item,"status",t)},expression:"item.status"}},[e._v(e._s(t))]):e._e()]}))],2),a("el-form-item",{attrs:{required:"",pros:"desc",label:"跟进说明"}},[a("el-input",{attrs:{type:"textarea"},model:{value:e.item.desc,callback:function(t){e.$set(e.item,"desc",t)},expression:"item.desc"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onSave(e.item)}}},[e._v("保 存")])],1),a("el-tabs",{attrs:{type:"border-card"},model:{value:e.active,callback:function(t){e.active=t},expression:"active"}},[a("el-tab-pane",{attrs:{name:"follow",label:"跟进记录"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.item.follow}},[a("el-table-column",{attrs:{label:"日期",width:"138"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("parseTime")(t.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{label:"跟进人",width:"110",prop:"admin.username"}}),a("el-table-column",{attrs:{label:"状态",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.status_arr[t.row.status]))])]}}])}),a("el-table-column",{attrs:{prop:"desc",label:"跟进说明"}})],1)],1),a("el-tab-pane",{attrs:{name:"finance",label:"财务记录"}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.item.finance}},[a("el-table-column",{attrs:{label:"日期"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("parseTime")(t.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),a("el-table-column",{attrs:{label:"类型",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.type_arr[t.row.type]))])]}}])}),a("el-table-column",{attrs:{label:"状态",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.total/100))])]}}])})],1)],1)],1)],1),a("el-dialog",{attrs:{title:e.confirmTitle,visible:e.orderConfirmDialogVisible},on:{"update:visible":function(t){e.orderConfirmDialogVisible=t}}},[a("el-form",{attrs:{"label-width":"130px",model:e.confirmItem}},[a("el-form-item",{attrs:{label:"产品名称"}},[e._v(" "+e._s(e.confirmItem.product_name)+" ")]),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"产品状态"}},[e._v(" "+e._s(e.confirmItem.order_status_name)+" ")]),a("el-form-item",{attrs:{label:"数量"}},[e._v(" "+e._s(e.confirmItem.quantity)+" ")]),a("el-form-item",{attrs:{label:"手机"}},[a("el-button",{attrs:{type:"text"},on:{click:function(t){return e.copyToClipboard(e.confirmItem.mobile)}}},[e._v(e._s(e.confirmItem.mobile))])],1),a("el-form-item",{attrs:{label:"入粉时间"}},[e._v(" "+e._s(e._f("parseTime")(e.confirmItem.create_at,"{y}-{m}-{d} {h}:{i}"))+" ")])],1),a("el-col",{attrs:{span:12}},[e.confirmItem.dyOrderAppointments.number_of_guests?a("el-form-item",{attrs:{label:"人员"}},[a("el-row",[a("el-col",{attrs:{span:3}},[e._v("大人")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"adult",placeholder:"大人"},model:{value:e.confirmItem.dyOrderAppointments.number_of_guests.adult,callback:function(t){e.$set(e.confirmItem.dyOrderAppointments.number_of_guests,"adult",t)},expression:"confirmItem.dyOrderAppointments.number_of_guests.adult"}})],1),a("el-col",{attrs:{span:3}},[e._v("小孩")]),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{name:"child",placeholder:"小孩"},model:{value:e.confirmItem.dyOrderAppointments.number_of_guests.child,callback:function(t){e.$set(e.confirmItem.dyOrderAppointments.number_of_guests,"child",t)},expression:"confirmItem.dyOrderAppointments.number_of_guests.child"}})],1)],1)],1):e._e(),a("el-form-item",{attrs:{label:"计划出游日期"}},[e._v(" "+e._s(e.confirmItem.dyOrderAppointments.book_info.book_start_date)+" ")])],1)],1),a("el-form-item",{attrs:{label:"预约详情"}},[e.confirmItem.dyOrderAppointments.book_info.occupancies?a("el-table",{staticStyle:{width:"100%","margin-bottom":"0"},attrs:{data:e.confirmItem.dyOrderAppointments.book_info.occupancies}},[a("el-table-column",{attrs:{prop:"name",label:"出行人",width:"180"}}),a("el-table-column",{attrs:{prop:"license_id",label:"证件号",width:"180"}})],1):e._e()],1)],1),1==e.confirmItem.appointment_status?a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dyOrderConfirm(e.confirmItem,1)}}},[e._v("确认接单")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dyOrderConfirm(e.confirmItem,2)}}},[e._v("拒绝")])],1):e._e(),2==e.confirmItem.appointment_status?a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dyOrderCancel(e.confirmItem,2)}}},[e._v("仅取消预约")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.dyOrderCancel(e.confirmItem,1)}}},[e._v("取消预约并全额退款")])],1):e._e()],1),a("el-dialog",{attrs:{title:"纯核销",visible:e.dialog2Visible},on:{"update:visible":function(t){e.dialog2Visible=t}}},[a("el-form",{attrs:{"label-width":"160px",model:e.form}},[a("el-form-item",{attrs:{label:"平台"}},[a("el-radio",{attrs:{label:"1"},model:{value:e.form.os,callback:function(t){e.$set(e.form,"os",t)},expression:"form.os"}},[e._v("美团")])],1),a("el-form-item",{attrs:{label:"核销码"}},[a("el-input",{attrs:{placeholder:"请输入平台核销码"},model:{value:e.form.check_sn,callback:function(t){e.$set(e.form,"check_sn",t)},expression:"form.check_sn"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onPass(e.form)}}},[e._v("保 存")])],1)],1),a("el-dialog",{attrs:{title:"申请转出订单",visible:e.applyVisible},on:{"update:visible":function(t){e.applyVisible=t}}},[a("el-form",{ref:"ruleForm",attrs:{"label-width":"160px",model:e.item3,rules:e.rules}},[e.isAll?e._e():a("el-form-item",{attrs:{label:"标题:"}},[a("el-input",{attrs:{disabled:""},model:{value:e.item3.product_name,callback:function(t){e.$set(e.item3,"product_name",t)},expression:"item3.product_name"}})],1),e.isAll?e._e():a("el-form-item",{attrs:{label:"订单号:"}},[a("el-input",{attrs:{disabled:""},model:{value:e.item3.sn,callback:function(t){e.$set(e.item3,"sn",t)},expression:"item3.sn"}})],1),a("el-form-item",{staticStyle:{width:"600px"},attrs:{label:"流转对象:",prop:"flowObj"}},[a("el-select",{attrs:{placeholder:"请选择"},on:{change:e.onChange2},model:{value:e.item3.flowObj,callback:function(t){e.$set(e.item3,"flowObj",t)},expression:"item3.flowObj"}},[a("el-form-item",{staticStyle:{display:"inline-flex","text-align":"left",width:"770px"}},e._l(e.adminList,(function(e){return a("el-option",{key:e.value,staticStyle:{width:"250px",display:"inline-flex","word-break":"break-all"},attrs:{label:e.username,value:e.id}})})),1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.item3.backs&&0==e.item3.backs.status?a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onCancel(e.item3.flowObj)}}},[e._v("取 消")]):a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.onCirculationSave(e.item3.flowObj)}}},[e._v("确 认")])],1)],1),a("create-order-dialog",{attrs:{visible:e.createOrderVisible},on:{"data-shared":e.handleSharedData,"update:visible":function(t){e.createOrderVisible=t},refresh:e.getList}})],1)},o=[],r=a("d00a"),n=a("d09a"),s=a("0fc4"),l=a("7921"),c=(a("e224"),a("4cc3"),a("374d"),a("5227"),a("90c8"),a("67f2")),u=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"upload-container"},[a("el-upload",{staticClass:"image-uploader",attrs:{data:e.dataObj,"http-request":e.handlesSuccess,action:"",accept:".xls,.xlsx","show-file-list":!1}},[a("el-button",{attrs:{icon:"el-icon-plus",type:"success"}},[e._v(e._s(e.name))])],1),a("el-dialog",{attrs:{title:"导入失败数据",visible:e.errorDialogVisible,width:"60%"},on:{"update:visible":function(t){e.errorDialogVisible=t}}},[a("el-table",{attrs:{data:e.failData}},[a("el-table-column",{attrs:{property:"line",label:"失败行"}}),a("el-table-column",{attrs:{property:"productName",label:"线路名称"}}),a("el-table-column",{attrs:{property:"mobile",label:"手机号"}}),a("el-table-column",{attrs:{property:"wechat",label:"微信号"}}),a("el-table-column",{attrs:{property:"remark",label:"备注"}}),a("el-table-column",{attrs:{property:"error_msg",label:"错误原因"}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.errorDialogVisible=!1}}},[e._v("关 闭")])],1)],1)],1)},d=[],m=a("5f87"),p={name:"UploadExcelButton",props:{name:{type:String,default:"导入线索"}},data:function(){return{dialogVisible:!1,dialogImageUrl:"",tempUrls:[],dataObj:{token:"",key:""},errorDialogVisible:!1,failData:[]}},watch:{},methods:{handlesSuccess:function(e){var t=this;return Object(l["a"])(Object(s["a"])().mark((function a(){var i,o;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,i=new FormData,i.append("file",e.file),a.next=5,t.$axios.post("/admin/excel/import",i,{headers:{"Content-type":"multipart/form-data","X-Token":Object(m["a"])()}});case 5:o=a.sent,t.$message({showClose:!0,message:o.msg}),o.data&&o.data.data&&o.data.data.fail_data&&o.data.data.fail_data.length>0&&(t.failData=o.data.data.fail_data,t.errorDialogVisible=!0),t.$emit("refresh"),a.next=15;break;case 11:a.prev=11,a.t0=a["catch"](0),console.error("上传失败:",a.t0),e.onError(a.t0);case 15:case"end":return a.stop()}}),a,null,[[0,11]])})))()}}},f=p,b=(a("6c2b"),a("8a34")),h=Object(b["a"])(f,u,d,!1,null,"47440f3e",null),_=h.exports,g=a("f8b7"),v=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"私域线路",visible:e.dialogVisible,width:"600px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("el-form",{ref:"orderForm",attrs:{model:e.formData,"label-width":"100px",rules:e.rules}},[a("el-form-item",{attrs:{label:"私域线路",prop:"product_id"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请输入线路名称搜索"},model:{value:e.formData.product_id,callback:function(t){e.$set(e.formData,"product_id",t)},expression:"formData.product_id"}},e._l(e.lineList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.product_name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:"手机号码",prop:"mobile"}},[a("el-input",{attrs:{placeholder:"请输入客户手机号码"},model:{value:e.formData.mobile,callback:function(t){e.$set(e.formData,"mobile",t)},expression:"formData.mobile"}})],1),a("el-form-item",{attrs:{label:"微信号",prop:"wechat"}},[a("el-input",{attrs:{placeholder:"请输入客户微信号"},model:{value:e.formData.wechat,callback:function(t){e.$set(e.formData,"wechat",t)},expression:"formData.wechat"}})],1),a("el-form-item",{attrs:{label:"等级",prop:"level"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择等级"},model:{value:e.formData.level,callback:function(t){e.$set(e.formData,"level",t)},expression:"formData.level"}},e._l(e.levelList,(function(e,t){return a("el-option",{key:t,attrs:{label:e,value:t}})})),1)],1),a("el-form-item",{attrs:{label:"来源",prop:"source"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择来源"},model:{value:e.formData.source,callback:function(t){e.$set(e.formData,"source",t)},expression:"formData.source"}},e._l(e.sourceList,(function(e,t){return a("el-option",{key:t,attrs:{label:e,value:t}})})),1)],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入备注信息"},model:{value:e.formData.remark,callback:function(t){e.$set(e.formData,"remark",t)},expression:"formData.remark"}})],1),a("el-form-item",{attrs:{label:"凭证图片"}},[a("el-upload",{attrs:{action:"","list-type":"picture-card",multiple:!1,"show-file-list":!1,"http-request":e.handleImageUpload}},[e.formData.evidence_image?a("img",{staticStyle:{width:"120px",height:"120px","margin-top":"14px"},attrs:{src:e.formData.evidence_image}}):a("i",{staticClass:"el-icon-plus"})])],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submitForm}},[e._v("确 定")])],1)],1)},y=[],w=(a("e168"),{name:"CreateOrderDialog",props:{visible:{type:Boolean,default:!1}},data:function(){return{dialogVisible:!1,formData:{product_id:"",mobile:"",wechat:"",level:"",source:"",remark:"",evidence_image:""},rules:{product_id:[{required:!0,message:"请选择线路",trigger:"change"}],wechat:[],level:[{required:!0,message:"请选择等级",trigger:"change"}],source:[{required:!0,message:"请选择来源",trigger:"change"}]},lineList:[],levelList:{},sourceList:{},loading:!1,upLoading:!1,submitLoading:!1,searchTimeout:null}},mounted:function(){this.fetchLineList()},watch:{visible:function(e){this.dialogVisible=e,e&&(this.fetchLineList(),this.fetchParameters())},dialogVisible:function(e){e||(this.$emit("update:visible",!1),this.$refs.orderForm&&this.$refs.orderForm.resetFields(),this.formData.evidence_image="")}},methods:{fetchLineList:function(){var e=this;this.searchTimeout&&clearTimeout(this.searchTimeout),this.searchTimeout=setTimeout((function(){e.loading=!0,e.$axios.get("/admin/line/list",{params:{limit:500,page:1}}).then((function(t){e.$emit("data-shared",t.data.data),e.lineList=t.data.data})).catch((function(t){e.$message.error("获取线路列表失败"),console.error(t)})).finally((function(){e.loading=!1}))}),300)},fetchParameters:function(){var e=this;this.$axios.get("/admin/line/parameter").then((function(t){0===t.error&&t.data&&(e.levelList=t.data.levels||{},e.sourceList=t.data.source||{})})).catch((function(t){e.$message.error("获取参数列表失败"),console.error(t)}))},handleImageUpload:function(e){var t=this;return Object(l["a"])(Object(s["a"])().mark((function a(){var i,o;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,i=new FormData,i.append("file",e.file),t.upLoading=!0,a.next=6,t.$axios.post("/admin/upload/index",i,{headers:{"Content-type":"multipart/form-data","X-Token":Object(m["a"])()}});case 6:o=a.sent,o.data&&(t.formData.evidence_image="".concat(window.location.protocol,"//").concat(window.location.host).concat(o.data)),t.upLoading=!1,a.next=16;break;case 11:a.prev=11,a.t0=a["catch"](0),t.$message.error("图片上传失败"),console.error("error:",a.t0),t.upLoading=!1;case 16:case"end":return a.stop()}}),a,null,[[0,11]])})))()},submitForm:function(){var e=this;this.$refs.orderForm.validate((function(t){t&&(e.submitLoading=!0,e.$axios.post("/admin/line/addOrder",e.formData).then((function(t){0===t.error?(e.$message.success("订单创建成功"),e.dialogVisible=!1,e.$emit("refresh")):e.$message.error(t.msg||"订单创建失败")})).catch((function(t){e.$message.error("订单创建失败"),console.error(t)})).finally((function(){e.submitLoading=!1})))}))}}}),x=w,k=(a("af0a"),Object(b["a"])(x,v,y,!1,null,"74ba8f0e",null)),$=k.exports,S=a("5472"),O=a.n(S),C=a("ca9b"),j={name:"Orderlist",components:{Pagination:c["a"],CreateOrderDialog:$,UploadExcelButton:_,SoftPhone:C["a"]},data:function(){return{active:"follow",types:{0:"",1:"",2:"",3:"primary",4:"success",5:"warning",6:"",7:"info"},types2:{1:"primary",2:"success",3:"warning"},status_arr:{0:"待跟进",1:"跟进中",3:"已加微信(未通过)",2:"已加微信",6:"已电询",4:"放弃跟单"},type_arr:["-","收益","支出"],timetype_arr:{},order_status:["#9e9f9c","#04bcd9","#fc9904","#1193f4","#48b14b","#eb1662","#9d1cb5"],follow_status:["#9e9f9c","#04bcd9","#fc9904","#1193f4","#48b14b","#eb1662","#04bcd9","#04bcd9"],options:[],value:null,next_follow:null,list:[],total:0,listLoading:!0,listQuery:{page:1,limit:10,times:[],status:null,admin:null,zhubo:null,os_status:[],appointment_status:"",un_use_type:""},item:{next_follow:"",personnel:{}},confirmItem:{next_follow:"",personnel:{adult:""},dyOrderAppointments:{book_info:{},number_of_guests:{}}},follow:[],dialogVisible:!1,dialog2Visible:!1,applyVisible:!1,notice:!1,orderConfirmDialogVisible:!1,oss:[],lineList:[],source:[],levels:[],isSynchronization:!1,item3:{sn:null,backs:null,flowObj:"",os:null},multipleSelection:[],sn:[],adminList:[],form:{},isAll:!1,rules:{flowObj:[{required:!0,message:"请选择流转对象",trigger:"change"}]},currentSort:{prop:null,order:null},confirmTitle:"确认接单",createOrderVisible:!1,moorSoftphone:null,softphoneVisible:!1,currentPhoneNumber:"",currentLoginType:"Local"}},created:function(){var e=this;return Object(l["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.listQuery.zhubo=e.$route.query.zhubo||null,e.$route.query.start&&e.$route.query.end&&(e.listQuery.times=[e.$route.query.start,e.$route.query.end]),e.setQuery("status"),e.setQuery("os_status"),e.setQuery("times"),e.setQuery("appointment_status"),e.getShortcutContent(),e.getAdminList();case 8:case"end":return t.stop()}}),t)})))()},mounted:function(){this.setMode(),this.$route.query.id&&this.onInfo({id:this.$route.query.id})},computed:{tableMaxHeight:function(){return window.innerHeight-320+"px"}},watch:{$route:function(e,t){this.onInfo({id:this.$route.query.id})}},methods:{dowload:function(){window.open("/admin/excel/export")},beforeUpload:function(e){var t=e.size/1024/1024<1;return!!t||(this.$message({message:"Please do not upload files larger than 1m in size.",type:"warning"}),!1)},handleSuccess:function(e){var t=e.results,a=e.header;this.tableData=t,this.tableHeader=a},handleSharedData:function(e){this.lineList=e},handleSelectionChange:function(e){this.multipleSelection=e;var t=[];this.multipleSelection.map((function(e){t.push(e.sn)})),this.sn=t},setQuery:function(e){this.$route.query.hasOwnProperty(e)?this.listQuery[e]=this.$route.query[e]:this.listQuery[e]=""},stripHtml:function(e){var t=document.createElement("div");return t.innerHTML=e,t.textContent||t.innerText||""},copyToClipboard:function(e){var t=this.stripHtml(e),a=document.createElement("textarea");a.value=t,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a),this.$message({showClose:!0,message:"内容已复制",type:"success"})},callPhone:function(e){var t=this;return Object(l["a"])(Object(s["a"])().mark((function a(){var i,o;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e){a.next=3;break}return t.$message.error("电话号码不能为空"),a.abrupt("return");case 3:if(11===e.length){a.next=6;break}return t.$message.error("请输入正确的手机号码"),a.abrupt("return");case 6:if(a.prev=6,t.currentPhoneNumber=e,t.softphoneVisible=!0,!t.moorSoftphone){a.next=11;break}return a.abrupt("return");case 11:return a.next=13,t.$axios.get("/admin/qimo/getAvailableAgent");case 13:if(i=a.sent,i.data){a.next=17;break}return t.$message.error("暂无空闲坐席，请稍后再试"),a.abrupt("return");case 17:i.data,console.log("创建Softphone实例..."),t.moorSoftphone&&console.log("销毁之前的Softphone实例"),o=new O.a({accountId:"T00000032238",agentNumber:"8000@xglgj",password:"PZy12jxT8000",loginType:"Local",proxy_url:"https://sh-hw-cc-v4.7moor.com",success:function(){console.log("Softphone初始化成功！"),t.moorSoftphone=o,t.initSoftphoneEvents(o,null)},error:function(e){console.log("Softphone初始化失败:",e),t.$message.error("软电话初始化失败")}}),console.log("Softphone实例创建完成，等待初始化..."),a.next=28;break;case 24:a.prev=24,a.t0=a["catch"](6),console.error("呼叫失败:",a.t0),t.$message.error(a.t0.message||"呼叫失败");case 28:case"end":return a.stop()}}),a,null,[[6,24]])})))()},initSoftphoneEvents:function(e,t){var a=this;return Object(l["a"])(Object(s["a"])().mark((function i(){return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.prev=0,e.attachEvent({success:function(){console.log("事件绑定成功")},message:function(e){if(console.log("通话事件:",e.event),e.event)switch(e.event.type){case"peerstate":console.log("座席状态变化:",e.event.typeValue);break;case"dialing":console.log("呼叫中..."),a.$message.info("正在拨号...");break;case"dialTalking":console.log("外呼通话中"),a.$message.success("通话已接通");break;case"innerTalking":console.log("呼入通话中");break;default:console.log("其他事件:",e.event.type)}},error:function(e){console.log("事件绑定异常:",e)}}),i.next=4,new Promise((function(e){return setTimeout(e,1e3)}));case 4:if(!t){i.next=9;break}return i.next=7,a.$axios.post("/admin/qimo/makeCall",{callee_number:t,agent_id:1});case 7:console.log("发起外呼:",t),e.callApi.dialout({calleeNumber:t,success:function(e){console.log("外呼成功:",e),a.$message.success("呼叫发起成功")},fail:function(e){console.log("外呼失败:",e),a.$message.error("呼叫失败: ".concat(e.message||"未知错误"))}});case 9:i.next=15;break;case 11:i.prev=11,i.t0=i["catch"](0),console.error("初始化事件失败:",i.t0),a.$message.error("软电话事件初始化失败");case 15:case"end":return i.stop()}}),i,null,[[0,11]])})))()},onLoginTypeChange:function(e){console.log("登录类型变化为:",e),this.currentLoginType=e},onReinitSoftphone:function(e){var t=this;return Object(l["a"])(Object(s["a"])().mark((function a(){var i,o;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,console.log("重新初始化软电话，登录类型:",e),t.moorSoftphone){try{t.moorSoftphone.destroy&&"function"===typeof t.moorSoftphone.destroy&&t.moorSoftphone.destroy()}catch(r){console.log("销毁实例时出错:",r)}t.moorSoftphone=null}return a.next=5,new Promise((function(e){return setTimeout(e,500)}));case 5:return a.next=7,t.$axios.get("/admin/qimo/getAvailableAgent");case 7:if(i=a.sent,i.data){a.next=11;break}return t.$message.error("暂无空闲坐席，请稍后再试"),a.abrupt("return");case 11:console.log("创建新的Softphone实例，loginType:",e),o=new O.a({accountId:"T00000032238",agentNumber:"8000@xglgj",password:"PZy12jxT8000",loginType:e,proxy_url:"https://sh-hw-cc-v4.7moor.com",success:function(){console.log("重新初始化成功！loginType:",e),t.moorSoftphone=o,t.$message.success("已切换到".concat("Local"===e?"手机号":"sip"===e?"SIP话机":"WebRTC","模式")),t.$nextTick((function(){var e;null===(e=t.$refs.softphone)||void 0===e||e.updateStatus("空闲")})),t.initSoftphoneEvents(o,null)},error:function(e){console.log("重新初始化失败:",e),t.$message.error("切换登录方式失败: ".concat(e.message||"未知错误"))}}),console.log("Softphone实例创建调用完成，等待初始化回调..."),a.next=20;break;case 16:a.prev=16,a.t0=a["catch"](0),console.error("重新初始化异常:",a.t0),t.$message.error("切换登录方式异常: "+a.t0.message);case 20:case"end":return a.stop()}}),a,null,[[0,16]])})))()},setMode:function(){var e=this;return Object(l["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getList();case 2:case"end":return t.stop()}}),t)})))()},setOneClickRepair:function(){var e=this;return Object(l["a"])(Object(s["a"])().mark((function t(){var a;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=e.list.map((function(e){return e.id})),t.next=3,e.onOneClickRepair({id:a.join()});case 3:case"end":return t.stop()}}),t)})))()},orderSort:function(e){var t=e.column,a=e.prop,i=e.order;"ascending"==t.order&&(this.listQuery.order_by="verify_date_asc"),"descending"==t.order&&(this.listQuery.order_by="verify_date_desc"),this.currentSort.prop!==a?this.currentSort={prop:a,order:i}:this.currentSort.order="ascending"===this.currentSort.order?"descending":"ascending",console.log({column:t,prop:a,order:i}),this.getList()},getList:function(e){var t=arguments,a=this;return Object(l["a"])(Object(s["a"])().mark((function i(){var o,r,l;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(o=t.length>1&&void 0!==t[1]?t[1]:1,a.listQuery.excel=null,1!=e){i.next=8;break}return a.listQuery.excel=1,r=a.listQuery.times[0]instanceof Date,l=Object(n["a"])(Object(n["a"])({},a.listQuery),{},{times:[r?a.listQuery.times[0].toISOString():"",r?a.listQuery.times[1].toISOString():""]}),window.open("/admin/order-xs/index?"+a.objectToQuery(l)),i.abrupt("return");case 8:return i.next=10,a.$axios.get("/admin/order-xs/index",{params:a.listQuery}).then((function(e){a.list=e.data.data,a.total=e.data.total,a.timetype_arr=e.ext.timetype,a.oss=e.ext.oss,a.listLoading=!1,a.isSynchronization=!0,a.levels=e.ext.levels,a.source=e.ext.source}));case 10:if(1!=o){i.next=13;break}return i.next=13,a.setOneClickRepair();case 13:case"end":return i.stop()}}),i)})))()},objectToQuery:function(e){return Object.keys(e).map((function(t){var a=e[t];return void 0==a||null==a?"":encodeURIComponent(t)+"="+encodeURIComponent(a)})).join("&")},onInfo:function(e){var t=this;this.value=null,this.next_follow=null,this.$set(e,"next_follow",null),this.active="follow",this.$axios.get("/admin/order-xs/info",{params:{id:e.id}}).then((function(e){t.item=e.data,t.item.status=0==t.item.status?"0":String(t.item.status),t.dialogVisible=!0})).catch((function(e){}))},confirmOrder:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;1==a&&(this.confirmTitle="确认接单"),2==a&&(this.confirmTitle="协助取消/退款"),this.$axios.get("/admin/order-xs/info",{params:{id:e.id}}).then((function(e){t.confirmItem=e.data,t.orderConfirmDialogVisible=!0})).catch((function(e){}))},resetForm:function(e){this.$refs[e].resetFields()},getAdminList:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.$axios.get("/admin/admin/index",{params:{limit:100,status:1,is_private:1,type_desc:t}}).then((function(t){e.adminList=t.data.data,e.listLoading=!1})).catch((function(e){}))},onCirculation:function(e){this.getAdminList(e.category_desc),this.applyVisible=!0,this.isAll=!1,this.item3=Object(n["a"])(Object(n["a"])({},e),{},{os:Number(e.os)}),console.log(this.item3),this.item3.backs&&this.item3.backs.admin_id?this.item3.flowObj=this.item3.backs.admin_id:this.resetForm("ruleForm")},onCirculationAll:function(){this.applyVisible=!0,this.isAll=!0},onCirculationSave:function(e){var t=this;this.$refs.ruleForm.validate(function(){var a=Object(l["a"])(Object(s["a"])().mark((function a(i){var o;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!i){a.next=11;break}if(!t.isAll){a.next=5;break}t.$confirm("是否批量流转订单","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(l["a"])(Object(s["a"])().mark((function a(){return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,Object(g["d"])({sn:t.sn,to_admin_id:e});case 2:o=a.sent,o.data&&(t.$message({message:"批量流转订单成功",type:"success"}),t.applyVisible=!1,t.isAll=!1,t.getList());case 4:case"end":return a.stop()}}),a)})))),a.next=9;break;case 5:return a.next=7,Object(g["b"])({sn:t.item3.sn,os:t.item3.os,to_admin_id:e});case 7:o=a.sent,o.data&&(t.$message({message:"流转订单成功",type:"success"}),t.applyVisible=!1,t.isAll=!1,t.getList());case 9:a.next=12;break;case 11:return a.abrupt("return",!1);case 12:case"end":return a.stop()}}),a)})));return function(e){return a.apply(this,arguments)}}())},onCancel:function(){var e=this;this.$refs.ruleForm.validate((function(t){if(!t)return!1;e.$axios.post("/admin/order-xs/backcancel",{id:e.item3.id}).then((function(t){e.applyVisible=!1,e.isAll=!1,e.getList()})).catch((function(e){console.log(e)}))}))},onBack:function(){var e=this;this.$axios.post("/admin/order-xs/back",this.item).then((function(t){e.dialogVisible=!1,e.item={},e.getList()})).catch((function(e){}))},onSave:function(e){var t=this;this.$axios.post("/admin/order-xs/save",{id:e.id,check_sn:e.check_sn,is_wechat:e.is_wechat,wechat:e.wechat,travel_end:e.travel_end,travel_date:e.travel_date,desc:e.desc,status:e.status,next_follow:this.next_follow,personnel:this.item.personnel,fans_status:e.fans_status}).then((function(e){t.dialogVisible=!1,t.item={next_follow:"",personnel:{adult:""}},t.$router.push({path:"/order-xs/index"})})).catch((function(e){}))},onPass:function(e){var t=this;this.$axios.post("/admin/order-xs/pass",{check_sn:e.check_sn}).then((function(e){t.dialog2Visible=!1,t.form={}})).catch((function(e){}))},onChange:function(e){this.$set(this.item,"desc",e+(void 0!=this.item.desc?this.item.desc:""))},onChange2:function(e){this.$set(this.item,"to_admin_id",e+(void 0!=this.item.admin_id?this.item.admin_id:""))},handleChange:function(e){console.log(e)},getShortcutContent:function(){var e=this;this.listLoading=!0,this.$axios.get("/admin/shortcutContent/list",{params:{page:1,limit:50,status:1}}).then((function(t){var a,i=Object(r["a"])(t.data.data);try{for(i.s();!(a=i.n()).done;){var o=a.value;e.options.push({value:o.id,label:o.content})}}catch(n){i.e(n)}finally{i.f()}})).catch((function(){}))},onOneClickRepair:function(e){var t=this,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.notice=a,this.$axios.post("/admin/order-xs/oneClickRepair",{id:e.id}).then((function(e){t.notice&&t.$notify({title:"成功",message:"同步完成",type:"success"}),t.getList(0,0)})).catch((function(e){}))},dyOrderCancel:function(e,t){var a=this;this.$axios.post("/admin/order-xs/dyOrderCancel",{id:e.id,cancel_type:t}).then((function(e){a.$notify({title:"成功",message:"短信已发送给客人",type:"success"}),a.orderConfirmDialogVisible=!1,a.getList()})).catch((function(e){a.$notify.error({title:"发送失败",message:e})}))},dyOrderConfirm:function(e,t){var a=this;this.$axios.post("/admin/order-xs/dyOrderConfirm",{id:e.id,confirm_result:t}).then((function(e){a.$notify({title:"成功",message:"操作成功",type:"success"}),a.orderConfirmDialogVisible=!1,a.getList()})).catch((function(e){a.$notify.error({title:"接单失败",message:e})}))},onOneClickYyHandle:function(e){var t=this;this.$axios.post("/admin/order-xs/changeAppointmentStatus",{id:e.id}).then((function(e){t.dialogVisible=!1,t.$notify({title:"成功",message:"已处理",type:"success"})})).catch((function(e){t.$notify.error({title:"错误",message:e})}))},showCreateOrderDialog:function(){this.createOrderVisible=!0},onWechatCheckboxChange:function(e){1!==e||this.item.wechat||this.$message({message:"请填写客户微信号",type:"warning"})}}},L=j,T=(a("54e0"),Object(b["a"])(L,i,o,!1,null,"7d53bb72",null));t["default"]=T.exports},"9da7":function(e,t,a){},af0a:function(e,t,a){"use strict";a("9da7")},e234:function(e,t,a){}}]);