<?php

namespace app\command;

use app\common\Error;
use app\model\Orders as OrdersModel;
use app\server\Orders;
use support\Redis;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Output\OutputInterface;


class OrderNextRemindTime extends Command
{
    protected static $defaultName = 'order:next_remind';
    protected static $defaultDescription = '更新下次短信提示时间';

    /**
     * @return void
     */
    protected function configure()
    {
        // $this->addArgument('name', InputArgument::OPTIONAL, 'Name description');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->output($output, self::$defaultName . " started, " . date('Y-m-d H:i:s'));

        $orders = $this->orders();
        $total = count($orders);

        $this->output($output, "got {$total} orders");

        if (1 > $total) {
            return self::SUCCESS;
        }

        foreach($orders as $order) {
            if (empty($order->mobile)) {
                continue;
            }

            $orderTime = (int)($order->create_at / 1000);

            $orderTime = date('Y-m-d H:i:s', $orderTime);

            $next_remind_time = $this->getNextPeriodDateTime($orderTime);

            $this->output($output, "单号 {$order->sn} ： 下单时间：{$orderTime}， 下次提醒时间：{$next_remind_time}");

            $order->next_remind_time = strtotime($next_remind_time);
            $order->save();
        }

        return self::SUCCESS;
    }

    private function getNextPeriodDateTime($startDateTime, $periodDays = 10) {
        $startDate = new \DateTime($startDateTime);
        $currentDate = new \DateTime();
        $periodSeconds = $periodDays * 24 * 60 * 60;

        $secondsSinceStart = $currentDate->getTimestamp() - $startDate->getTimestamp();

        $completedPeriods = floor($secondsSinceStart / $periodSeconds);

        if ($completedPeriods < 0) {
            $completedPeriods = 0;
        }

        $nextPeriodTimestamp = $startDate->getTimestamp() + (($completedPeriods + 1) * $periodSeconds);

        $nextPeriodDate = (new \DateTime())->setTimestamp($nextPeriodTimestamp);

        return $nextPeriodDate->format('Y-m-d H:i:s');
    }

    private function output(OutputInterface $output, string $message) 
    {
        $output->writeln(sprintf('{"time":"%s", "message":"%s"}', date('Y-m-d H:i:s'), $message));
    }

    /**
     * @return OrdersModel[]
     */
    private function orders(): array
    {
        $list = OrdersModel::where('admin_id', '>', 0)->where('next_remind_time', 0)->whereRaw(OrdersModel::AllOssStatusSql[1])->select()->all();
        return $list;
    }
}
