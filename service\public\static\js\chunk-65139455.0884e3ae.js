(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-65139455"],{"09f4":function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,n,i){return t/=i/2,t<1?n/2*t*t+e:(t--,-n/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function a(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,n){var o=r(),c=t-o,l=20,u=0;e="undefined"===typeof e?500:e;var s=function(){u+=l;var t=Math.easeInOutQuad(u,o,c,e);a(t),u<e?i(s):n&&"function"===typeof n&&n()};s()}},1629:function(t,e,n){},"2cbf":function(t,e,n){"use strict";n("bac3")},"333d":function(t,e,n){"use strict";var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[n("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},a=[],r=(n("374d"),n("09f4")),o={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(t){this.$emit("update:page",t)}},pageSize:{get:function(){return this.limit},set:function(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange:function(t){this.$emit("pagination",{page:this.currentPage,limit:t}),this.autoScroll&&Object(r["a"])(0,800)},handleCurrentChange:function(t){this.$emit("pagination",{page:t,limit:this.pageSize}),this.autoScroll&&Object(r["a"])(0,800)}}},c=o,l=(n("2cbf"),n("8a34")),u=Object(l["a"])(c,i,a,!1,null,"6af373ef",null);e["a"]=u.exports},"50fc":function(t,e,n){"use strict";n.d(e,"f",(function(){return a})),n.d(e,"b",(function(){return r})),n.d(e,"h",(function(){return o})),n.d(e,"k",(function(){return c})),n.d(e,"e",(function(){return l})),n.d(e,"a",(function(){return u})),n.d(e,"j",(function(){return s})),n.d(e,"m",(function(){return d})),n.d(e,"g",(function(){return p})),n.d(e,"i",(function(){return f})),n.d(e,"l",(function(){return m})),n.d(e,"d",(function(){return g})),n.d(e,"n",(function(){return h})),n.d(e,"c",(function(){return b}));n("e168");var i=n("b775");function a(){return Object(i["a"])({url:"admin/products/list",method:"get"})}function r(t){return Object(i["a"])({url:"/admin/products/add",method:"post",data:t})}function o(t){var e,n;return Object(i["a"])({url:"/admin/products/list?page=".concat(t.page,"&limit=").concat(t.limit,"&product_name=").concat(null!==(e=t.product_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(n=t.third_product_id)&&void 0!==n?n:""),method:"get"})}function c(t){return Object(i["a"])({url:"/admin/products/add",method:"post",data:t})}function l(t){return Object(i["a"])({url:"/admin/products/productschedules?id=".concat(t.id,"&date=").concat(t.date),method:"get"})}function u(t){return Object(i["a"])({url:"/admin/products/addproductschedules",method:"post",data:t})}function s(t){var e,n;return Object(i["a"])({url:"/admin/routes/list?page=".concat(t.page,"&limit=").concat(t.limit,"&route_name=").concat(null!==(e=t.route_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(n=t.third_product_id)&&void 0!==n?n:""),method:"get"})}function d(t){return Object(i["a"])({url:"/admin/routes/add",method:"post",data:t})}function p(){return Object(i["a"])({url:"admin/products-xs/list",method:"get"})}function f(t){var e,n;return Object(i["a"])({url:"/admin/products-xs/list?page=".concat(t.page,"&limit=").concat(t.limit,"&product_name=").concat(null!==(e=t.product_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(n=t.third_product_id)&&void 0!==n?n:""),method:"get"})}function m(t){return Object(i["a"])({url:"/admin/products-xs/add",method:"post",data:t})}function g(){return Object(i["a"])({url:"/admin/setting/getContractSetting",method:"get"})}function h(t){return Object(i["a"])({url:"/admin/setting/savecontractsetting",method:"post",data:t})}function b(t){return Object(i["a"])({url:"/admin/admin/index",method:"get",params:t})}},"86f5":function(t,e,n){"use strict";n("1629")},bac3:function(t,e,n){},e145:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("div",{staticClass:"filter-container"},[n("el-input",{staticClass:"filter-item",staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"商品名称"},model:{value:t.listQuery.product_name,callback:function(e){t.$set(t.listQuery,"product_name",e)},expression:"listQuery.product_name"}}),n("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getList}},[t._v(" 搜索 ")]),n("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-circle-plus"},on:{click:function(e){return t.handleCreate("add",[])}}},[t._v(" 新增商品 ")])],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{align:"center",label:"操作",width:"200"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-edit"},on:{click:function(n){return t.handleCreate("edit",e.row)}}},[t._v(" 修改 ")]),n("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(n){return t.handleDelete(e.row)}}},[t._v(" 删除 ")])]}}])}),n("el-table-column",{attrs:{align:"center",label:"商品名称",prop:"product_name"}}),n("el-table-column",{attrs:{align:"type",label:"境内/境外",width:"100",prop:"type"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(1===e.row.type?"境内":"境外"))])]}}])}),n("el-table-column",{attrs:{align:"type",label:"天数",width:"120",prop:"day"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.day)+"天"+t._s(e.row.night)+"晚")])]}}])}),n("el-table-column",{attrs:{width:"180px",align:"center",label:"创建时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t._f("parseTime")(e.row.created_at,"{y}-{m}-{d} {h}:{i}")))])]}}])}),n("el-table-column",{attrs:{width:"180px",align:"center",label:"修改时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t._f("parseTime")(e.row.updated_at,"{y}-{m}-{d} {h}:{i}")))])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}),n("el-dialog",{attrs:{visible:t.dialogVisible,title:t.dialogTitle},on:{"update:visible":function(e){t.dialogVisible=e},open:t.onOpen,close:t.onClose}},[n("el-form",{ref:"elForm",attrs:{model:t.formData,rules:t.rules,size:"medium","label-width":"120px"}},[n("el-form-item",{attrs:{label:"产品名称",prop:"product_name"}},[n("el-input",{attrs:{placeholder:"请输入产品名称",clearable:""},model:{value:t.formData.product_name,callback:function(e){t.$set(t.formData,"product_name",e)},expression:"formData.product_name"}})],1),n("el-form-item",{attrs:{label:"天数",prop:"day"}},[n("el-input",{attrs:{placeholder:"请输入天数",clearable:""},model:{value:t.formData.day,callback:function(e){t.$set(t.formData,"day",e)},expression:"formData.day"}})],1),n("el-form-item",{attrs:{label:"晚数",prop:"night"}},[n("el-input",{attrs:{placeholder:"请输入晚数",clearable:""},model:{value:t.formData.night,callback:function(e){t.$set(t.formData,"night",e)},expression:"formData.night"}})],1),n("el-form-item",{attrs:{label:"出游类型",prop:"type"}},[n("el-radio-group",{attrs:{size:"medium"},model:{value:t.formData.type,callback:function(e){t.$set(t.formData,"type",e)},expression:"formData.type"}},t._l(t.fieldTypeOptions,(function(e,i){return n("el-radio",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.label))])})),1)],1)],1),n("div",{attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:t.close}},[t._v("取消")]),n("el-button",{attrs:{type:"primary"},on:{click:t.handelConfirm}},[t._v("确定")])],1)],1)],1)},a=[],r=n("0fc4"),o=n("7921"),c=n("d09a"),l=(n("e168"),n("3066"),n("d987"),n("4cc3"),n("7019"),n("485c"),n("452e"),n("333d")),u=n("50fc"),s=n("5f87"),d=n("e350"),p={name:"ProManagement",components:{Pagination:l["a"]},data:function(){return{listQuery:{page:1,limit:10},total:0,listLoading:!0,list:[],ossArr:[],dialogVisible:!1,dialogTitle:"新增商品",fieldFileList:[],formData:{os:"",product_name:"",type:"",day:"",night:"",third_product_id:"",trip_info:""},fieldAction:"//admin/upload/index",fieldTypeOptions:[{label:"境内",value:1},{label:"境外",value:2}],rules:{os:[{required:!0,message:"请选择平台",trigger:"change"}],product_name:[{required:!0,message:"请输入线路名称",trigger:"blur"}],day:[{required:!0,message:"请输入天数",trigger:"blur"}],night:[{required:!0,message:"请输入晚数",trigger:"blur"}],type:[{required:!0,message:"请选择出游类型",trigger:"change"}]},editType:""}},created:function(){this.getList()},methods:{handleDelete:function(t){var e=this;this.$confirm("确认删除私域线路?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((function(){e.$axios.post("/admin/products-xs/delete",{id:t.id}).then((function(){e.$message({type:"success",message:"删除成功!"}),e.getList()}))})).catch((function(){e.$message({type:"info",message:"已取消删除"})}))},checkPermission:d["a"],handleCreate:function(t,e){this.editType=t,"add"===t?this.AddProduct():this.editProduct(e)},AddProduct:function(){this.dialogTitle="新增商品",this.dialogVisible=!0},editProduct:function(t){console.log(t,"row"),this.formData=Object(c["a"])({},t),this.dialogTitle="编辑商品",this.dialogVisible=!0},getList:function(){var t=this;return Object(o["a"])(Object(r["a"])().mark((function e(){var n;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log("this.listQuery",t.listQuery),e.next=3,Object(u["i"])(t.listQuery);case 3:n=e.sent,0===n.error&&(t.listLoading=!1,t.total=n.data.total,t.list=n.data.data,t.ossArr=n.ext.oss);case 5:case"end":return e.stop()}}),e)})))()},onWork:function(t){this.$router.push("/system/proScheduling?id=".concat(t))},handleChange:function(t,e){this.fileList=e.slice(-1)},handlesAvatarSuccess:function(t){var e=this;return Object(o["a"])(Object(r["a"])().mark((function n(){var i,a,o,c;return Object(r["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,i=new FormData,i.append("file",t.file),n.next=5,e.$axios.post("/admin/upload/index",i,{headers:{"Content-type":"multipart/form-data","X-Token":Object(s["a"])()}});case 5:a=n.sent,console.log(a,"收拾收拾"),0===a.error&&t.onSuccess(a),n.next=17;break;case 10:n.prev=10,n.t0=n["catch"](0),console.log(t,"error--handlesAvatarSuccess"),o=t.file.uid,c=e.$refs.field105.uploadFiles.findIndex((function(t){return t.uid===o})),e.$refs.field105.uploadFiles.splice(c,1),e.$message.error("上传失败");case 17:case"end":return n.stop()}}),n,null,[[0,10]])})))()},handleWordSuccess:function(t,e,n,i){console.log(t,e,n,"成功了"),t&&(this.formData.trip_info="".concat(window.location.protocol,"//").concat(window.location.host).concat(t.data),this.fieldFileList=[{name:e.name,uid:e.uid,url:this.formData.trip_info}],this.$message.success("上传成功"))},beforeRemove:function(t,e){return this.$confirm("确定移除 ".concat(t.name,"？"))},handleRemove:function(t,e){var n=this;console.log(t,e,"handleRemove"),this.formData.trip_info="",this.fieldFileList.map((function(e,i){e.uid===t.uid&&n.fieldFileList.splice(i,1)}))},handleUploadError:function(t,e){var n=this;this.$message.error("上传失败: ".concat(e.name)),console.log(this.fieldFileList,"失败了"),this.fieldFileList.map((function(t,i){t.uid===e.uid&&n.fieldFileList.splice(i,1)}))},wordBeforeUpload:function(t){var e=["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"].includes(t.type);return e||this.$message.error("只允许上传 PDF、DOCX、XLSX 格式的文件"),e},openLink:function(t){window.open(t)},onOpen:function(){},onClose:function(){this.$refs["elForm"].resetFields()},close:function(){this.dialogVisible=!1},handelConfirm:function(){var t=this;this.$refs["elForm"].validate(function(){var e=Object(o["a"])(Object(r["a"])().mark((function e(n){var i;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,Object(u["l"])(t.formData);case 4:i=e.sent,0===i.error&&(t.getList(),t.$message({message:"add"===t.editType?"商品添加成功":"商品编辑成功",type:"success"})),t.close();case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())}}},f=p,m=(n("86f5"),n("8a34")),g=Object(m["a"])(f,i,a,!1,null,"4b3074da",null);e["default"]=g.exports},e350:function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));n("d987"),n("90c8"),n("6de1");var i=n("4360");function a(t){if(t&&t instanceof Array&&t.length>0){var e=i["a"].getters&&i["a"].getters.roles,n=t,a=e.some((function(t){return n.includes(t)}));return a}return console.error("need roles! Like v-permission=\"['admin','editor']\""),!1}}}]);