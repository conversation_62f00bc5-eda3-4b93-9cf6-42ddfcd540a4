<template>
  <div class="app-container">

    <div class="filter-container">
      <el-input v-model="listQuery.admin" placeholder="用户名" style="width: 200px; margin-right: 10px;" class="filter-item" />
      <el-date-picker
        class="filter-item"
        v-model="listQuery.times"
        type="datetimerange"
        :default-time="['00:00:00', '23:59:59']"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期">
      </el-date-picker>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getList">
        搜索
      </el-button>
    </div>

    <el-table v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">

      <el-table-column align="center" fixed label="姓名" width="120" prop="name" />
      
      <el-table-column align="center" label="管理员" width="120" prop="username" />

      <el-table-column align="center" width="120" label="头像">
        <template slot-scope="scope">
          <el-avatar :size="50" :src="scope.row.avatar"></el-avatar>
        </template>
      </el-table-column>

      <el-table-column align="center" label="时段">
        <template slot-scope="scope">
          <div class="day" v-for="(long,day) in scope.row.days">
            <p>{{day}}</p>
            {{ (long/3600).toFixed(1) }}
          </div>
        </template>
      </el-table-column>
      
    </el-table>

  </div>
</template>

<script>

export default {
  name: 'Datalist',
  data() {
    return {
      list: [],
      listLoading: true,
      listQuery:{}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$axios.get('/admin/data/online', { params: this.listQuery }).then(response => {
        this.list = response.data
        this.listLoading = false
      })
    }
  }
}
</script>
<style scoped>
.day {
  display: inline-block;
  margin: 10px;
  border: 1px solid #45af3c;
  border-radius: 6px;
  overflow: hidden;
}
.day p{
  background-color: #a3f39c;
  margin: 0;
  padding: 3px 10px;
}
</style>