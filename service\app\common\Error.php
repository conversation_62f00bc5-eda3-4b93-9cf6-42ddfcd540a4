<?php
namespace app\common;

class Error {
    const ERR_UNDEFINED = ['code' => -9999, 'message' => ''];
    const ERR_SMS_BLACKS = ['code' => 100001, 'message' => 'mobile is in blacks'];
    const ERR_SMS_SEND_FAIL = ['code' => 100002, 'message' => 'send error'];

    public static function undefined(string $message) 
    {
        return ['code' => self::ERR_UNDEFINED, 'message' => $message];
    }

    public static function setMessage(string $message, array $err) 
    {
        return ['code' => $err['code'] ?? 0, 'message' => $message];
    }

    public static function is(array $err): bool 
    {
        return isset($err['code']) && $err['code'] != 0;
    }

    public static function compare(array $err, array $needle): bool 
    {
        return isset($err['code']) && isset($needle['code']) && $err['code'] === $needle['code'];
    }
}