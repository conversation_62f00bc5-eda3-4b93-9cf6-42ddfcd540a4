<?php

namespace app\server;

use app\common\Keys;
use support\Redis;
use think\facade\Db;

class Blacks {
    public static function CExists(string $mobile, bool $refresh = false): bool 
    {
        $key = Keys::SUPPORT_BLACKS();

        if ($refresh || !Redis::exists($key)) {
            $list = Db::table('blacks')->column('id', 'mobile');

            if (!empty($list)) {
                $chunks = array_chunk($list, 50, true);

                foreach($chunks as $mobiles) {
                    Redis::hMSet($key, $mobiles);
                }

                Redis::expire($key, 3600);
            }
        }

        return Redis::hExists($key, $mobile);
    }
}
