<template>

  <div class="app-container">
    <div class="filter-container">
      <el-date-picker
        class="filter-item"
        v-model="listQuery.times"
        type="datetimerange"
        :default-time="['00:00:00', '23:59:59']"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期">
      </el-date-picker>
      <el-input v-model="listQuery.product_name" placeholder="产品名称" style="width: 200px;" class="filter-item" />
      <el-select v-model="listQuery.os_status" placeholder="平台状态" class="filter-item">
        <el-option
          v-for="(value,index) in oss"
          :key="index"
          :label="value"
          :value="index">
        </el-option>
      </el-select>

      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getList">
        搜索
      </el-button>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList(1)"
      >
        导出
      </el-button>

    </div>

    <el-table v-loading="listLoading" :data="list" border  highlight-current-row style="width: 100%">

      <el-table-column align="center" label="产品名称" width="260" prop="product_name" />

      <el-table-column align="center" label="平台" width="80" prop="os_name">
      </el-table-column>

      <el-table-column align="center" label="订单数" width="80" prop="all"/>

      <el-table-column align="center" label="订单金额" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.total ? parseFloat(scope.row.total)/100 : 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" width="100px" label="待跟进" prop="wait" />

      <el-table-column width="100px" align="center" label="跟进中" prop="doing" />

      <el-table-column align="center" width="100px" label="待使用数" prop="tobeused" />

      <el-table-column width="100px" align="center" label="待使用金额">
        <template slot-scope="scope">
          <span>{{ scope.row.tobeused_price ? parseFloat(scope.row.tobeused_price)/100 : 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" width="100px" label="核销数" prop="asset" />

      <el-table-column width="100px" align="center" label="核销金额">
        <template slot-scope="scope">
          <span>{{ scope.row.asset_price ? parseFloat(scope.row.asset_price)/100 : 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" width="100px" label="退款数" prop="refund" />

      <el-table-column width="100px" align="center" label="退款金额">
        <template slot-scope="scope">
          <span>{{ scope.row.refund_price ? parseFloat(scope.row.refund_price)/100 : 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="核销率" width="160">
        <template slot-scope="scope">
          {{ scope.row.asset_rate }}%
        </template>
      </el-table-column>

      <el-table-column align="center" label="退款率" width="160">
        <template slot-scope="scope">
          {{ scope.row.refund_rate }}%
        </template>
      </el-table-column>

    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

  </div>
</template>

<script>
// import Pagination from '@/Wangeditor/Pagination'
import Pagination from '@/components/PaginationFixed'

export default {
  name: 'productNameList',
  components: { Pagination },
  data() {
    return {
      active: 'follow',
      types: { 0: '', 1: '', 2: '', 3: 'primary', 4: 'success', 5: 'warning' },
      types2: { 1: 'primary', 2: 'success', 3: 'warning' },
      status_arr: ['待跟进', '跟进中', '已核销', '核销失败', '放弃跟单', "加入公海"],
      type_arr: ['-', '收益', '支出'],
      timetype_arr: {},
      order_status: ['#9e9f9c', '#04bcd9', '#fc9904', '#1193f4', '#48b14b', '#eb1662', '#9d1cb5'],
      follow_status: ['#9e9f9c', '#04bcd9', '#fc9904', '#1193f4', '#48b14b', '#eb1662'],
      options: [],
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        product_name: ''
      },
      oss: {},
      form: {}
    }
  },
  mounted() {
    this.listQuery.status = this.$route.query.status || null
    this.listQuery.zhubo = this.$route.query.zhubo || null
    if (this.$route.query.start && this.$route.query.end) {
      this.listQuery.times = [this.$route.query.start, this.$route.query.end]
    }

    this.getList()
  },
  //被缓存接收参数
  activated(){
    this.listQuery.status = this.$route.query.status || null
    this.listQuery.zhubo = this.$route.query.zhubo || null
    if (this.$route.query.start && this.$route.query.end) {
      this.listQuery.times = [this.$route.query.start, this.$route.query.end]
    }

    this.getList()
  },
  methods: {
    getList(is_excel) {
      if (is_excel == 1) {
        this.listQuery.excel = 1;
        console.log('l:' + this.listQuery.times)
        if (!this.listQuery.times) {
          this.$message({
            message: "请选择日期",
            type: "warning",
          });
          return;
        }
        
        const isdate = this.listQuery.times[0] instanceof Date;
        const params = {
          ...this.listQuery,
          times: [
            isdate ? this.listQuery.times[0].toISOString() : "",
            isdate ? this.listQuery.times[1].toISOString() : "",
          ],
        };
        window.open("/admin/index/productNameList?" + this.objectToQuery(params));
        return;
      }

      this.listQuery.excel = 0;
      this.$axios.get('/admin/index/productNameList', { params: this.listQuery }).then(response => {
        this.listLoading = false
        console.log(this.listLoading)
        this.list = response.data.data
        this.total = response.data.total
        this.timetype_arr = response.ext.timetype
        this.oss = response.ext.oss

      }).catch(() => {
      })
    },
    objectToQuery(obj) {
      return Object.keys(obj)
        .map((key) => {
          const value = obj[key];
          if (value == undefined || value == null) return "";
          return encodeURIComponent(key) + "=" + encodeURIComponent(value);
        })
        .join("&");
    }
  }
}
</script>

<style scoped>
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 52px; /* 分页条的高度，以避免内容重叠 */
}
</style>
