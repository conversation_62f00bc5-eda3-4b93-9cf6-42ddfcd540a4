<?php

declare(strict_types=1);

namespace base\helper;


use support\Response;

/**
 * API标准输出
 * @package itch\tools
 */
class Output
{
    /**
     * 标准输出
     *
     * @param int $code
     * @param mixed $data
     * @param string $msg
     *
     * @return Response
     *
     * @throws \JsonException
     */
    public static function norm(int $code, ?array $data, string $msg): Response
    {
        return response(
            arr2Json([
                'code'    => $code,
                'message' => $msg,
                'data'  => $data,
            ]),
        );
    }

    /**
     * 成功输出
     *
     * @param array|null $data
     * @param string $msg
     * @param int $code
     *
     * @return Response
     * @throws \JsonException
     */
    public static function success(?array $data, string $msg = 'success', int $code = 200): Response
    {
        return static::norm($code, $data, $msg);
    }

    /**
     * 异常输出
     *
     * @param int $code
     * @param string $msg
     * @param array|null $data
     *
     * @return Response
     */
    public static function error(int $code = 500, string $msg = 'error', ?array $data = []): Response
    {
        return static::norm($code, $data, $msg);
    }

}
