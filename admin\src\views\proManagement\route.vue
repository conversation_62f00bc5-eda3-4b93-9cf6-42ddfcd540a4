<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.route_name"
        placeholder="线路名称"
        style="width: 200px; margin-right: 10px"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="getList"
      >
        搜索
      </el-button>
      <el-button v-if="token"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-circle-plus"
        @click="handleCreate('add', [])"
      >
        新增线路
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column align="center" label="操作" width="200">
        <template slot-scope="scope">
          <el-button
            type="primary"
            @click="handleCreate('edit', scope.row)"
            size="small"
            icon="el-icon-edit"
          >
            修改
          </el-button>

          <!-- <el-button
            type="primary"
            v-if="scope.row.status"
            @click="onWork(scope.row.id)"
            size="small"
            icon="el-icon-date"
          >
            班期
          </el-button> -->
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        label="线路名称"
        prop="route_name"
      ></el-table-column>
      <!-- <el-table-column align="center" label="线路图片" width="120">
        <template slot-scope="scope">
          <el-image v-for="pic in [scope.row.route_image]"
            style="width: 100px; height: 100px"
            :src="pic"
            :preview-src-list="[scope.row.route_image]"
          >
          </el-image>
        </template>
      </el-table-column> -->
      <el-table-column align="center" label="行程图片" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.route_image">
            <div class="image-preview-wrapper" v-if="getImages(scope.row.route_image).length > 0">
              <el-image 
                :src="getImages(scope.row.route_image)[0]" 
                fit="cover" 
                class="preview-image"
                :preview-src-list="getImages(scope.row.route_image)"
              >
              </el-image>
              <span v-if="getImages(scope.row.jy_license).length > 1" class="image-count">
                +{{ getImages(scope.row.jy_license).length - 1 }}
              </span>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="行程天数"
        prop="days"
        withd="300"
      ></el-table-column>

      <el-table-column align="center" label="资质" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.qualifications">
            <div class="image-preview-wrapper" v-if="getImages(scope.row.qualifications).length > 0">
              <el-image 
                :src="getImages(scope.row.qualifications)[0]" 
                fit="cover" 
                class="preview-image"
                :preview-src-list="getImages(scope.row.qualifications)"
              >
              </el-image>
              <span v-if="getImages(scope.row.jy_license).length > 1" class="image-count">
                +{{ getImages(scope.row.jy_license).length - 1 }}
              </span>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="收款二维码" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.payment_code">
            <div class="image-preview-wrapper" v-if="getImages(scope.row.payment_code).length > 0">
              <el-image 
                :src="getImages(scope.row.payment_code)[0]" 
                fit="cover" 
                class="preview-image"
                :preview-src-list="getImages(scope.row.payment_code)"
              >
              </el-image>
              <span v-if="getImages(scope.row.pay_qr).length > 1" class="image-count">
                +{{ getImages(scope.row.pay_qr).length - 1 }}
              </span>
            </div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>


      <el-table-column
        align="center"
        label="地接价（分）"
        prop="ground_price"
        width="150"
      ></el-table-column>

      <el-table-column
        align="center"
        label="销售价（分）"
        width="150"
        prop="sale_price"
      ></el-table-column>
      <el-table-column
        align="center"
        label="利润(分)"
        width="100"
        prop="profit"
      ></el-table-column>

      <el-table-column align="type" label="线路类型" width="100" prop="type">
        <template slot-scope="scope">
          <span>{{ scope.row.type === 1 ? "境内多日游" : scope.row.type === 2 ? "境外多日游" : "一日游" }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        align="type"
        label="天数"
        width="120"
        prop="day"
      >
      <template slot-scope="scope">
        <span>{{ scope.row.day }}天{{ scope.row.night }}晚</span>
      </template>
    </el-table-column> -->
      <el-table-column
        align="type"
        label="线路详细"
        width="180"
        prop="trip_info"
      >
        <template slot-scope="scope">
          <span class="link-type" @click="openLink(scope.row.trip_info)">{{
            scope.row.trip_info
          }}</span>
        </template>
      </el-table-column>

      <el-table-column width="180px" align="center" label="创建时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.created_at | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column width="180px" align="center" label="修改时间">
        <template slot-scope="scope">
          <span>{{
            scope.row.updated_at | parseTime("{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
    <!-- 添加/编辑表单 -->
    <el-dialog
      :visible.sync="dialogVisible"
      @open="onOpen"
      @close="onClose"
      :title="dialogTitle"
    >
      <el-form
        ref="elForm"
        :model="formData"
        :rules="rules"
        size="medium"
        label-width="120px"
      >
        <!-- <el-form-item label="请选择平台" prop="os">
          <el-select v-model="formData.os" placeholder="请选择平台" clearable>
            <el-option
              v-for="(item, index) in ossArr"
              :key="item.id"
              :label="item.os"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="线路名称" prop="route_name" required>
          <el-input
            v-model="formData.route_name"
            placeholder="请输入线路名称"
            clearable
          >
          </el-input>
        </el-form-item>
        
        <!-- <el-form-item label="行程图片">
          <el-upload
            action = ""
            list-type="route_image"
            multiple:false
            :show-file-list="false"
            :http-request="handlesRouteImageSuccess"
            :on-success="
                    (response, file, fileList) =>
                      handleSuccess(response, file, fileList, 1)"
          >
            <img v-if="formData.route_image" :src="formData.route_image" style="width: 120px; height: 120px;margin-top: 14px;"/>
            <img v-else class="el-icon-plus avatar-uploader-icon"/>
          </el-upload>
        </el-form-item> -->

        <el-form-item label="行程图片">
          <multiple-image v-model="formData.route_image" />
        </el-form-item>
        <el-form-item label="资质">
          <multiple-image v-model="formData.qualifications" />
        </el-form-item>
        <el-form-item label="收款码">
          <multiple-image v-model="formData.payment_code" />
        </el-form-item>


        <el-form-item label="行程天数" prop="days" required>
          <el-input
            type = "number"
            v-model="formData.days"
            placeholder="请输入地接价"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="地接价(分)" prop="ground_price" required>
          <el-input
            type = "number"
            v-model="formData.ground_price"
            placeholder="请输入地接价"
            clearable
            @input="calcProfit()"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="售卖价(分)" prop="sale_price" required>
          <el-input
            type = "number"
            v-model="formData.sale_price"
            placeholder="请输入售卖价"
            clearable
            @input="calcProfit()"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="利润(分)" prop="profit">
          <el-input
            v-model="formData.profit"
            placeholder="请输入地接价"
            clearable
            disabled="true"
          >
          </el-input>
        </el-form-item>
        <!-- <el-form-item label="天数" prop="day">
          <el-input v-model="formData.day" placeholder="请输入天数" clearable>
          </el-input>
        </el-form-item> -->
        <!-- <el-form-item label="晚数" prop="night">
          <el-input v-model="formData.night" placeholder="请输入晚数" clearable>
          </el-input>
        </el-form-item> -->
        <el-form-item label="线路类型" prop="type" required>
          <el-radio-group v-model="formData.type" size="medium">
            <el-radio
              v-for="(item, index) in fieldTypeOptions"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="线路详细" prop="trip_info">
          <el-upload
            ref="field105"
            action=""
            :before-upload="wordBeforeUpload"
            :http-request="handlesAvatarSuccess"
            :on-success="handleWordSuccess"
            :on-error="handleUploadError"
            :on-remove="handleRemove"
            :on-change="handleChange"
            :before-remove="beforeRemove"
            :limit="1"
            :file-list="fieldFileList"
            accept=".pdf,.docx,.xlsx"
          >
            <el-button size="small" type="primary" icon="el-icon-upload"
              >点击上传</el-button
            >
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handelConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Pagination from "@/components/Pagination";
import { getRoutesListsApi, postAddRouteApi } from "@/api/admin";
import { getToken } from "@/utils/auth";
import MultipleImage from "@/components/Upload/MultipleImage.vue";
export default {
  name: "ProManagement",
  components: { Pagination, MultipleImage },
  data() {
    return {
      listQuery: {
        page: 1,
        limit: 10,
      },
      total: 0,
      listLoading: true,
      list: [],
      ossArr: [],
      dialogVisible: false,
      dialogTitle: "新增线路",
      fieldFileList: [],
      token:getToken(),
      formData: {
        os: "",
        route_name: "",
        type: 0,
        day: "",
        night: "",
        third_product_id: "",
        trip_info: "",
        ground_price:0,
        sale_price:0,
        profit:0,
        days: "",
        route_image:"",
      },
      fieldAction: process.env.VUE_APP_BASE_API + "/admin/upload/index",
      fieldTypeOptions: [
      {
          label: "一日游",
          value: 0,
        },
        {
          label: "境内多日游",
          value: 1,
        },
        {
          label: "境外多日游",
          value: 2,
        },
      ],
      rules: {
        route_name: [
          {
            required: true,
            message: "请输入线路名称",
            trigger: "blur",
          },
        ],
        days: [
          {
            required: true,
            message: "请输入行程天数",
            trigger: "blur",
          },
        ],
        ground_price: [
          {
            required: true,
            message: "请输入地接价",
            trigger: "blur",
          },
        ],
        sale_price: [
          {
            required: true,
            message: "请输入销售价",
            trigger: "blur",
          },
        ],
        type: [
          {
            required: true,
            message: "请选择线路类型",
            trigger: "change",
          },
        ],
      },
      editType: "",
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleCreate(type, item) {
      this.editType = type;
      type === "add" ? this.AddProduct() : this.editProduct(item);
    },
    AddProduct() {
      this.formData = {
        os: "",
        route_name: "",
        type: "",
        day: "",
        night: "",
        third_product_id: "",
        trip_info: "",
        ground_price:0,
        sale_price:0,
        profit:0,
        days: "",
        id:0,
        route_image:"",
      };
      this.dialogTitle = "新增线路";
      this.dialogVisible = true;
    },
    calcProfit() {
       let profit = this.formData.sale_price - this.formData.ground_price
       this.formData.profit = profit > 0 ? profit : 0;
    },
    editProduct(row) {
      this.formData = { ...row };
      this.dialogTitle = "编辑线路";
      this.dialogVisible = true;
    },
    async getList() {
      console.log("this.listQuery", this.listQuery);
      const res = await getRoutesListsApi(this.listQuery);
      if (res.error === 0) {
        this.listLoading = false;
        this.total = res.data.total;
        this.list = res.data.data;
      }
    },
    onWork(id) {
      this.$router.push(`/system/proScheduling?id=${id}`);
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-1);
    },
    async handlesAvatarSuccess(file) {
      try {
        var formdata = new FormData();
        formdata.append("file", file.file);
        const res = await this.$axios.post("/admin/upload/index", formdata, {
          headers: {
            "Content-type": "multipart/form-data",
            "X-Token": getToken(),
          },
        });
        console.log(res, "收拾收拾");
        if (res.error === 0) {
          file.onSuccess(res);
        }
      } catch (error) {
        console.log(file, "error--handlesAvatarSuccess");
        let uid = file.file.uid;
        let idx = this.$refs.field105.uploadFiles.findIndex(
          (item) => item.uid === uid
        );
        this.$refs.field105.uploadFiles.splice(idx, 1);
        this.$message.error(`上传失败`);
      }
    },
    handleWordSuccess(res, file, fileList, index) {
      console.log(res, file, fileList, "成功了");
      if (res) {
        this.formData.trip_info = `${window.location.protocol}//${window.location.host}${res.data}`;
        this.fieldFileList = [
          {
            name: file.name,
            uid: file.uid,
            url: this.formData.trip_info,
          },
        ];
        this.$message.success("上传成功");
      }
      // if (!res) return;

      // this.formData.trip_info = `${window.location.protocol}//${window.location.host}${res.data}`;
      // this.fieldFileList = [
      //   {
      //     name: file.name,
      //     uid: file.uid,
      //     url: this.formData.trip_info,
      //   },
      // ];
    },
    // 将逗号分隔的图片字符串转为数组
    getImages(imageStr) {
      if (!imageStr) return []
      return imageStr.split(',').filter(item => item.trim() !== '')
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleRemove(file, fileList) {
      console.log(file, fileList, "handleRemove");
      this.formData.trip_info = "";
      this.fieldFileList.map((item, index) => {
        if (item.uid === file.uid) {
          this.fieldFileList.splice(index, 1);
        }
      });
    },
    handleUploadError(err, file) {
      this.$message.error(`上传失败: ${file.name}`);
      console.log(this.fieldFileList, "失败了");
      this.fieldFileList.map((item, index) => {
        if (item.uid === file.uid) {
          this.fieldFileList.splice(index, 1);
        }
      });
    },
    async handlesRouteImageSuccess(file) {
      try {
        var formdata = new FormData();
        formdata.append("file", file.file);

        this.upLoading = true;
        const _this = this;
        const res = await this.$axios.post("/admin/upload/index", formdata, {
          headers: {
            "Content-type": "multipart/form-data",
            "X-Token": getToken(),
          },
        });
        this.formData.route_image = `${window.location.protocol}//${window.location.host}${res.data}`;
        file.onSuccess(res);
      } catch (error) {
        console.error('error:', error);
      }
    },
    handleSuccess(res, file, fileList) {
      console.log(res, file, fileList);
      if (!res.data) return;
      this.formData.route_image = `${window.location.protocol}//${window.location.host}${res.data}`;
    },
    wordBeforeUpload(file) {
      const isRightType = [
        "application/pdf",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ].includes(file.type);
      // const isRightSize = file.size / 1024 / 1024 < 2;

      if (!isRightType) {
        this.$message.error("只允许上传 PDF、DOCX、XLSX 格式的文件");
      }
      // if (!isRightSize) {
      //   this.$message.error("文件大小超过 2MB");
      // }

      // return isRightType && isRightSize;
      return isRightType;
    },
    openLink(link) {
      window.open(link);
    },
    onOpen() {},
    onClose() {
      this.$refs["elForm"].resetFields();
      this.$refs.field105.uploadFiles = []; // 删除该条数据
    },
    close() {
      console.log("1111", this.$refs.field105.uploadFiles);
      this.dialogVisible = false;
      this.$refs.field105.uploadFiles = []; // 删除该条数据
    },
    handelConfirm() {
      this.$refs["elForm"].validate(async (valid) => {
        if (!valid) return;
        const res = await postAddRouteApi(this.formData);
        if (res.error === 0) {
          this.getList();
          this.$message({
            message: this.editType === "add" ? "商品线路成功" : "商品线路成功",
            type: "success",
          });
        }
        this.close();
      });
    },
  },
};
</script>
<style scoped lang="scss">
.link-type {
  color: rgb(7, 181, 249);
  cursor: pointer;
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }

  /* 紧凑型表格样式 */
::v-deep .el-table {
  font-size: 15px;
  color: #333;
}

::v-deep .el-table thead {
  font-weight: 500;
  color: #2c3e50;
}

::v-deep .el-table td {
  padding: 4px 0;
}

// ::v-deep .el-table th {
//   padding: 6px 6px;
// }
::v-deep .el-table .cell {
  line-height: 1.4;
  padding-left: 6px;
  padding-right: 6px;
}

::v-deep .el-button--mini, ::v-deep .el-button--small {
  padding: 6px 9px;
  font-size: 12.5px;
}

::v-deep .el-tag {
  padding: 0 6px;
  height: 22px;
  line-height: 22px;
}

::v-deep .el-button-group .el-button--small {
  padding: 6px 9px;
}

/* 图片预览关闭按钮样式修改为白色 */
::v-deep .el-image-viewer__close {
  color: #ffffff;
}

/* 让关闭按钮更醒目 */
::v-deep .el-image-viewer__close {
  color: #ffffff;
  font-size: 64px;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(0, 0, 0, 0.8);
  opacity: 0.9;
  transition: all 0.3s;
}
</style>
