<template>
    <div style="border: 1px solid #ccc" class="relative">
      <div v-if="disabled" class="disable-layer" />
      <Toolbar ref="toolbar" style="border-bottom: 1px solid #ccc" :editor="editor" :default-config="toolbarConfig" :mode="mode" />
      <Editor style="height: 300px; overflow-y: hidden" :value="value" :default-config="editorConfig" :mode="mode" @input="handleInput" @onCreated="onCreated" />
    </div>
  </template>

  <script>
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
  import '@wangeditor/editor/dist/css/style.css'
  export default {
    name: 'WangEditor',
    components: { Editor, Toolbar },
    props: {
      value: String,
      disabled: Boolean,
      cusHeight: {
        type: String,
        default: '250px'
      }
    },
    data() {
      return {
        editor: null,
        html: '',
        toolbarConfig:{
                toolbarKeys:[
                'headerSelect',

                "blockquote",
                "header1",
                "header2",
                "header3",
                "|",
                "bold",
                "underline",
                "italic",
                "through",
                "color",
                "bgColor",
                "clearStyle",
                "|",
                "bulletedList",
                "numberedList",
                "todo",
                "justifyLeft",
                "justifyRight",
                "justifyCenter",
                "|",
                "insertLink",
                "insertTable",
                /*"codeBlock",
                "|",*/
                /*"undo",
                "redo",*/
                ]
            },
        editorConfig: {
          placeholder: '请输入内容...'
        },
        mode: 'default'
      }
    },
    mounted() {},
    created() {},
    beforeDestroy() {
      const editor = this.editor
      if (editor == null) return
      editor.destroy() // 组件销毁时，及时销毁编辑器
    },
    methods: {
      onCreated(editor) {
        this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      },
      handleInput(value) {
        // console.log(value)
        this.$emit('input', value)
      }
    }
  }
  </script>

  <style lang="scss" scoped>
  .disable-layer {
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99;
    opacity: 0.5;
  }
  </style>
