<?php

return [

    'enable' => true,
    //阿里云
    'aliyun'       => [
        'version'       => '2017-05-25',
        'host'          => 'dysmsapi.aliyuncs.com',
        'scheme'        => 'http',
        'region_id'     => 'cn-hangzhou',
        'access_key'    => 'LTAI5tAxxr5Y3MUW8vpAba6A',
        'access_secret' => '******************************',
        'sign_name'     => '海悦',
        'actions'       => [

            //验证码${code}，您正在注册成为新用户，感谢您的支持！
            'register'        => [
                'actions_name'      => '用户注册验证码',
                'template_id'  => 'SMS_147595177',
            ],

            //验证码${code}，您正在登录，若非本人操作，请勿泄露。
            'login'           => [
                'actions_name'      => '登录确认验证码',
                'template_id'  => 'SMS_147595179',
            ],

            //验证码${code}，您正在尝试修改登录密码，请妥善保管账户信息。
            'changePassword' => [
                'actions_name'      => '修改密码验证码',
                'template_id'  => 'SMS_147595176',
            ],

            //验证码${code}，您正在尝试变更重要信息，请妥善保管账户信息。
            'changeUserinfo' => [
                'actions_name'      => '信息变更验证码',
                'template_id'  => 'SMS_147595175',
            ],

            //您的验证码：${code}，在5分钟内有效，请勿泄漏。祝您生活愉快。
            'yanzhengma' => [
                'actions_name'      => '获取验证码',
                'template_id'  => 'SMS_147436405',
            ],

            //亲爱的${realname}：恭喜您，您已成功报名: ${active}活动，祝您生活愉快。
            'baomingok' => [
                'actions_name'      => '报名成功通知',
                'template_id'  => 'SMS_147416688',
            ],

            //恭喜您，您有新的客户报名。姓名：${realname}，请及时处理。
            'baomingkehu' => [
                'actions_name'      => '客户报名通知',
                'template_id'  => 'SMS_147416682',
            ],

            'notice'        => [
                'actions_name'      => '旅游管家',
                'template_id'  => 'SMS_485335726',
            ],
        ],
    ],
];
