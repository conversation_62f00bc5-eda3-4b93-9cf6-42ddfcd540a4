<template>
  <div class="app-container">

    <div class="filter-container">
      <el-input v-model="listQuery.sn" placeholder="订单号" style="width: 300px;" class="filter-item" />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getList">
        搜索
      </el-button>
    </div>

    <el-table v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">

      <el-table-column align="center" fixed width="120" label="操作">
        <template slot-scope="scope">
          <el-button type="primary" plain size="small" icon="el-icon-magic-stick" @click="onFish(scope.row)">
            领取
          </el-button>
        </template>
      </el-table-column>

      <el-table-column align="center" fixed label="平台" width="80" prop="os_name" />

      <el-table-column align="center" fixed label="直播" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.is_zhibo">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="订单号" width="220" prop="sn" />
      <!--
      <el-table-column align="center" label="微信" width="80">
        <template slot-scope="scope">
          <i v-if="scope.row.is_wechat>0" class="el-icon-circle-check"></i>
        </template>
      </el-table-column> -->

      <el-table-column align="center" label="核单" width="80">
        <template slot-scope="scope">
          <i v-if="scope.row.is_check == 1" class="el-icon-check" />
          <i v-if="scope.row.is_check == 2" class="el-icon-close" />
        </template>
      </el-table-column>

      <el-table-column align="center" width="338px" label="标题" prop="product_name" />
      <el-table-column align="center" width="138px" label="标题" prop="category_desc" />

      <el-table-column align="center" label="总金额" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.total_price/100 }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" width="80px" label="人数" prop="quantity" />

      <el-table-column align="center" label="状态" width="80" prop="order_status_name">
        <template slot-scope="scope">
          <span>{{ scope.row.order_status_name }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" width="80px" label="跟进备注" prop="remark" />

      <el-table-column width="180px" align="center" label="下单时间">
        <template slot-scope="scope">
          <span>{{ scope.row.create_at | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>

      <el-table-column width="180px" align="center" label="修改时间">
        <template slot-scope="scope">
          <span>{{ scope.row.update_time | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

  </div>
</template>

<script>
import Pagination from '@/components/PaginationFixed'

export default {
  name: 'Publist',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$axios.get('/admin/order/pub', { params: this.listQuery }).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.listLoading = false
      })
    },
    onFish(item) {
      this.$axios.post('/admin/order/fish', { id: item.id }).then(res => {
        this.dialogVisible = false
        this.item = {}
        this.getList()
      }).catch(err => {

      })
    }
    // ,
    // onTel(item) {
    //   this.item = item
    //   this.$axios.get('/admin/order/tel',{params: {id: item.id} }).then(res=>{
    //     this.item = res.data;
    //     this.dialogVisible = true;
    //   }).catch(err=>{
    //     this.item = {}
    //   })
    // },
    // onPass(item) {
    //   this.$axios.post('/admin/order/pass',{id: item.id, status: 1}).then(res=>{
    //     this.item = {};
    //     this.dialogVisible = false;
    //   }).catch(err=>{

    //   })
    // },
    // onCancel(item) {
    //   this.$axios.post('/admin/order/pass',{id: item.id, status: 0}).then(res=>{
    //     this.item = {};
    //     this.dialogVisible = false;
    //   }).catch(err => {

    //   })
    // }
  }
}
</script>

<style scoped>
.edit-input {
  padding-right: 100px;
}

.cancel-btn {
  position: absolute;
  right: 15px;
  top: 10px;
}
.app-container {
  position: relative;
  padding-bottom: 60px; /* 分页条的高度 */
}

.filter-container,
.el-table {
  padding-bottom: 52px; /* 分页条的高度，以避免内容重叠 */
}
</style>
