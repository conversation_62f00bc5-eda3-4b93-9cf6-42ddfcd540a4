(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-038a546e"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function i(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(e,t,a){var l=o(),r=e-l,s=20,u=0;t="undefined"===typeof t?500:t;var c=function(){u+=s;var e=Math.easeInOutQuad(u,l,r,t);i(e),u<t?n(c):a&&"function"===typeof a&&a()};c()}},"2cbf":function(e,t,a){"use strict";a("bac3")},"333d":function(e,t,a){"use strict";var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[a("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},i=[],o=(a("374d"),a("09f4")),l={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default:function(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e}),this.autoScroll&&Object(o["a"])(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&Object(o["a"])(0,800)}}},r=l,s=(a("2cbf"),a("8a34")),u=Object(s["a"])(r,n,i,!1,null,"6af373ef",null);t["a"]=u.exports},bac3:function(e,t,a){},da0f:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("div",{staticClass:"filter-container"},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"手机号"},model:{value:e.listQuery.mobile,callback:function(t){e.$set(e.listQuery,"mobile",t)},expression:"listQuery.mobile"}}),a("el-date-picker",{staticClass:"filter-item",attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","default-time":["00:00:00","23:59:59"],"end-placeholder":"结束日期","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.listQuery.times,callback:function(t){e.$set(e.listQuery,"times",t)},expression:"listQuery.times"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.getList}},[e._v(" 搜索 ")]),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-circle-plus"},on:{click:e.onAdd}},[e._v(" 添加主播 ")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:e.list,border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{align:"center",fixed:"",label:"操作",width:"220"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(a){return e.onAdd(t.row)}}},[e._v(" 修改 ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"用户名",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.username))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"姓名",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.name))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"手机号",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.mobile))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"岗位",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(2==t.row.type?"主播":"中控"))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"订单数"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.orders)+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"直播时长(小时)"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.work_time)+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"订单金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.total)+" ")]}}])}),a("el-table-column",{attrs:{width:"140px",align:"center",label:"创建时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("parseTime")(t.row.create_time,"{y}-{m}-{d} {h}:{i}")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){return e.$set(e.listQuery,"page",t)},"update:limit":function(t){return e.$set(e.listQuery,"limit",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.dialogCreate},on:{"update:visible":function(t){e.dialogCreate=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"用户名",prop:"username"}},[a("el-input",{attrs:{placeholder:"用户名"},model:{value:e.form.username,callback:function(t){e.$set(e.form,"username",t)},expression:"form.username"}})],1),a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{placeholder:"姓名"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"手机号",prop:"mobile"}},[a("el-input",{attrs:{placeholder:"请输入手机号"},model:{value:e.form.mobile,callback:function(t){e.$set(e.form,"mobile",t)},expression:"form.mobile"}})],1),a("el-form-item",{attrs:{label:"岗位",prop:"type"}},[a("el-radio-group",{model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[a("el-radio",{attrs:{label:2}},[e._v("主播")]),a("el-radio",{attrs:{label:3}},[e._v("中控")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{type:"primary"},on:{click:function(t){return e.onSaves(e.form)}}},[e._v("保 存")])],1)],1)],1)},i=[],o=a("d09a"),l=a("333d"),r={name:"Works",components:{Pagination:l["a"]},data:function(){return{oss:{},list:null,title:"",total:0,listLoading:!0,loading:!1,listQuery:{page:1,limit:20},form:{username:"",name:"",mobile:"",type:2},dialogWork:!1,dialogCreate:!1,item:{},anchors:[]}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.$axios.get("/admin/liveroom/zhuboStatistics",{params:this.listQuery}).then((function(t){e.list=t.data.data,e.total=t.data.total,e.listLoading=!1})).catch((function(e){}))},onDel:function(e){var t=this;this.$axios.post("/admin/work/del",{id:e.id}).then((function(e){t.dialogVisible=!1,t.getList()})).catch((function(e){}))},onAdd:function(e){var t=this;this.dialogCreate=!0,this.$nextTick((function(a){t.$refs["form"].resetFields(),t.title=e.id?"编辑":"添加",t.form=e.id?Object(o["a"])({},e):{username:"",name:"",mobile:"",type:2}}))},onSaves:function(){var e=this;this.$axios.post("/admin/admin/save",this.form).then((function(t){e.$message({message:"成功",type:"success"}),e.dialogCreate=!1,e.getList()})).catch((function(e){}))}}},s=r,u=a("8a34"),c=Object(u["a"])(s,n,i,!1,null,null,null);t["default"]=c.exports}}]);