(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-083e1d3c"],{"005a":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"pro_scheduling"},[n("div",{staticClass:"pick"},[n("span",[t._v("当前日期:"+t._s(t.yue?t.yue:t.dDate))]),n("el-date-picker",{attrs:{"value-format":"yyyy-MM",format:"yyyy 年 MM 月",type:"month",placeholder:"选择日期"},on:{change:t.changeYue},model:{value:t.yue,callback:function(e){t.yue=e},expression:"yue"}})],1),n("el-calendar",{scopedSlots:t._u([{key:"dateCell",fn:function(e){var a=e.date;return[n("div",[n("div",[t._v(t._s(a.getDate()))]),t.isDateAvailable(a)?n("div",[n("el-button",{attrs:{type:"primary",disabled:!t.isDateAvailable(a)},on:{click:function(e){return t.bookDate(a)}}},[t._v(" 可预约 ")]),n("div",{staticClass:"yy"},[t._v("已约 "+t._s(t.bookedCount(a)))]),n("div",{staticClass:"yy"},[t._v("剩余 "+t._s(t.availableCount(a)))])],1):n("div",[n("el-button",{attrs:{disabled:""}},[t._v("不可预约")]),n("div",{staticClass:"test"},[t._v("已约 "+t._s(t.bookedCount(a)))]),n("div",{staticClass:"test"},[t._v("剩余 0")])],1)])]}}]),model:{value:t.yue,callback:function(e){t.yue=e},expression:"yue"}}),n("el-dialog",{attrs:{visible:t.dialogVisible,title:"编辑"},on:{"update:visible":function(e){t.dialogVisible=e},open:t.onOpen,close:t.onClose}},[n("el-form",{ref:"elForm",attrs:{model:t.formData,rules:t.rules,size:"medium","label-width":"100px"}},[n("el-form-item",{attrs:{label:"出行日期",prop:"date"}},[n("el-input",{style:{width:"100%"},attrs:{placeholder:"请输入单行文本单行文本",disabled:!0,clearable:""},model:{value:t.formData.date,callback:function(e){t.$set(t.formData,"date",e)},expression:"formData.date"}})],1),n("el-form-item",{attrs:{label:"可预约数量",prop:"num"}},[n("el-input",{style:{width:"100%"},attrs:{type:"number",placeholder:"请输入可预约数量",clearable:""},model:{value:t.formData.num,callback:function(e){t.$set(t.formData,"num",e)},expression:"formData.num"}})],1)],1),n("div",{attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:t.close}},[t._v("取消")]),n("el-button",{attrs:{type:"primary"},on:{click:t.handelConfirm}},[t._v("确定")])],1)],1)],1)},r=[],o=n("6069"),i=n("0fc4"),u=n("7921"),c=(n("e168"),n("90c8"),n("04b0"),n("f2e9"),n("50fc")),s={data:function(){return{dialogVisible:!1,value:new Date,appointments:{},formData:{date:"",num:"",id:""},yue:"",dDate:"",rules:{num:[{required:!0,message:"请输入可预约数量",trigger:"blur"}]}}},created:function(){this.formData.id=this.$route.query.id,this.getList()},methods:Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])({getList:function(t){var e=this;return Object(u["a"])(Object(i["a"])().mark((function n(){var a,r,o,u,s,d;return Object(i["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return a=e.value,r=a.getFullYear(),o=String(a.getMonth()+1).padStart(2,"0"),u="".concat(r,"-").concat(o),e.dDate=u,s={id:e.formData.id,date:t||u},n.next=8,Object(c["e"])(s);case 8:d=n.sent,d&&0===d.error&&(e.appointments={},d.data.forEach((function(t){e.$set(e.appointments,t.date,{books_num:t.books_num,left_num:t.left_num})})));case 10:case"end":return n.stop()}}),n)})))()},changeYue:function(){this.getList(this.yue)},isDateAvailable:function(t){var e=new Date;return e.setHours(0,0,0,0),t>=e}},"isDateAvailable",(function(t){var e=new Date;e.setHours(0,0,0,0);var n=this.yue?new Date(this.yue).getFullYear():e.getFullYear(),a=this.yue?new Date(this.yue).getMonth():e.getMonth(),r=t.getFullYear(),o=t.getMonth();return r>n||r===n&&o>a||r===n&&o===a&&t>=e})),"bookDate",(function(t){console.log("预约日期: ".concat(t.toLocaleDateString())),this.dialogVisible=!0,this.formData.date=t.toLocaleDateString()})),"onOpen",(function(){})),"onClose",(function(){this.$refs["elForm"].resetFields()})),"close",(function(){this.dialogVisible=!1})),"handelConfirm",(function(){var t=this;this.$refs["elForm"].validate(function(){var e=Object(u["a"])(Object(i["a"])().mark((function e(n){var a;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,Object(c["a"])(t.formData);case 4:a=e.sent,0===a.error&&(t.$message.success("预约成功"),t.getList()),t.close();case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())})),computed:{availableCount:function(){var t=this;return function(e){var n,a=e.toISOString().split("T")[0];return(null===(n=t.appointments[a])||void 0===n?void 0:n.left_num)||0}},bookedCount:function(){var t=this;return function(e){var n,a=e.toISOString().split("T")[0];return(null===(n=t.appointments[a])||void 0===n?void 0:n.books_num)||0}}}},d=s,l=(n("2175"),n("8a34")),m=Object(l["a"])(d,a,r,!1,null,"81a84594",null);e["default"]=m.exports},2175:function(t,e,n){"use strict";n("6f32")},"50fc":function(t,e,n){"use strict";n.d(e,"f",(function(){return r})),n.d(e,"b",(function(){return o})),n.d(e,"h",(function(){return i})),n.d(e,"k",(function(){return u})),n.d(e,"e",(function(){return c})),n.d(e,"a",(function(){return s})),n.d(e,"j",(function(){return d})),n.d(e,"m",(function(){return l})),n.d(e,"g",(function(){return m})),n.d(e,"i",(function(){return f})),n.d(e,"l",(function(){return p})),n.d(e,"d",(function(){return b})),n.d(e,"n",(function(){return v})),n.d(e,"c",(function(){return h}));n("e168");var a=n("b775");function r(){return Object(a["a"])({url:"admin/products/list",method:"get"})}function o(t){return Object(a["a"])({url:"/admin/products/add",method:"post",data:t})}function i(t){var e,n;return Object(a["a"])({url:"/admin/products/list?page=".concat(t.page,"&limit=").concat(t.limit,"&product_name=").concat(null!==(e=t.product_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(n=t.third_product_id)&&void 0!==n?n:""),method:"get"})}function u(t){return Object(a["a"])({url:"/admin/products/add",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/admin/products/productschedules?id=".concat(t.id,"&date=").concat(t.date),method:"get"})}function s(t){return Object(a["a"])({url:"/admin/products/addproductschedules",method:"post",data:t})}function d(t){var e,n;return Object(a["a"])({url:"/admin/routes/list?page=".concat(t.page,"&limit=").concat(t.limit,"&route_name=").concat(null!==(e=t.route_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(n=t.third_product_id)&&void 0!==n?n:""),method:"get"})}function l(t){return Object(a["a"])({url:"/admin/routes/add",method:"post",data:t})}function m(){return Object(a["a"])({url:"admin/products-xs/list",method:"get"})}function f(t){var e,n;return Object(a["a"])({url:"/admin/products-xs/list?page=".concat(t.page,"&limit=").concat(t.limit,"&product_name=").concat(null!==(e=t.product_name)&&void 0!==e?e:"","&third_product_id=").concat(null!==(n=t.third_product_id)&&void 0!==n?n:""),method:"get"})}function p(t){return Object(a["a"])({url:"/admin/products-xs/add",method:"post",data:t})}function b(){return Object(a["a"])({url:"/admin/setting/getContractSetting",method:"get"})}function v(t){return Object(a["a"])({url:"/admin/setting/savecontractsetting",method:"post",data:t})}function h(t){return Object(a["a"])({url:"/admin/admin/index",method:"get",params:t})}},"6f32":function(t,e,n){}}]);