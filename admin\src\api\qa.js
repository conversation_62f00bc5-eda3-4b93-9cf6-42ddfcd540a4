import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/admin/login',
    method: 'post',
    data
  })
}

// 获取子集列表
export function getQaList(params) {
  return request({
    url: '/admin/qa/getQaList',
    method: 'get',
    params: params
  })
}

// 获取qa详情
export function getQaDetail(city_id) {
  return request({
    url: 'admin/qa/getQaDetail',
    method: 'get',
    params: {
      city_id
    }
  })
}

//获取城市列表
export function getQaCityList(city_id) {
  return request({
    url: '/admin/qa/getQaCityList',
    method: 'get'
  })
}
