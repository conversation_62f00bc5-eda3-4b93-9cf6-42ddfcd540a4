<template>
  <div class="app-container">

    <div class="filter-container">
      <el-input v-model="listQuery.admin" placeholder="管理员" style=" margin-right:10px; width: 200px;" class="filter-item" />
      <el-input v-model="listQuery.sn" placeholder="订单号" style=" margin-right:10px; width: 200px;" class="filter-item" />
      <el-date-picker
        class="filter-item"
        v-model="listQuery.times"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :default-time="['00:00:00', '23:59:59']"
        style=" margin-right:10px; margin-bottom: 10px;"
      >
      </el-date-picker>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="getList">
        搜索
      </el-button>
    </div>

    <el-table v-loading="listLoading" :data="list" border fit highlight-current-row style="width: 100%">

      <el-table-column align="center" label="跟进人" width="80" prop="admin.username" />

      <el-table-column align="center" label="跟进事件" width="80" prop="status_name" />
      <el-table-column align="center" label="备注" prop="desc" />
      <el-table-column align="center" label="订单号" width="220" prop="orders.sn" />
      <el-table-column align="center" label="产品" width="360" prop="orders.product_name" />

      <el-table-column align="center" label="总金额" width="120">
        <template slot-scope="scope">
          {{ scope.row.orders&&scope.row.orders.total_price/100 }}
        </template>
      </el-table-column>

      <el-table-column align="center" width="80px" label="人数" prop="orders.quantity" />

      <el-table-column width="180px" align="center" label="下单时间">
        <template slot-scope="scope">
          {{ scope.row.orders&&scope.row.orders.create_at | parseTime('{y}-{m}-{d} {h}:{i}') }}
        </template>
      </el-table-column>

      <el-table-column width="180px" align="center" label="跟进时间">
        <template slot-scope="scope">
          {{ scope.row.create_time | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'

export default {
  name: 'Orderlist',
  components: { Pagination },
  filters: {
    statusFilter(status) {
      const statusMap = {
        1: 'success',
        0: 'danger'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$axios.get('/admin/follow/index', { params: this.listQuery }).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.listLoading = false
      })
    }
  }
}
</script>