#!/usr/bin/env php
<?php

use Webman\Config;
use Webman\Console\Command;
use Webman\Console\Util;
use support\Container;

require_once __DIR__ . '/vendor/autoload.php';

if (!in_array($argv[1] ?? '', ['start', 'restart', 'stop', 'status', 'reload', 'connections'])) {
    require_once __DIR__ . '/support/bootstrap.php';
} else {
    if (class_exists('Support\App')) {
        Support\App::loadAllConfig(['route']);
    } else {
        Config::reload(config_path(), ['route', 'container']);
    }
}

$cli = new Command();
$cli->setName('webman cli');
$cli->installInternalCommands();
if (is_dir($command_path = Util::guessPath(app_path(), '/command', true))) {
    $cli->installCommands($command_path);
}

foreach (config('plugin', []) as $firm => $projects) {
    if (isset($projects['app'])) {
        foreach (['', '/app'] as $app) {
            if ($command_str = Util::guessPath(base_path() . "/plugin/$firm{$app}", 'command')) {
                $command_path = base_path() . "/plugin/$firm{$app}/$command_str";
                $cli->installCommands($command_path, "plugin\\$firm" . str_replace('/', '\\', $app) . "\\$command_str");
            }
        }
    }
    foreach ($projects as $name => $project) {
        if (!is_array($project)) {
            continue;
        }
        foreach ($project['command'] ?? [] as $class_name) {
            $reflection = new \ReflectionClass($class_name);
            if ($reflection->isAbstract()) {
                continue;
            }
            $properties = $reflection->getStaticProperties();
            $name = $properties['defaultName'];
            if (!$name) {
                throw new RuntimeException("Command {$class_name} has no defaultName");
            }
            $description = $properties['defaultDescription'] ?? '';
            $command = Container::get($class_name);
            $command->setName($name)->setDescription($description);
            $cli->add($command);
        }
    }
}

$cli->run();
