import request from '@/utils/request'

// 获取管理员操作日志列表
export function fetchAdminOperationLogList(params) {
  return request({
    url: '/admin/admin-operation-log/index',
    method: 'get',
    params
  })
}

// 获取管理员操作日志详情
export function fetchAdminOperationLogDetail(params) {
  return request({
    url: '/admin/admin-operation-log/show',
    method: 'get',
    params
  })
}

// 获取管理员操作日志统计
export function fetchAdminOperationLogStatistics(params) {
  return request({
    url: '/admin/admin-operation-log/statistics',
    method: 'get',
    params
  })
}

// 导出管理员操作日志
export function exportAdminOperationLog(params) {
  return request({
    url: '/admin/admin-operation-log/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
} 