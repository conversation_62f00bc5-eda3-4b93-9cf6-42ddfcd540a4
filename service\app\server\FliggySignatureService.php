<?php
declare(strict_types=1);

namespace app\server;

use support\Log;

/**
 * 飞猪签名验证服务
 */
final class FliggySignatureService
{
    /**
     * 生成签名
     *
     * @param string $appKey 应用的公钥
     * @param string $appSecret 应用的私钥
     * @param string $httpMethod HTTP方法（GET, POST等）
     * @param string $url 请求的URL
     * @param string $contentString 请求内容的JSON字符串
     * @param int $timestamp 时间戳
     * @param string $nonce 随机字符串，保证请求的唯一性
     * @param string $optionsKeysString 其他选项键值对的字符串
     * @return string 生成的签名字符串
     */
    public static function generateSignature(
        string $appKey,
        string $appSecret,
        string $httpMethod,
        string $url,
        string $contentString,
        int $timestamp,
        string $nonce,
        string $optionsKeysString = ''
    ): string {
        // 依据content字符串生成对应的SHA256哈希值，并转为小写形式
        $contentHash = strtolower(hash('sha256', $contentString));

        // 生成待签名字符串，由HTTP方法、URL、内容哈希和其他选项组成，以换行符分隔
        $stringToSign = implode("\n", [$httpMethod, $url, $contentHash, $optionsKeysString]);

        // 生成待加密的明文，由公钥、时间戳、随机字符串和待签名字符串组成
        $plainText = $appKey . $timestamp . $nonce . $stringToSign;

        // 使用HMAC-SHA256算法生成签名，并将其转换为大写
        $sign = strtoupper(hash_hmac('sha256', $plainText, $appSecret));

        return $sign;
    }

    /**
     * 验证签名
     *
     * @param array $headers 请求头信息
     * @param string $body 请求体内容
     * @param string $method HTTP方法
     * @param string $uri 请求URI
     * @return bool 验证结果
     */
    public static function verifySignature(array $headers, string $body, string $method, string $uri): bool
    {
        try {
            $bodyArr = json_decode($body, true);
            // 获取签名相关的请求头
            $appKey = $bodyArr['vendor']['extend']['apiKey'] ?? '';
            $timestamp = $headers['timestamp'] ?? '';
            $nonce = $headers['nonce'] ?? '';
            $signature = $headers['sign'] ?? '';
            $optionsKeys = $headers['x-options-keys'] ?? '';

            if (empty($appKey) || empty($timestamp) || empty($nonce) || empty($signature)) {
                Log::warning('Fliggy signature verification failed: Missing required headers');
                return false;
            }

            // 检查时间戳是否在有效期内（5分钟）
            $currentTime = time() * 1000; // 转换为毫秒
            $requestTime = intval($timestamp);
            if (abs($currentTime - $requestTime) > 300000) { // 5分钟
                Log::warning('Fliggy signature verification failed: Timestamp expired', [
                    'current_time' => $currentTime,
                    'request_time' => $requestTime
                ]);
                return false;
            }

            // 获取应用密钥（实际项目中应该从配置或数据库中获取）
            $appSecret = self::getAppSecret($appKey);
            if (empty($appSecret)) {
                Log::warning('Fliggy signature verification failed: Invalid app key', ['app_key' => $appKey]);
                return false;
            }

            // 生成期望的签名
            $expectedSignature = self::generateSignature(
                $appKey,
                $appSecret,
                strtoupper($method),
                $uri,
                $body,
                intval($timestamp),
                $nonce,
                $optionsKeys
            );

            // 比较签名
            $isValid = hash_equals($expectedSignature, $signature);

            if (!$isValid) {
                Log::warning('Fliggy signature verification failed: Signature mismatch', [
                    'expected' => $expectedSignature,
                    'actual' => $signature,
                    'app_key' => $appKey,
                    'timestamp' => $timestamp,
                    'nonce' => $nonce,
                    'body' => $body
                ]);
            }

            return $isValid;
        } catch (\Exception $e) {
            Log::error('Fliggy signature verification error: ' . $e->getMessage(), [
                'headers' => $headers,
                'body' => $body
            ]);
            return false;
        }
    }

    /**
     * 获取应用密钥
     *
     * @param string $appKey 应用公钥
     * @return string 应用密钥
     */
    private static function getAppSecret(string $appKey): string
    {
        // 从数据库中获取密钥
        return \app\model\FliggyConfigs::getAppSecret($appKey);
    }

    /**
     * 生成标准响应格式
     *
     * @param bool $success 是否成功
     * @param string $code 状态码
     * @param string $message 消息
     * @param array|null $data 数据
     * @return array 响应数组
     */
    public static function buildResponse(bool $success, string $code, string $message, ?array $data = null): array
    {
        $response = [
            'success' => $success,
            'code' => $code,
            'message' => $message
        ];

        if ($success && $data !== null) {
            $response['data'] = $data;
        }

        return $response;
    }
}
