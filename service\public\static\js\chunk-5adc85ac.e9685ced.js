(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5adc85ac"],{"08a4":function(t,e,i){"use strict";i("79c7")},"5c7c":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"app-container"},[i("div",{staticClass:"filter-container"},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"用户名"},model:{value:t.listQuery.username,callback:function(e){t.$set(t.listQuery,"username",e)},expression:"listQuery.username"}}),i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"手机号"},model:{value:t.listQuery.mobile,callback:function(e){t.$set(t.listQuery,"mobile",e)},expression:"listQuery.mobile"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.getOnlineList}},[t._v(" 搜索 ")])],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{width:"100%"},attrs:{data:t.list,border:"",fit:"","highlight-current-row":""}},[i("el-table-column",{attrs:{align:"center",label:"ID",width:"60",prop:"id"}}),i("el-table-column",{attrs:{align:"center",label:"姓名",width:"80",prop:"username"}}),i("el-table-column",{attrs:{align:"center",label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[0===e.row.isOnline?i("el-tag",{attrs:{type:"border-card"}},[t._v("下线")]):t._e(),1===e.row.isOnline?i("el-tag",{attrs:{type:"success"}},[t._v("在线")]):t._e(),2===e.row.isOnline?i("el-tag",{attrs:{type:"info"}},[t._v("没上线")]):t._e()]}}])}),i("el-table-column",{attrs:{align:"center",label:"是否分单",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(i){return t.updateStatus(e.row)}},model:{value:e.row.isEndWork,callback:function(i){t.$set(e.row,"isEndWork",i)},expression:"scope.row.isEndWork"}})]}}])}),i("el-table-column",{attrs:{align:"center",label:"是否私域分单",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(i){return t.updatePrivateStatus(e.row)}},model:{value:e.row.is_private,callback:function(i){t.$set(e.row,"is_private",i)},expression:"scope.row.is_private"}})]}}])}),i("el-table-column",{attrs:{align:"center",label:"在线时长",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(Math.floor((e.row.data?e.row.data.onlineTime:e.row.onlineTime)/60)+"分钟"||!1)+" ")]}}])}),i("el-table-column",{attrs:{width:"138px",align:"center",label:"上线时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("parseTime")(e.row.start_work_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),i("el-table-column",{attrs:{width:"138px",align:"center",label:"停止分单时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("parseTime")(e.row.end_work_time,"{y}-{m}-{d} {h}:{i}")))])]}}])}),i("el-table-column",{attrs:{width:"138px",align:"center",label:"下线时间"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("parseTime")(e.row.last_work_time,"{y}-{m}-{d} {h}:{i}")))])]}}])})],1),i("div",{staticClass:"pagination-container"},[i("el-pagination",{attrs:{"current-page":t.listQuery.page,"page-sizes":[10,20,30,50],"page-size":t.listQuery.limit,total:t.total,background:"",layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)},a=[],l={name:"GetOnlineList",components:{},data:function(){return{statusArr:{0:"禁用",1:"启用"},list:[],total:0,loading:!1,listLoading:!0,listQuery:{page:1,limit:10,status:null,content:"",mobile:""},dialogCreate:!1,dialogEdit:!1,item:{},anchors:{}}},created:function(){this.listQuery.status=this.$route.query.status||null,this.listQuery.content=this.$route.query.content||null,this.listQuery.mobile=this.$route.query.mobile||null,this.getOnlineList()},methods:{getOnlineList:function(){var t=this;this.listLoading=!0,this.$axios.get("/admin/admin/getOnlineList",{params:this.listQuery}).then((function(e){t.list=e.data.data,t.total=e.data.total,t.listLoading=!1})).catch((function(){t.listLoading=!1}))},handleSizeChange:function(t){this.listQuery.limit=t,this.getOnlineList()},handleCurrentChange:function(t){this.listQuery.page=t,this.getOnlineList()},updateStatus:function(t){var e=this;this.$axios.post("/admin/admin/editInfo",{id:t.id,order_num:t.order_num,is_order:t.isEndWork}).then((function(){e.getOnlineList()})).catch((function(){}))},updatePrivateStatus:function(t){var e=this;this.$axios.post("/admin/admin/editInfo",{id:t.id,is_private:t.is_private}).then((function(){e.getOnlineList()})).catch((function(){}))}}},s=l,r=(i("08a4"),i("8a34")),o=Object(r["a"])(s,n,a,!1,null,"60d42231",null);e["default"]=o.exports},"79c7":function(t,e,i){}}]);