<?php
namespace app\admin\controller;

use app\model\AdminOperationLogs;
use support\Log;
use support\Request;

class AdminOperationLogController extends base
{
    /**
     * 获取管理员操作日志列表
     * @param Request $request
     * @return \support\Response
     */
    public function index(Request $request)
    {
        $adminId = $request->get('admin_id', 0);
        $operatorId = $request->get('operator_id', 0);
        $operationType = $request->get('operation_type', '');
        $startTime = $request->get('start_time', '');
        $endTime = $request->get('end_time', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 20);

        $query = AdminOperationLogs::with(['admin', 'operator']);

        // 应用搜索条件
        if ($adminId > 0) {
            $query->where('admin_id', $adminId);
        }

        if ($operatorId > 0) {
            $query->where('operator_id', $operatorId);
        }

        if (!empty($operationType)) {
            $query->where('operation_type', $operationType);
        }

        if (!empty($startTime) && !empty($endTime)) {
            $startTimestamp = strtotime($startTime);
            $endTimestamp = strtotime($endTime . ' 23:59:59');
            $query->where('operation_time', '>=', $startTimestamp)
                  ->where('operation_time', '<=', $endTimestamp);
        }

        // 权限控制：普通管理员只能查看自己门店的操作日志
        if (!$request->admin->is_super) {
            $query->whereHas('admin', function($q) use ($request) {
                $q->where('shop_id', $request->admin->shop_id);
            });
        }

        $logs = $query->order('operation_time', 'desc')
                    ->append(['operation_type_name'])
                     ->paginate($limit);

        return $this->success($logs);
    }

    /**
     * 查看单个操作日志详情
     * @param Request $request
     * @return \support\Response
     */
    public function show(Request $request)
    {
        $id = $request->get('id', 0);

        $log = AdminOperationLogs::with(['admin', 'operator'])->find($id);

        if (!$log) {
            return $this->error(404, '操作日志不存在');
        }

        // 权限控制
        if (!$request->admin->is_super) {
            $adminShopId = $log->admin->shop_id ?? 0;
            if ($adminShopId != $request->admin->shop_id) {
                return $this->error(403, '没有权限查看此日志');
            }
        }

        return $this->success($log);
    }

    /**
     * 统计操作日志数据
     * @param Request $request
     * @return \support\Response
     */
    public function statistics(Request $request)
    {
        $startTime = $request->get('start_time', date('Y-m-d'));
        $endTime = $request->get('end_time', date('Y-m-d'));

        $startTimestamp = strtotime($startTime);
        $endTimestamp = strtotime($endTime . ' 23:59:59');

        $query = AdminOperationLogs::where('operation_time', '>=', $startTimestamp)
                                  ->where('operation_time', '<=', $endTimestamp);

        // 权限控制
        if (!$request->admin->is_super) {
            $query->whereHas('admin', function($q) use ($request) {
                $q->where('shop_id', $request->admin->shop_id);
            });
        }

        // 总操作数量
        $totalCount = $query->count();

        $operatorStatsQuery = clone $query;
        $typeStatsQuery = clone $query;
        $hourlyStatsQuery = clone $query;

        // 按操作者统计
        $operatorStats = $operatorStatsQuery->with('operator')
                           ->fieldRaw('operator_id, COUNT(*) as count')
                           ->group('operator_id')
                           ->order('count', 'desc')
                           ->select();

        // 按操作类型统计
        $typeStats = $typeStatsQuery->fieldRaw('operation_type, COUNT(*) as count')
                          ->group('operation_type')
                          ->order('count', 'desc')
                          ->select();

        // 按小时统计（当日）
        if ($startTime === $endTime) {
            $hourlyStats = $hourlyStatsQuery->fieldRaw('HOUR(FROM_UNIXTIME(operation_time)) as hour, COUNT(*) as count')
                                ->group('hour')
                                ->order('hour')
                                ->select();
        } else {
            $hourlyStats = [];
        }

        return $this->success([
            'total_count' => $totalCount,
            'operator_stats' => $operatorStats,
            'type_stats' => $typeStats,
            'hourly_stats' => $hourlyStats
        ]);
    }

    /**
     * 导出操作日志
     * @param Request $request
     * @return \support\Response
     */
    public function export(Request $request)
    {
        $adminId = $request->get('admin_id', 0);
        $operatorId = $request->get('operator_id', 0);
        $operationType = $request->get('operation_type', '');
        $startTime = $request->get('start_time', '');
        $endTime = $request->get('end_time', '');

        $query = AdminOperationLogs::with(['admin', 'operator']);

        // 应用搜索条件
        if ($adminId > 0) {
            $query->where('admin_id', $adminId);
        }

        if ($operatorId > 0) {
            $query->where('operator_id', $operatorId);
        }

        if (!empty($operationType)) {
            $query->where('operation_type', $operationType);
        }

        if (!empty($startTime) && !empty($endTime)) {
            $startTimestamp = strtotime($startTime);
            $endTimestamp = strtotime($endTime . ' 23:59:59');
            $query->where('operation_time', '>=', $startTimestamp)
                  ->where('operation_time', '<=', $endTimestamp);
        }

        // 权限控制
        if (!$request->admin->is_super) {
            $query->whereHas('admin', function($q) use ($request) {
                $q->where('shop_id', $request->admin->shop_id);
            });
        }

        $logs = $query->order('operation_time', 'desc')->limit(10000)->select();

        $data = [];
        $data[] = ['操作时间', '被操作管理员', '操作者', '操作类型', '变更字段', '备注', 'IP地址'];

        foreach ($logs as $log) {
            $fieldChanges = '';
            if (!empty($log->field_changes)) {
                $changes = [];
                foreach ($log->field_changes as $field => $change) {
                    $changes[] = $change['label'] . ': ' . $change['old_value'] . ' → ' . $change['new_value'];
                }
                $fieldChanges = implode('; ', $changes);
            }

            $data[] = [
                date('Y-m-d H:i:s', $log->operation_time),
                ($log->admin->name ?? '') . '(' . ($log->admin->username ?? '') . ')',
                ($log->operator->name ?? '') . '(' . ($log->operator->username ?? '') . ')',
                $log->operation_type_name,
                $fieldChanges,
                $log->remark,
                $log->ip_address
            ];
        }

        $filename = '管理员操作日志_' . date('YmdHis') . '.csv';

        // 设置CSV头部
        $response = response('', 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);

        // 添加BOM以支持中文
        $csv = "\xEF\xBB\xBF";

        foreach ($data as $row) {
            $csv .= implode(',', array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row)) . "\n";
        }

        return $response->withBody($csv);
    }
}
