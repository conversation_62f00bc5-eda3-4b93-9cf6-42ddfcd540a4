<?php
namespace app\admin\controller;

use app\model\OrderAllocateLogs;
use support\Log;
use support\Request;

class OrderAllocateLogController extends base
{
    /**
     * 订单分配日志列表
     * @param Request $request
     * @return \support\Response
     */
    public function index(Request $request)
    {
        $orderSn = $request->get('order_sn', '');
        $adminId = $request->get('admin_id', 0);
        $shopId = $request->get('shop_id', 0);
        $startTime = $request->get('start_time', '');
        $endTime = $request->get('end_time', '');
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 20);

        $query = OrderAllocateLogs::with(['admin', 'shop']);

        // 搜索条件
        if (!empty($orderSn)) {
            $query->where('order_sn', 'like', "%{$orderSn}%");
        }

        if ($adminId > 0) {
            $query->where('admin_id', $adminId);
        }

        if ($shopId > 0) {
            $query->where('shop_id', $shopId);
        }

        if (!empty($startTime) && !empty($endTime)) {
            $startTimestamp = strtotime($startTime);
            $endTimestamp = strtotime($endTime . ' 23:59:59');
            $query->where('allocate_time', '>=', $startTimestamp)
                  ->where('allocate_time', '<=', $endTimestamp);
        }

        // 权限控制：非超级管理员只能查看自己门店的日志
        $query->where('shop_id', $request->admin->shop_id);

        $logs = $query->order('allocate_time', 'desc')
                     ->order('id', 'desc')
                     ->paginate($limit);

        return $this->success($logs);
    }

    /**
     * 获取统计数据
     * @param Request $request
     * @return \support\Response
     */
    public function statistics(Request $request)
    {
        $startTime = $request->get('start_time', date('Y-m-d'));
        $endTime = $request->get('end_time', date('Y-m-d'));

        $startTimestamp = strtotime($startTime);
        $endTimestamp = strtotime($endTime . ' 23:59:59');

        $query = OrderAllocateLogs::where('allocate_time', '>=', $startTimestamp)
                                 ->where('allocate_time', '<=', $endTimestamp);

        // 权限控制
        $query->where('shop_id', $request->admin->shop_id);
        if (!$request->admin->is_super) {
            $query->where('admin_id', $request->admin->id);
        }

        // 总分配数量
        $totalCount = $query->count();

        $adminStatsQuery = clone $query;
        $shopStatsQuery = clone $query;
        $methodStatsQuery = clone $query;
        $hourlyStatsQuery = clone $query;
        // 按客服统计
        $adminStats = $adminStatsQuery->with('admin')
                           ->fieldRaw('admin_id, COUNT(*) as count')
                           ->group('admin_id')
                           ->order('count', 'desc')
                           ->select();

        // 按门店统计
        $shopStats = $shopStatsQuery->with('shop')
                          ->fieldRaw('shop_id, COUNT(*) as count')
                          ->group('shop_id')
                          ->order('count', 'desc')
                          ->select();

        // 按分配方式统计
        $methodStats = $methodStatsQuery->fieldRaw('JSON_EXTRACT(extra_data, "$.allocate_method") as method, COUNT(*) as count')
                            ->group('method')
                            ->order('count', 'desc')
                            ->select();

        // 按小时统计（当日）
        if ($startTime === $endTime) {
            $hourlyStats = $hourlyStatsQuery->fieldRaw('HOUR(FROM_UNIXTIME(allocate_time)) as hour, COUNT(*) as count')
                                ->group('hour')
                                ->order('hour')
                                ->select();
        } else {
            $hourlyStats = [];
        }

        return $this->success([
            'total_count' => $totalCount,
            'admin_stats' => $adminStats,
            'shop_stats' => $shopStats,
            'method_stats' => $methodStats,
            'hourly_stats' => $hourlyStats
        ]);
    }

    /**
     * 查看单个分配日志详情
     * @param Request $request
     * @return \support\Response
     */
    public function show(Request $request)
    {
        $id = $request->get('id', 0);

        $log = OrderAllocateLogs::with(['admin', 'shop'])->find($id);

        if (!$log) {
            return $this->error(404, '分配日志不存在');
        }

        // 权限控制
        if (!$request->admin->is_super && $log->shop_id != $request->admin->shop_id) {
            return $this->error(403, '没有权限查看此日志');
        }

        return $this->success($log);
    }

    /**
     * 导出分配日志
     * @param Request $request
     * @return \support\Response
     */
    public function export(Request $request)
    {
        $orderSn = $request->get('order_sn', '');
        $adminId = $request->get('admin_id', 0);
        $shopId = $request->get('shop_id', 0);
        $startTime = $request->get('start_time', '');
        $endTime = $request->get('end_time', '');

        $query = OrderAllocateLogs::with(['admin', 'shop']);

        // 应用搜索条件
        if (!empty($orderSn)) {
            $query->where('order_sn', 'like', "%{$orderSn}%");
        }

        if ($adminId > 0) {
            $query->where('admin_id', $adminId);
        }

        if ($shopId > 0) {
            $query->where('shop_id', $shopId);
        }

        if (!empty($startTime) && !empty($endTime)) {
            $startTimestamp = strtotime($startTime);
            $endTimestamp = strtotime($endTime . ' 23:59:59');
            $query->where('allocate_time', '>=', $startTimestamp)
                  ->where('allocate_time', '<=', $endTimestamp);
        }

        // 权限控制
        if (!$request->admin->is_super) {
            $query->where('shop_id', $request->admin->shop_id);
        }

        $logs = $query->order('allocate_time', 'desc')->limit(10000)->select();

        $data = [];
        $data[] = ['订单号', '客服姓名', '客服用户名', '门店名称', '分配时间', '线路权限', '分配方式', '手机号', '商品ID'];

        foreach ($logs as $log) {
            $extraData = $log->extra_data;
            $data[] = [
                $log->order_sn,
                $log->admin->name ?? '',
                $log->admin->username ?? '',
                $log->shop->name ?? '',
                date('Y-m-d H:i:s', $log->allocate_time),
                $log->route_permission,
                $extraData['allocate_method'] ?? '',
                $extraData['mobile'] ?? '',
                $extraData['product_id'] ?? ''
            ];
        }

        $filename = '订单分配日志_' . date('YmdHis') . '.csv';

        // 设置CSV头部
        $response = response('', 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);

        // 添加BOM以支持中文
        $csv = "\xEF\xBB\xBF";

        foreach ($data as $row) {
            $csv .= implode(',', array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row)) . "\n";
        }

        return $response->withBody($csv);
    }

    /**
     * 获取产品列表（用于线路权限展示）
     * @param Request $request
     * @return \support\Response
     */
    public function getProducts(Request $request)
    {
        try {
            $productIds = $request->get('ids', '');

            if (empty($productIds)) {
                return $this->success([]);
            }

            // 解析产品ID列表
            $ids = array_filter(explode(',', $productIds));
            if (empty($ids)) {
                return $this->success([]);
            }

            // 获取产品信息
            $products = \app\model\Products::whereIn('id', $ids)
                ->field('id,product_name,third_product_id')
                ->select();

            // 转换为键值对格式
            $productMap = [];
            foreach ($products as $product) {
                $productMap[$product->id] = [
                    'id' => $product->id,
                    'name' => $product->product_name,
                    'third_product_id' => $product->third_product_id
                ];
            }

            return $this->success($productMap);

        } catch (\Exception $e) {
            Log::error('获取产品信息失败: ' . $e->getMessage());
            return $this->error(500, '获取产品信息失败');
        }
    }
}
