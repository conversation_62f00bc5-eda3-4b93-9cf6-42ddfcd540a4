<?php

namespace app\admin\controller;

use app\model\Admins;
use support\Log;
use support\Redis;
use think\db\exception\DbException;

class WatchController extends base
{
    public static function watchIsWork()
    {
        $nowTime = time();
        $datas = Admins::order('create_time desc')->select();
        foreach ($datas as $data) {
            $item = [];

            //set admin/index/isWork
            $watch = Redis::get('CRM:USER:ONLINE:WATCH:'.$data->id);

            //记录下线时间
            if (!empty($watch) && ($nowTime - $watch) > 6*60){
                $item['end_work_time'] = $watch + 5*60;
            }
            //实时更新在线时间
            if (($nowTime - $watch) <= 6*60){
                $item['last_work_time'] = $nowTime;
            }

            //记录当天第一次上线时间 start_work_time  admin/login/index

            if (!empty($item)){
                try {
                    Admins::where('id', $data->id)->update($item);
                } catch (DbException $e) {
                    Log::error('watchIsWork',['error'=>$e]);
                    continue;
                }
            }
        }
        return;
    }
}