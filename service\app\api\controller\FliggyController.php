<?php
declare(strict_types=1);

namespace app\api\controller;

use app\model\FliggyOrders;
use app\model\FliggyOrderLogs;
use app\server\FliggySignatureService;
use support\Request;
use support\Response;
use support\Log;
use Webman\App;

/**
 * 飞猪回调控制器
 */
final class FliggyController extends base
{
    public function __construct() {
        $request = App::request();
        Log::info('FliggyController:' . json_encode($request->all()) . ',headers:'.json_encode($request->header()));
    }

    /**
     * 预创建订单
     */
    public function preCreateOrder(Request $request): Response {
        try {
            Log::info('preCreateOrder:' . json_encode($request->all()));

            return $this->jsonResponse(
                FliggySignatureService::buildResponse(true, 'ok', 'Processing successful')
            );

        } catch (\Exception $e) {
            return $this->jsonResponse(
                FliggySignatureService::buildResponse(false, 'SYSTEM_ERROR', 'System error occurred')
            );
        }
    }

    /**
     * 预创建订单
     */
    public function cancelOrder(Request $request): Response {
        try {
            Log::info('cancelOrder:' . json_encode($request->all()));

            return $this->jsonResponse(
                FliggySignatureService::buildResponse(true, 'ok', 'Processing successful')
            );

        } catch (\Exception $e) {
            return $this->jsonResponse(
                FliggySignatureService::buildResponse(false, 'SYSTEM_ERROR', 'System error occurred')
            );
        }
    }

    /**
     * 创建订单
     */
    public function createOrder(Request $request): Response
    {
        try {
            $body = $request->rawBody();
            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'PARAMS_ERROR', 'Invalid JSON format')
                );
            }

            // 验证签名
            if (!$this->verifySignature($request, $body)) {
//                return $this->jsonResponse(
//                    FliggySignatureService::buildResponse(false, 'SIGNATURE_ERROR', 'Signature verification failed')
//                );
            }

            // 验证必填字段
            $validationResult = $this->validateCreateOrderData($data);
            if (!$validationResult['success']) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'PARAMS_ERROR', $validationResult['message'])
                );
            }

            // 检查订单是否已存在
            $existingOrder = FliggyOrders::findByFliggyOrderId($data['fliggyOrderId']);
            if ($existingOrder) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'ORDER_EXISTS', 'Order already exists')
                );
            }

            // 创建订单
            $order = FliggyOrders::createOrder($data);

            // 生成内部订单号
            $internalOrderId = 'FLG' . date('Ymd') . str_pad((string)$order->id, 6, '0', STR_PAD_LEFT);
            $order->order_id = $internalOrderId;
            $order->save();

            Log::info('Fliggy order created successfully', [
                'fliggy_order_id' => $data['fliggyOrderId'],
                'internal_order_id' => $internalOrderId
            ]);

            $responseData = [
                'orderId' => $internalOrderId,
                'fliggyOrderId' => $data['fliggyOrderId']
            ];

            // 记录操作日志
            FliggyOrderLogs::log(
                $data['fliggyOrderId'],
                FliggyOrderLogs::ACTION_CREATE,
                $data,
                $responseData,
                FliggyOrderLogs::STATUS_SUCCESS,
                '',
                $internalOrderId,
                $request->getRealIp(),
                $request->header('user-agent', '')
            );

            return $this->jsonResponse(
                FliggySignatureService::buildResponse(true, 'ok', 'Processing successful', $responseData)
            );

        } catch (\Exception $e) {
            Log::error('Fliggy create order error: ' . $e->getMessage());

            // 记录错误日志
            if (isset($data['fliggyOrderId'])) {
                FliggyOrderLogs::log(
                    $data['fliggyOrderId'],
                    FliggyOrderLogs::ACTION_CREATE,
                    $data ?? [],
                    [],
                    FliggyOrderLogs::STATUS_FAILED,
                    $e->getMessage(),
                    '',
                    $request->getRealIp(),
                    $request->header('user-agent', '')
                );
            }

            return $this->jsonResponse(
                FliggySignatureService::buildResponse(false, 'SYSTEM_ERROR', 'System error occurred')
            );
        }
    }

    /**
     * 订单查询
     */
    public function queryOrder(Request $request): Response
    {
        try {
            $body = $request->rawBody();
            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'PARAMS_ERROR', 'Invalid JSON format')
                );
            }

            // 验证签名
            if (!$this->verifySignature($request, $body)) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'SIGNATURE_ERROR', 'Signature verification failed')
                );
            }

            // 验证必填字段
            if (empty($data['fliggyOrderId'])) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'PARAMS_ERROR', 'Missing fliggyOrderId')
                );
            }

            // 查找订单
            $order = FliggyOrders::findByFliggyOrderId($data['fliggyOrderId']);
            if (!$order) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'ORDER_NOT_FOUND', 'Order not found')
                );
            }

            // 构建订单信息
            $orderData = [
                'orderId' => $order->order_id,
                'fliggyOrderId' => $order->fliggy_order_id,
                'status' => $order->status,
                'statusName' => $order->status_name,
                'createTime' => date('Y-m-d H:i:s', $order->create_at),
                'confirmTime' => $order->confirm_time ? date('Y-m-d H:i:s', $order->confirm_time) : null,
                'travellers' => $order->travellers,
                'productList' => $order->product_list,
                'contacts' => $order->contacts
            ];

            Log::info('Fliggy order query successful', [
                'fliggy_order_id' => $data['fliggyOrderId'],
                'order_status' => $order->status
            ]);

            return $this->jsonResponse(
                FliggySignatureService::buildResponse(true, 'ok', 'Query successful', $orderData)
            );

        } catch (\Exception $e) {
            Log::error('Fliggy query order error: ' . $e->getMessage());

            return $this->jsonResponse(
                FliggySignatureService::buildResponse(false, 'SYSTEM_ERROR', 'System error occurred')
            );
        }
    }

    /**
     * 退款处理
     */
    public function refundOrder(Request $request): Response
    {
        try {
            $body = $request->rawBody();
            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'PARAMS_ERROR', 'Invalid JSON format')
                );
            }

            // 验证签名
            if (!$this->verifySignature($request, $body)) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'SIGNATURE_ERROR', 'Signature verification failed')
                );
            }

            // 验证必填字段
            if (empty($data['fliggyOrderId'])) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'PARAMS_ERROR', 'Missing fliggyOrderId')
                );
            }

            // 查找订单
            $order = FliggyOrders::findByFliggyOrderId($data['fliggyOrderId']);
            if (!$order) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'ORDER_NOT_FOUND', 'Order not found')
                );
            }

            // 检查订单状态是否可以退款
            if ($order->status === FliggyOrders::STATUS_REFUNDED) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'ORDER_ALREADY_REFUNDED', 'Order already refunded')
                );
            }

            if ($order->status === FliggyOrders::STATUS_CANCELLED) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'ORDER_CANCELLED', 'Order already cancelled')
                );
            }

            // 处理退款逻辑
            $refundType = $data['refundType'] ?? 1;
            $refundFee = $data['refundFee'] ?? 0;
            $refundReason = $data['refundReason'] ?? '';

            $refundStatus = $this->processRefund($order, $refundType, $refundFee, $refundReason);

            // 更新订单状态
            if ($refundStatus === FliggyOrders::REFUND_STATUS_SUCCESS) {
                $order->updateStatus(FliggyOrders::STATUS_REFUNDED);
                $order->refund_status = $refundStatus;
                $order->refund_fee = $refundFee;
                $order->refund_reason = $refundReason;
                $order->refund_time = time();
                $order->save();
            }

            Log::info('Fliggy order refund processed', [
                'fliggy_order_id' => $data['fliggyOrderId'],
                'refund_status' => $refundStatus,
                'refund_fee' => $refundFee
            ]);

            return $this->jsonResponse(
                FliggySignatureService::buildResponse(true, 'ok', 'Refund processed successfully', [
                    'fliggyOrderId' => $data['fliggyOrderId'],
                    'orderId' => $order->order_id,
                    'refundStatus' => $refundStatus
                ])
            );

        } catch (\Exception $e) {
            Log::error('Fliggy refund order error: ' . $e->getMessage());

            return $this->jsonResponse(
                FliggySignatureService::buildResponse(false, 'SYSTEM_ERROR', 'System error occurred')
            );
        }
    }

    /**
     * 修改订单
     */
    public function updateOrder(Request $request): Response
    {
        try {
            $body = $request->rawBody();
            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'PARAMS_ERROR', 'Invalid JSON format')
                );
            }

            // 验证签名
            if (!$this->verifySignature($request, $body)) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'SIGNATURE_ERROR', 'Signature verification failed')
                );
            }

            // 验证必填字段
            if (empty($data['fliggyOrderId'])) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'PARAMS_ERROR', 'Missing fliggyOrderId')
                );
            }

            // 查找订单
            $order = FliggyOrders::findByFliggyOrderId($data['fliggyOrderId']);
            if (!$order) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'ORDER_NOT_FOUND', 'Order not found')
                );
            }

            // 检查订单状态是否可以修改
            if (in_array($order->status, [FliggyOrders::STATUS_CANCELLED, FliggyOrders::STATUS_REFUNDED])) {
                return $this->jsonResponse(
                    FliggySignatureService::buildResponse(false, 'ORDER_CANNOT_UPDATE', 'Order cannot be updated')
                );
            }

            // 更新订单信息
            if (isset($data['travelDate'])) {
                $order->travel_date = $data['travelDate'];
            }

            if (isset($data['travellers'])) {
                $order->travellers = $data['travellers'];
            }

            if (isset($data['orderInfo'])) {
                $order->order_info = $data['orderInfo'];
            }

            $order->update_at = time();
            $order->save();

            Log::info('Fliggy order updated successfully', [
                'fliggy_order_id' => $data['fliggyOrderId']
            ]);

            return $this->jsonResponse(
                FliggySignatureService::buildResponse(true, 'ok', 'Order updated successfully')
            );

        } catch (\Exception $e) {
            Log::error('Fliggy update order error: ' . $e->getMessage());

            return $this->jsonResponse(
                FliggySignatureService::buildResponse(false, 'SYSTEM_ERROR', 'System error occurred')
            );
        }
    }

    private function verifySignature(Request $request, string $body): bool
    {
        $headers = $request->header();
        $method = $request->method();
        $uri = $request->uri();
        return FliggySignatureService::verifySignature($headers, $body, $method, $uri);
    }

    private function validateCreateOrderData(array $data): array
    {
        $requiredFields = ['bizType', 'fliggyOrderId', 'vendor', 'travellers', 'productList'];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return ['success' => false, 'message' => "Missing required field: {$field}"];
            }
        }

        if ($data['bizType'] !== 2) {
            return ['success' => false, 'message' => 'Invalid bizType, must be 2 for travel business'];
        }

        if (!is_array($data['travellers']) || empty($data['travellers'])) {
            return ['success' => false, 'message' => 'Travellers must be a non-empty array'];
        }

        if (!is_array($data['productList']) || empty($data['productList'])) {
            return ['success' => false, 'message' => 'ProductList must be a non-empty array'];
        }

        return ['success' => true];
    }

    private function processRefund(FliggyOrders $order, int $refundType, int $refundFee, string $refundReason): int
    {
        try {
            Log::info('Processing refund', [
                'order_id' => $order->order_id,
                'refund_type' => $refundType,
                'refund_fee' => $refundFee,
                'reason' => $refundReason
            ]);

            // 这里可以添加具体的退款逻辑
            // 1. 调用支付系统退款接口
            // 2. 更新库存
            // 3. 发送通知等

            return FliggyOrders::REFUND_STATUS_PROCESSING;

        } catch (\Exception $e) {
            Log::error('Refund processing failed: ' . $e->getMessage());
            return FliggyOrders::REFUND_STATUS_FAILED;
        }
    }

    private function jsonResponse(array $data): Response
    {
        return new Response(200, ['Content-Type' => 'application/json'], json_encode($data, JSON_UNESCAPED_UNICODE));
    }
}
