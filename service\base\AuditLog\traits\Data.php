<?php

namespace base\AuditLog\traits;

trait Data
{
    /**
     * 数据差异
     *
     * @var array
     */
    private array $diffData = [];
    private array $onlyArr  = [];
    private       $diffInfo;

    public function newData(array $data): self
    {
        $this->context['new'] = $data;

        return $this;
    }

    public function oldData(array $data): self
    {
        $this->context['old'] = $data;

        return $this;
    }

    /**
     * 解析字段标识
     * @return void
     */
    private function initData(): void
    {
        $this->initField();
        /** 处理数据差异 */
        $this->getDiff();
        $this->getDiffInfo();
    }

    private function getDiff(): array
    {
        $new = $this->context['new'] ?? [];
        $old = $this->context['old'] ?? [];

        return $this->diffData = $this->diff(
            $new,
            $old
        );
    }

    private function getDiffInfo(): void
    {
        $new     = $this->diffData['new'] ?? [];
        $removed = $this->diffData['removed'] ?? [];
        $edited  = $this->diffData['edited'] ?? [];

        $diff_info = array_merge(
            $this->newOrRemovedInfo($new),
            $this->newOrRemovedInfo($removed, 'delete'),
            $this->editedInfo($edited)
        );

        $this->diffInfo = $diff_info;
    }

    private function newOrRemovedInfo($data, $mode = 'add'): array
    {
        $diff_info = [];
        foreach ($data as $key => $value) {
            $alias = $this->getAlias($key);
            if ($alias) {
                $value = $this->getParse($alias, $value);
                $title = $this->getTitle($alias);
                $info = [
                    'mode' => $mode,
                    'title' => $title,
                    'value' => $value,
                ];
                if (isset($this->indexList[$key])) {
                    $diff_info[$this->getTitle(
                        $this->indexList2[$key]
                    ) . $this->config->get('highlight.array_index_splitter', '::') . $this->indexList[$key]][] = $info;
                } else {
                    $diff_info[] = $info;
                }
            }
        }

        return $diff_info;
    }

    private function editedInfo($data): array
    {
        $diff_info = [];
        foreach ($data as $key => $value) {
            $alias = $this->getAlias($key);
            if ($alias) {
                $title     = $this->getTitle($alias);
                $old_value = $this->getParse($alias, $value['oldValue']);
                $new_value = $this->getParse($alias, $value['newValue']);

                $str = [
                    'mode' => 'update',
                    'title' => $title,
                    'oldValue' => $old_value,
                    'newValue' => $new_value,
                ];
                if (isset($this->indexList[$key])) {
                    $diff_info[$this->getTitle(
                        $this->indexList2[$key]
                    ) . '::' . $this->indexList[$key]][] = $str;
                } else {
                    $diff_info[] = $str;
                }
            }
        }

        return $diff_info;
    }

    private function descInfo(): array
    {
        $list = [];
        foreach ($this->desc as $value) {
            $list[] = $this->parse($value);
        }

        return $list;
    }

}
