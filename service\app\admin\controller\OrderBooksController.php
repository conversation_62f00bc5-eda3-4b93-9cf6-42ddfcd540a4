<?php
namespace app\admin\controller;
require_once(__DIR__.'/xlsxwriter.class.php');

use app\model\OrderBooks;
use app\model\Orders;
use app\model\Products;
use support\Request;

class OrderBooksController extends base {
    /**
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DbException
     */
    public function list(Request $request) {
        $where = [];
        if($request->get('sn')) {
            $order = Orders::where('sn', $request->get('sn'))->find();
            if ($order) {
                $where['order_id'] = $order->id;
            } else {
                $where['order_id'] = '-1';
            }
        }
        $query = OrderBooks::where($where)->order('id', 'desc');

        $list = $query->with(['orderInfo'])->paginate($request->get('limit',10));
        foreach ($list as &$val) {
            if ($val['code_pic']) {
                $val['code_pic'] = json_decode($val['code_pic'], true);
                $val['code_pic_show'] = $val['code_pic'][0];
            }
        }
        return $this->success($list,null,['oss' => array_values(array_map(function ($os, $k) {
            return ['id' => $k, 'os' => $os];
        }, Orders::OSS, array_keys(Orders::OSS)))]);
    }

    /**
     * @param Request $request
     * @return \support\Response
     * @throws \think\db\exception\DbException
     */
    public function updateStatus(Request $request) {
        if (!$request->post('id')) {
            return $this->error('请选择预约记录');
        }
        OrderBooks::where('id', $request->post('id'))->update(['status' => 1]);
        return $this->success([]);
    }
}