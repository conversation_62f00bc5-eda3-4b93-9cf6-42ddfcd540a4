<?php
namespace app\model;

use think\facade\Db;

class BacksXs extends base
{
    protected $table = 'backs';

    public function into() {
        return $this->belongsTo(Admins::class, 'admin_id')->visible(['name','username','avatar']);
    }

    public function outto() {
        return $this->belongsTo(Admins::class, 'admin')->visible(['name','username','avatar']);
    }

    public function apply() {
        return $this->belongsTo(Admins::class, 'apply_id')->visible(['name','username','avatar']);
    }

    public function orders(){
        return $this->belongsTo(OrdersXs::class, 'order_id');
    }

    public static function change(BacksXs $item) {
        Db::transaction(function() use ($item) {
            BacksXs::where('id', $item->id)->where('status', 0)->update(['status' => 1]);
            OrdersXs::where('id', $item->order_id)->where('admin_id', $item->admin)->update(['admin_id'=> $item->admin_id]);
            Logs::todo($item->order_id, $item->admin, 8); //转单
            $other = BacksXs::where('order_id', $item->order_id)->where('status', 0)->lock()->select();
            foreach($other as $o) {
                BacksXs::where('id', $o->id)->where('status', 0)->update(['status' => 3]);
                LogsXs::todo($o->order_id, $o->admin, 10); //取消其他转单需求
            }
        });
    }

    public static function refuse(BacksXs $item) {
        Db::transaction(function() use ($item) {
            BacksXs::where('id', $item->id)->where('status', 0)->update(['status' => 2]);
            LogsXs::todo($item->order_id, $item->admin, 9); //拒绝请求
        });
    }

    public static function cancel(Backs $item) {
        Db::transaction(function() use ($item) {
            BacksXs::where('id', $item->id)->where('status', 0)->update(['status' => 3]);
            LogsXs::todo($item->order_id, $item->admin, 10); //拒绝请求
        });
    }
}