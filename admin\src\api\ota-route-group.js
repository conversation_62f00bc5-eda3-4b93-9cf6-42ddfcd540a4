import request from '@/utils/request'

// 获取OTA线路分组列表
export function fetchOtaRouteGroupList(params) {
  return request({
    url: '/admin/ota-route-group/index',
    method: 'get',
    params
  })
}

// 获取OTA线路分组详情
export function fetchOtaRouteGroupDetail(params) {
  return request({
    url: '/admin/ota-route-group/show',
    method: 'get',
    params
  })
}

// 保存OTA线路分组
export function saveOtaRouteGroup(data) {
  return request({
    url: '/admin/ota-route-group/save',
    method: 'post',
    data
  })
}

// 删除OTA线路分组
export function deleteOtaRouteGroup(data) {
  return request({
    url: '/admin/ota-route-group/delete',
    method: 'post',
    data
  })
}

// 更新OTA线路分组状态
export function updateOtaRouteGroupStatus(data) {
  return request({
    url: '/admin/ota-route-group/updateStatus',
    method: 'post',
    data
  })
}

// 获取启用的线路组列表
export function fetchActiveOtaRouteGroups() {
  return request({
    url: '/admin/ota-route-group/getActiveGroups',
    method: 'get'
  })
}

// 获取可选择的产品列表
export function fetchOtaRouteGroupProducts() {
  return request({
    url: '/admin/ota-route-group/getProducts',
    method: 'get'
  })
}

// 批量更新排序
export function updateOtaRouteGroupSort(data) {
  return request({
    url: '/admin/ota-route-group/updateSort',
    method: 'post',
    data
  })
}

// 获取操作日志
export function fetchOtaRouteGroupLogs(params) {
  return request({
    url: '/admin/ota-route-group/logs',
    method: 'get',
    params
  })
} 