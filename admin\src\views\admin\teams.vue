<template>
  <div class="app-container">

    <div class="filter-container">
      <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="append">
        添加
      </el-button>
    </div>

    <div class="teams">

      <el-tree
        :data="data"
        node-key="id"
        default-expand-all
        @node-drag-start="handleDragStart"
        @node-drag-enter="handleDragEnter"
        @node-drag-leave="handleDragLeave"
        @node-drag-over="handleDragOver"
        @node-drag-end="handleDragEnd"
        @node-drop="handleDrop"
        draggable
        :allow-drop="allowDrop"
        :allow-drag="allowDrag">

        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span>{{ node.label }}</span>
          <span>
            <el-button
              type="text"
              size="mini"
              @click.stop="() => append(data)">
              添加
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="() => edit(data)">
              修改
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click.stop="() => remove(node, data)">
              删除
            </el-button>
          </span>
        </span>

      </el-tree>

    </div>

    <el-dialog title="编辑" :visible.sync="showDailog">
      <el-form label-width="120px">

        <el-form-item label="名称">
          <el-input v-model="item.name" name="name" placeholder="团队名称"></el-input>
        </el-form-item>

        <el-form-item label="成员">
          <el-input v-model="item.name" name="name" placeholder="团队名称"></el-input>
        </el-form-item>

      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-loading="loading" @click="onSave">保 存</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  let id = 1000;
  export default {
    data() {
      return {
        listQuery: {},
        data: [],
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        showDailog: false,
        loading: false,
        item: {},
        pd: {}
      };
    },
    created(){
      this.$axios.get('/admin/team/index').then(res=>{
        this.data = res.data
      })
    },
    methods: {
      handleDragStart(node, ev) {
        console.log('drag start', node);
      },
      handleDragEnter(draggingNode, dropNode, ev) {
        console.log('tree drag enter: ', dropNode);
      },
      handleDragLeave(draggingNode, dropNode, ev) {
        console.log('tree drag leave: ', dropNode);
      },
      handleDragOver(draggingNode, dropNode, ev) {
        // console.log('tree drag over: ', dropNode);
      },
      handleDragEnd(draggingNode, dropNode, dropType, ev) {
        console.log('tree drag end: ', dropNode && dropNode.label, dropType, ev);
      },
      handleDrop(draggingNode, dropNode, dropType, ev) {
        console.log('draggingNode: ', draggingNode.data);

        this.$axios.post('/admin/team/move', {id: draggingNode.data.id, type: dropType, pid: dropNode.data.id})

        console.log('dropType: ', dropType);

        console.log('tree drop: ', dropNode);
      },
      allowDrop(draggingNode, dropNode, type) {
        // if (dropNode.data.label === '二级 3-1') {
        //   return type !== 'inner';
        // } else {
          return true;
        // }
      },
      allowDrag(draggingNode) {
        return true
      },
      onSave() {
        if(!this.item.pid) {
          this.item.pid = this.pd.id || 0;
        }
        this.$axios.post('/admin/team/save', this.item).then(res=>{
          if(this.pd) {
            if (!this.pd.children) {
              this.$set(this.pd, 'children', []);
            }
            this.pd.children.push(res.data);
          }else{
            this.data.push(res.data)
          }
          this.item.label = this.item.name
          this.showDailog = false
        })
      },
      edit(data) {
        this.item = data;
        this.showDailog = true;
      },
      append(data) {
        this.pd = data;
        this.showDailog = true;
      },
      remove(node, data) {
        this.$axios.post('/admin/team/del',{id:data.id});
        const parent = node.parent;
        const children = parent.data.children || parent.data;
        const index = children.findIndex(d => d.id === data.id);
        children.splice(index, 1);
      },
    }
  };
</script>
<style>
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
</style>