import request from '@/utils/request'

// 获取订单分配记录列表
export function fetchOrderAllocateList(params) {
  return request({
    url: '/admin/order-allocate-log/index',
    method: 'get',
    params
  })
}

// 获取订单分配统计数据
export function fetchOrderAllocateStatistics(params) {
  return request({
    url: '/admin/order-allocate-log/statistics',
    method: 'get',
    params
  })
}

// 获取订单分配记录详情
export function fetchOrderAllocateDetail(params) {
  return request({
    url: '/admin/order-allocate-log/show',
    method: 'get',
    params
  })
}

// 导出订单分配记录
export function exportOrderAllocateData(params) {
  return request({
    url: '/admin/order-allocate-log/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取产品信息（用于线路权限展示）
export function fetchProductsByIds(ids) {
  return request({
    url: '/admin/order-allocate-log/getProducts',
    method: 'get',
    params: { ids: ids }
  })
} 