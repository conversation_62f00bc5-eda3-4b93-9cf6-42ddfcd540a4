<?php
namespace app\user\controller;

use Firebase\JWT\JWT;
use Webman\Http\Request;

class IndexController extends base
{
    public function index(Request $request)
    {
        $uid = $request->user->id;

    }

    public function login(Request $request) {
        $time = time();
        $data = [
            'id'=>  1,
            'username'=> 'Tom'
        ];
        
        $payload = array(
            // "iss" => "http://127.0.0.1:8000",  // issuer
            // "aud" => "http://example.com",  // audience
            "iat" => $time,  // token 的创建时间
            "nbf" => $time,  // token 的生效时间
            "exp" => $time+3600*24,  // token 的过期时间
            "data"=> $data  // 携带数据
        );
        
        $keyId = "keyId";
        $token = JWT::encode($payload, config('app.jwt_key'), 'HS256', $keyId);

        return $this->success($token);
    }

    public function forget(Request $request) {

    }

    public function sms(Request $request) {

    }
    
}
